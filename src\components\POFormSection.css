.section-header {
  margin-bottom: 1.5rem;
}

.section-title {
  margin: 0 0 0.5rem 0;
  color: #272D4F;
  font-weight: 600;
}

.section-subtitle {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.table-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.professional-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.table-header {
  background: #272D4F;
  color: white;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  border: none;
}

.table-row:hover {
  background-color: #f8f9fa;
}

.table-cell {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: top;
}

.table-label {
  background-color: #f8f9fa;
  font-weight: 500;
  width: 30%;
}

.label-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label-description {
  font-size: 0.8rem;
  color: #666;
  font-weight: normal;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.professional-select {
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s;
}

.professional-select:focus {
  outline: none;
  border-color: #272D4F;
  box-shadow: 0 0 0 3px rgba(39, 45, 79, 0.1);
}

.field-hint {
  font-size: 0.8rem;
  color: #666;
  font-style: italic;
}