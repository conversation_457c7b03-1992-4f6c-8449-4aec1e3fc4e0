import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import APIEndpoint from "../../config";
import {
  Card,
  Typography,
  Button,
  Box,
  Grid,
  Paper,
  LinearProgress,
  Alert,
  Snackbar,
} from "@mui/material";
import SettingsIcon from "@mui/icons-material/Settings";
import CloudIcon from "@mui/icons-material/Cloud";
import ActionButtons from "./ActionButtons";
const NEOFormSection = ({ formData, setFormData, serviceInterfaces }) => {
  const isConfigureEnabled =
    formData.NeoadapterName &&
    (formData.NeoadapterName === "SFTP" ||
      formData.NeoadapterName === "IDOC" ||
      formData.NeoadapterName === "HTTPS" ||
      formData.NeoadapterName === "SOAP");

  const navigate = useNavigate();
  const [packages, setPackages] = useState([]);
  const [iFlows, setIFlows] = useState([]);
  const [loadingPackages, setLoadingPackages] = useState(false);
  const [loadingIFlows, setLoadingIFlows] = useState(false);
  const [isDeploying, setIsDeploying] = useState(false);
  const [deployMessage, setDeployMessage] = useState(null);
  const [showMessage, setShowMessage] = useState(false);
  // Search functionality states
  const [packageSearchTerm, setPackageSearchTerm] = useState("");
  const [showPackageDropdown, setShowPackageDropdown] = useState(false);
  const [filteredPackages, setFilteredPackages] = useState([]);
  const packageDropdownRef = useRef(null);

  // Function to convert tenant value for API (DEV -> Dev, QA -> Qa)
  const getApiTenantName = (tenant) => {
    if (!tenant) return tenant;
    return tenant.charAt(0).toUpperCase() + tenant.slice(1).toLowerCase();
  };
  const handleDeploy = async () => {
    if (!formData.iFlowName || !formData.tenant) {
      setDeployMessage({
        text: "Please select both I-Flow Name and Tenant before deploying",
        severity: "error",
      });
      setShowMessage(true);
      return;
    }

    setIsDeploying(true);
    setDeployMessage(null);

    try {
      const apiTenantName =
        formData.tenant.charAt(0).toUpperCase() +
        formData.tenant.slice(1).toLowerCase();
      const response = await fetch(
        `${APIEndpoint}/api/deploy?artifactId=${encodeURIComponent(
          formData.iFlowName
        )}&configName=${encodeURIComponent(apiTenantName)}`,
        {
          method: "POST",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to initiate deployment");
      }

      const data = await response.json();
      setDeployMessage({
        text: "Deployment started successfully! Your artifact is being processed.",
        severity: "success",
      });
      setShowMessage(true);
    } catch (error) {
      console.error("Deployment error:", error);
      setDeployMessage({
        text: "Deployment failed. Please try again.",
        severity: "error",
      });
      setShowMessage(true);
    } finally {
      setIsDeploying(false);
    }
  };
  const onConfigure = () => {
    if (!formData.iFlowName) {
      setDeployMessage({
        text: "Please select an I-Flow Name before configuring",
        severity: "error",
      });
      setShowMessage(true);
      return;
    }
    if (formData.NeoadapterName === "SFTP") {
      setShowConfigureModal(true);
    } else if (formData.NeoadapterName === "IDOC") {
      setShowIDOCModal(true);
    } else if (formData.NeoadapterName === "HTTPS") {
      setShowHTTPModal(true);
    } else if (formData.NeoadapterName === "SOAP") {
      setShowSOAPModal(true);
    }
  };
  // Filter packages based on search term
  useEffect(() => {
    if (packageSearchTerm) {
      const filtered = packages.filter((pkg) =>
        pkg.name.toLowerCase().includes(packageSearchTerm.toLowerCase())
      );
      setFilteredPackages(filtered);
    } else {
      setFilteredPackages(packages);
    }
  }, [packageSearchTerm, packages]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        packageDropdownRef.current &&
        !packageDropdownRef.current.contains(event.target)
      ) {
        setShowPackageDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Fetch packages when tenant changes
  useEffect(() => {
    const fetchPackages = async () => {
      if (!formData.tenantNeo) return;

      setLoadingPackages(true);
      try {
        const apiTenantName = getApiTenantName(formData.tenantNeo);
        const response = await fetch(
          `${APIEndpoint}/api/getneopackage?configName=${apiTenantName}`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch packages");
        }

        const data = await response.json();
        console.log("Raw API response:", data);

        // Extract package data from the response structure
        const packageKeys = Object.keys(data || {});

        // Create packages array with packageId for both display and value
        const packagesArray = packageKeys.map((key) => ({
          id: key,
          packageId: data[key].packageId,
          name: data[key].packageId,
        }));

        console.log("Processed packages:", packagesArray);
        setPackages(packagesArray);

        // Reset package and IFlow selections when tenant changes
        setFormData((prev) => ({
          ...prev,
          packageNameNeo: "",
          iFlowNameNeo: "",
        }));
        setIFlows([]);
        setPackageSearchTerm("");
      } catch (error) {
        console.error("Error fetching packages:", error);
      } finally {
        setLoadingPackages(false);
      }
    };

    fetchPackages();
  }, [formData.tenantNeo, setFormData]);
  
  // Fetch IFlows when package changes
  useEffect(() => {
    const fetchIFlows = async () => {
      if (!formData.packageNameNeo || !formData.tenantNeo) return;

      setLoadingIFlows(true);
      try {
        const apiTenantName = getApiTenantName(formData.tenantNeo);
        const response = await fetch(
          `${APIEndpoint}/api/getArticfactsbypackage?packageName=${formData.packageNameNeo}&configName=${apiTenantName}`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch IFlows");
        }

        const data = await response.json();
        console.log("IFlow API response:", data);

        // Extract IFlow data from the response structure
        const iFlowNames = Object.keys(data).map((key) => ({
          id: data[key].artifactId, // Using artifactId as the unique identifier
          name: data[key].artifactName,
        }));

        setIFlows(iFlowNames);

        // Reset IFlow selection when package changes
        setFormData((prev) => ({
          ...prev,
          iFlowNameNeo: "",
        }));
      } catch (error) {
        console.error("Error fetching IFlows:", error);
      } finally {
        setLoadingIFlows(false);
      }
    };

    fetchIFlows();
  }, [formData.packageNameNeo, formData.tenantNeo, setFormData]);

  const handlePackageSelect = (packageName) => {
    setFormData((prev) => ({
      ...prev,
      packageNameNeo: packageName,
      iFlowNameNeo: "",
    }));
    setPackageSearchTerm(packageName);
    setShowPackageDropdown(false);
  };

  const handlePackageInputChange = (e) => {
    const value = e.target.value;
    setPackageSearchTerm(value);
    setShowPackageDropdown(true);

    if (!value) {
      setFormData((prev) => ({
        ...prev,
        packageNameNeo: "",
        iFlowNameNeo: "",
      }));
    }
  };

  const handleIFlowChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      iFlowNameNeo: e.target.value,
    }));
  };

  const handleAdapterChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      NeoadapterName: e.target.value,
    }));
  };

  return (
    <div className="testcase-section">
      <h5>Please mention the required details from NEO Monitoring</h5>
      <table>
        <thead>
          <tr style={{ backgroundColor: "#272D4F", color: "white" }}>
            <th
              style={{
                backgroundColor: "#272D4F",
                padding: "10px",
                border: "1px solid #ccc",
                textAlign: "left",
              }}
            >
              SAP NEO
            </th>
            <th
              style={{
                backgroundColor: "#272D4F",
                padding: "10px",
                border: "1px solid #ccc",
                textAlign: "left",
              }}
            >
              Enter details
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Tenant</td>
            <td>
              <select
                value={formData.tenantNeo}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    tenantNeo: e.target.value,
                    packageNameNeo: "",
                    iFlowNameNeo: "",
                  }))
                }
                required
              >
                <option value="">Select Tenant</option>
                <option value="Dev">DEV</option>
                <option value="QA">QA</option>
              </select>
            </td>
          </tr>
          <tr>
            <td>Package Name</td>
            <td>
              <div style={{ position: "relative" }} ref={packageDropdownRef}>
                <input
                  type="text"
                  value={packageSearchTerm}
                  onChange={handlePackageInputChange}
                  onFocus={() => setShowPackageDropdown(true)}
                  placeholder={
                    loadingPackages
                      ? "Loading packages..."
                      : "Search and select package"
                  }
                  disabled={loadingPackages || !formData.tenantNeo}
                  required
                  style={{
                    width: "100%",
                    padding: "8px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                    fontSize: "14px",
                  }}
                />
                {showPackageDropdown &&
                  !loadingPackages &&
                  filteredPackages.length > 0 && (
                    <div
                      style={{
                        position: "absolute",
                        top: "100%",
                        left: 0,
                        right: 0,
                        backgroundColor: "white",
                        border: "1px solid #ccc",
                        borderTop: "none",
                        maxHeight: "200px",
                        overflowY: "auto",
                        zIndex: 1000,
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                      }}
                    >
                      {filteredPackages.map((pkg) => (
                        <div
                          key={pkg.id}
                          onClick={() => handlePackageSelect(pkg.name)}
                          style={{
                            padding: "8px 12px",
                            cursor: "pointer",
                            borderBottom: "1px solid #eee",
                            backgroundColor: "white",
                            fontSize: "14px",
                          }}
                          onMouseEnter={(e) =>
                            (e.target.style.backgroundColor = "#f5f5f5")
                          }
                          onMouseLeave={(e) =>
                            (e.target.style.backgroundColor = "white")
                          }
                        >
                          {pkg.name}
                        </div>
                      ))}
                    </div>
                  )}
                {showPackageDropdown &&
                  !loadingPackages &&
                  packageSearchTerm &&
                  filteredPackages.length === 0 && (
                    <div
                      style={{
                        position: "absolute",
                        top: "100%",
                        left: 0,
                        right: 0,
                        backgroundColor: "white",
                        border: "1px solid #ccc",
                        borderTop: "none",
                        padding: "8px 12px",
                        color: "#666",
                        fontSize: "14px",
                        zIndex: 1000,
                      }}
                    >
                      No packages found
                    </div>
                  )}
              </div>
            </td>
          </tr>
          <tr>
            <td>I-Flow Name</td>
            <td>
              <select
                value={formData.iFlowNameNeo}
                onChange={handleIFlowChange}
                required
                disabled={loadingIFlows || !formData.packageNameNeo}
              >
                <option value="">
                  {loadingIFlows ? "Loading IFlows..." : "Select IFlow"}
                </option>
                {iFlows.map((flow) => (
                  <option key={flow.id} value={flow.name}>
                    {flow.name}
                  </option>
                ))}
              </select>
            </td>
          </tr>
          <tr>
            <td>Sender Adapter</td>
            <td>
              <select
                value={formData.NeoadapterName}
                onChange={handleAdapterChange}
                required
              >
                <option value="">Select Adapter</option>
                <option value="AS2">AS2</option>
                <option value="HTTPS">HTTPS</option>
                <option value="IDOC">IDOC</option>
                <option value="SFTP">SFTP</option>
                <option value="SOAP">SOAP</option>
                <option value="JMS">JMS</option>
                <option value="Mail">MAIL</option>
                <option value="PROCESS_DIRECT">PROCESS DIRECT</option>
              </select>
            </td>
          </tr>
        </tbody>
      </table>
      <Box sx={{ display: "flex", gap: 2, mt: "50px" }}>
        <ActionButtons
          onConfigure={onConfigure}
          onDeploy={handleDeploy}
          isConfigureEnabled={isConfigureEnabled}
          isDeploying={isDeploying}
        />
      </Box>
    </div>
  );
};

export default NEOFormSection;
