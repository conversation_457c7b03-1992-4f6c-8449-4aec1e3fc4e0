// import React, { useState, useEffect } from "react";
// import { useNavigate } from "react-router-dom";
// import CPIFormSection from "../CPIFormSection";
// import TriggerFlowModal from "../TriggerFlowModal";
// import ConfigureSchedulerModal from "../ConfigureSchedulerModal";
// import "../../App.css";
// import APIEndpoint from "../../config";
// import NEOFormSection from "./NEOFormSection";
// import ConfigureIDOC from "../ConfigureIDOC";
// import ConfigureHTTP from "../ConfigureHTTP";
// import ConfigureSOAP from "../ConfigureSOAP";
// import {
//   Card,
//   Typography,
//   Button,
//   Box,
//   Grid,
//   Paper,
//   LinearProgress,
//   Alert,
//   Snackbar,
// } from "@mui/material";
// import CloudUploadIcon from "@mui/icons-material/CloudUpload";
// import ArrowBackIcon from "@mui/icons-material/ArrowBack";
// import SettingsIcon from "@mui/icons-material/Settings";
// import CloudIcon from "@mui/icons-material/Cloud";
// import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline";
// import AssessmentIcon from "@mui/icons-material/Assessment";
// import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
// import CPINeoFormSection from "./CPINeoFormSection";

// const NeoMultipleTestCaseForm = ({
//   formData,
//   setFormData,
//   onBack,
//   onProceed,
//   onGenerateReport,
//   serviceInterfaces,
// }) => {
//   const navigate = useNavigate();
//   const [deployMessage, setDeployMessage] = useState(null);
//   const [isDeploying, setIsDeploying] = useState(false);
//   const [showMessage, setShowMessage] = useState(false);
//   const [showTriggerModal, setShowTriggerModal] = useState(false);
//   const [showConfigureModal, setShowConfigureModal] = useState(false);
//   const [showIDOCModal, setShowIDOCModal] = useState(false);
//   const [showHTTPModal, setShowHTTPModal] = useState(false);
//   const [showSOAPModal, setShowSOAPModal] = useState(false);
//   const [isProceeding, setIsProceeding] = useState(false);
//   const [isGeneratingReport, setIsGeneratingReport] = useState(false);

//   const isConfigureEnabled =
//     formData.adapterName &&
//     (formData.adapterName === "SFTP" ||
//       formData.adapterName === "IDOC" ||
//       formData.adapterName === "HTTPS" ||
//       formData.adapterName === "SOAP");

//   const handleProceedClick = async () => {
//     setIsProceeding(true);
//     try {
//       await onProceed();
//     } finally {
//       setIsProceeding(false);
//     }
//   };

//   const handleGenerateReportClick = async () => {
//     setIsGeneratingReport(true);
//     try {
//       await onGenerateReport();
//     } finally {
//       setIsGeneratingReport(false);
//     }
//   };

//   const handleCloseSnackbar = () => {
//     setShowMessage(false);
//     setDeployMessage(null);
//   };

//   const onConfigure = () => {
//     if (!formData.iFlowName) {
//       setDeployMessage({
//         text: "Please select an I-Flow Name before configuring",
//         severity: "error",
//       });
//       setShowMessage(true);
//       return;
//     }
//     if (formData.adapterName === "SFTP") {
//       setShowConfigureModal(true);
//     } else if (formData.adapterName === "IDOC") {
//       setShowIDOCModal(true);
//     } else if (formData.adapterName === "HTTPS") {
//       setShowHTTPModal(true);
//     } else if (formData.adapterName === "SOAP") {
//       setShowSOAPModal(true);
//     }
//   };

//   const onTriggerFlow = () => {
//     if (!formData.iFlowName) {
//       setDeployMessage({
//         text: "Please select an I-Flow Name before triggering the flow",
//         severity: "error",
//       });
//       setShowMessage(true);
//       return;
//     }
//     if (!formData.startTime || !formData.endTime) {
//       setDeployMessage({
//         text: "Please set both Start Time and End Time before triggering the flow",
//         severity: "error",
//       });
//       setShowMessage(true);
//       return;
//     }
//     setShowTriggerModal(true);
//   };

//   const handleDeploy = async () => {
//     if (!formData.iFlowName || !formData.tenant) {
//       setDeployMessage({
//         text: "Please select both I-Flow Name and Tenant before deploying",
//         severity: "error",
//       });
//       setShowMessage(true);
//       return;
//     }

//     setIsDeploying(true);
//     setDeployMessage(null);

//     try {
//       const apiTenantName =
//         formData.tenant.charAt(0).toUpperCase() +
//         formData.tenant.slice(1).toLowerCase();
//       const response = await fetch(
//         `${APIEndpoint}/api/deploy?artifactId=${encodeURIComponent(
//           formData.iFlowName
//         )}&configName=${encodeURIComponent(apiTenantName)}`,
//         {
//           method: "POST",
//         }
//       );

//       if (!response.ok) {
//         throw new Error("Failed to initiate deployment");
//       }

//       const data = await response.json();
//       setDeployMessage({
//         text: "Deployment started successfully! Your artifact is being processed.",
//         severity: "success",
//       });
//       setShowMessage(true);
//     } catch (error) {
//       console.error("Deployment error:", error);
//       setDeployMessage({
//         text: "Deployment failed. Please try again.",
//         severity: "error",
//       });
//       setShowMessage(true);
//     } finally {
//       setIsDeploying(false);
//     }
//   };

//   return (
//     <Box
//       sx={{
//         p: 3,
//         backgroundColor: "#f5f7fa",
//         minHeight: "100vh",
//       }}
//     >
//       <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
//         <Grid container spacing={3}>
//           <Grid item xs={12}>
//             <Card sx={{ p: 1, mb: 1 }}>
//               <Grid container alignItems="center" spacing={2}>
//                 <Grid item xs={12} md={4}>
//                   <Typography
//                     variant="subtitle1"
//                     sx={{
//                       color: "#4a5568",
//                       fontWeight: "500",
//                     }}
//                   >
//                     Upload Input Payload To Be Tested:
//                   </Typography>
//                 </Grid>
//                 <Grid item xs={12} md={8}>
//                   <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
//                     <Button
//                       variant="outlined"
//                       component="label"
//                       startIcon={<CloudUploadIcon />}
//                       sx={{
//                         width: "18rem",
//                         flex: 1,
//                         py: 1.5,
//                         borderColor: "#cbd5e0",
//                         "&:hover": {
//                           borderColor: "#a0aec0",
//                         },
//                       }}
//                     >
//                       Select ZIP File
//                       <input
//                         type="file"
//                         accept=".zip"
//                         hidden
//                         onChange={(event) => {
//                           const file = event.target.files[0];
//                           if (file) {
//                             // Store the actual File object, not the text content
//                             setFormData((prev) => ({
//                               ...prev,
//                               neoInputPayloadzip: file, // Store the File object directly
//                               neoInputPayloadzip: file.name, // Store filename for display
//                             }));
//                           }
//                         }}
//                       />
//                     </Button>

//                     {formData.neoInputPayloadzip && (
//                       <Box
//                         sx={{
//                           display: "flex",
//                           alignItems: "center",
//                           color: "#38a169",                                                       
//                           fontSize: "0.875rem",
//                         }}
//                       >
//                         <CheckCircleOutlineIcon
//                           fontSize="small"
//                           sx={{ mr: 1 }}
//                         />
//                         {formData.neoInputPayloadContent ||
//                           "File uploaded successfully"}
//                       </Box>
//                     )}
//                   </Box>
//                 </Grid>
//               </Grid>
//             </Card>
//           </Grid>
//         </Grid>

//         <Box sx={{ mt: 3, display: "flex", flexDirection: "row", gap: 3 }}>
//           <NEOFormSection formData={formData} setFormData={setFormData} />
//           <CPINeoFormSection formData={formData} setFormData={setFormData} />
//         </Box>

//         <Snackbar
//           open={showMessage}
//           autoHideDuration={6000}
//           onClose={handleCloseSnackbar}
//           anchorOrigin={{ vertical: "top", horizontal: "right" }}
//         >
//           <Alert
//             onClose={handleCloseSnackbar}
//             severity={deployMessage?.severity || "info"}
//             sx={{ width: "100%" }}
//           >
//             {deployMessage?.text}
//           </Alert>
//         </Snackbar>

//         <Box
//           sx={{
//             display: "flex",
//             justifyContent: "space-between",
//             alignItems: "center",
//             mt: 4,
//             pt: 3,
//             borderTop: "1px solid #e2e8f0",
//           }}
//         >
//           <Button
//             variant="outlined"
//             startIcon={<ArrowBackIcon />}
//             onClick={onBack}
//             sx={{
//               px: 3,
//               py: 1.5,
//               color: "#4a5568",
//               borderColor: "#cbd5e0",
//               "&:hover": {
//                 backgroundColor: "#edf2f7",
//                 borderColor: "#a0aec0",
//               },
//             }}
//           >
//             Back to Connection
//           </Button>

//           <Box sx={{ display: "flex", gap: 2 }}>
//             <Button
//               variant="contained"
//               startIcon={<PlayCircleOutlineIcon />}
//               onClick={handleProceedClick}
//               disabled={isProceeding}
//               sx={{
//                 px: 3,
//                 py: 1.5,
//                 backgroundColor: "#3182ce",
//                 "&:hover": {
//                   backgroundColor: "#2c5282",
//                 },
//                 "&:disabled": {
//                   backgroundColor: "#e2e8f0",
//                   color: "#a0aec0",
//                 },
//               }}
//             >
//               {isProceeding ? "Processing..." : "Extract Data"}
//             </Button>

//             <Button
//               variant="contained"
//               startIcon={<AssessmentIcon />}
//               onClick={handleGenerateReportClick}
//               disabled={isGeneratingReport}
//               sx={{
//                 px: 3,
//                 py: 1.5,
//                 backgroundColor: "#38a169",
//                 "&:hover": {
//                   backgroundColor: "#2f855a",
//                 },
//                 "&:disabled": {
//                   backgroundColor: "#e2e8f0",
//                   color: "#a0aec0",
//                 },
//               }}
//             >
//               {isGeneratingReport ? "Generating..." : "Generate Report"}
//             </Button>
//           </Box>
//         </Box>
//       </Paper>

//       <TriggerFlowModal
//         isOpen={showTriggerModal}
//         onClose={() => setShowTriggerModal(false)}
//         iFlowName={formData.iFlowName}
//         startTime={formData.startTime}
//         endTime={formData.endTime}
//       />

//       {formData.adapterName === "SFTP" && (
//         <ConfigureSchedulerModal
//           isOpen={showConfigureModal}
//           onClose={() => setShowConfigureModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}

//       {formData.adapterName === "IDOC" && (
//         <ConfigureIDOC
//           isOpen={showIDOCModal}
//           onClose={() => setShowIDOCModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}

//       {formData.adapterName === "HTTPS" && (
//         <ConfigureHTTP
//           isOpen={showHTTPModal}
//           onClose={() => setShowHTTPModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}

//       {formData.adapterName === "SOAP" && (
//         <ConfigureSOAP
//           isOpen={showSOAPModal}
//           onClose={() => setShowSOAPModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}
//     </Box>
//   );
// };

// export default NeoMultipleTestCaseForm;



import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import CPIFormSection from "../CPIFormSection";
import TriggerFlowModal from "../TriggerFlowModal";
import ConfigureSchedulerModal from "../ConfigureSchedulerModal";
import "../../App.css";
import APIEndpoint from "../../config";
import NEOFormSection from "./NEOFormSection";
import ConfigureIDOC from "../ConfigureIDOC";
import ConfigureHTTP from "../ConfigureHTTP";
import ConfigureSOAP from "../ConfigureSOAP";
import {
  Card,
  Typography,
  Button,
  Box,
  Grid,
  Paper,
  LinearProgress,
  Alert,
  Snackbar,
} from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import SettingsIcon from "@mui/icons-material/Settings";
import CloudIcon from "@mui/icons-material/Cloud";
import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline";
import AssessmentIcon from "@mui/icons-material/Assessment";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CPINeoFormSection from "./CPINeoFormSection";

const NeoMultipleTestCaseForm = ({
  formData,
  setFormData,
  onBack,
  onProceed,
  onGenerateReport,
  serviceInterfaces,
}) => {
  const navigate = useNavigate();
  const [deployMessage, setDeployMessage] = useState(null);
  const [isDeploying, setIsDeploying] = useState(false);
  const [showMessage, setShowMessage] = useState(false);
  const [showTriggerModal, setShowTriggerModal] = useState(false);
  const [showConfigureModal, setShowConfigureModal] = useState(false);
  const [showIDOCModal, setShowIDOCModal] = useState(false);
  const [showHTTPModal, setShowHTTPModal] = useState(false);
  const [showSOAPModal, setShowSOAPModal] = useState(false);
  const [isProceeding, setIsProceeding] = useState(false);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  const isConfigureEnabled =
    formData.adapterName &&
    (formData.adapterName === "SFTP" ||
      formData.adapterName === "IDOC" ||
      formData.adapterName === "HTTPS" ||
      formData.adapterName === "SOAP");

  const handleProceedClick = async () => {
    setIsProceeding(true);
    try {
      await onProceed();
    } finally {
      setIsProceeding(false);
    }
  };

  const handleGenerateReportClick = async () => {
    setIsGeneratingReport(true);
    try {
      await onGenerateReport();
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const handleCloseSnackbar = () => {
    setShowMessage(false);
    setDeployMessage(null);
  };

  // Fixed file change handler for ZIP files
  const handleZipFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      console.log("Selected ZIP file:", file.name, file.type, file.size);
      
      // Update form data with the ZIP file
      setFormData((prev) => ({
        ...prev,
        neoInputPayloadzip: file, // Store the actual File object
        neoInputPayloadContentzip: `${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`, // Store display info
      }));
    }
  };

  const onConfigure = () => {
    if (!formData.iFlowName) {
      setDeployMessage({
        text: "Please select an I-Flow Name before configuring",
        severity: "error",
      });
      setShowMessage(true);
      return;
    }
    if (formData.adapterName === "SFTP") {
      setShowConfigureModal(true);
    } else if (formData.adapterName === "IDOC") {
      setShowIDOCModal(true);
    } else if (formData.adapterName === "HTTPS") {
      setShowHTTPModal(true);
    } else if (formData.adapterName === "SOAP") {
      setShowSOAPModal(true);
    }
  };

  const onTriggerFlow = () => {
    if (!formData.iFlowName) {
      setDeployMessage({
        text: "Please select an I-Flow Name before triggering the flow",
        severity: "error",
      });
      setShowMessage(true);
      return;
    }
    if (!formData.startTime || !formData.endTime) {
      setDeployMessage({
        text: "Please set both Start Time and End Time before triggering the flow",
        severity: "error",
      });
      setShowMessage(true);
      return;
    }
    setShowTriggerModal(true);
  };

  const handleDeploy = async () => {
    if (!formData.iFlowName || !formData.tenant) {
      setDeployMessage({
        text: "Please select both I-Flow Name and Tenant before deploying",
        severity: "error",
      });
      setShowMessage(true);
      return;
    }

    setIsDeploying(true);
    setDeployMessage(null);

    try {
      const apiTenantName =
        formData.tenant.charAt(0).toUpperCase() +
        formData.tenant.slice(1).toLowerCase();
      const response = await fetch(
        `${APIEndpoint}/api/deploy?artifactId=${encodeURIComponent(
          formData.iFlowName
        )}&configName=${encodeURIComponent(apiTenantName)}`,
        {
          method: "POST",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to initiate deployment");
      }

      const data = await response.json();
      setDeployMessage({
        text: "Deployment started successfully! Your artifact is being processed.",
        severity: "success",
      });
      setShowMessage(true);
    } catch (error) {
      console.error("Deployment error:", error);
      setDeployMessage({
        text: "Deployment failed. Please try again.",
        severity: "error",
      });
      setShowMessage(true);
    } finally {
      setIsDeploying(false);
    }
  };

  return (
    <Box
      sx={{
        p: 3,
        backgroundColor: "#f5f7fa",
        minHeight: "100vh",
      }}
    >
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card sx={{ p: 1, mb: 1 }}>
              <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      color: "#4a5568",
                      fontWeight: "500",
                    }}
                  >
                    Upload Input Payload To Be Tested:
                  </Typography>
                </Grid>
                <Grid item xs={12} md={8}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<CloudUploadIcon />}
                      sx={{
                        width: "18rem",
                        flex: 1,
                        py: 1.5,
                        borderColor: "#cbd5e0",
                        "&:hover": {
                          borderColor: "#a0aec0",
                        },
                      }}
                    >
                      Select ZIP File
                      <input
                        type="file"
                        accept=".zip"
                        hidden
                        onChange={handleZipFileChange} // Use the dedicated handler
                      />
                    </Button>

                    {formData.neoInputPayloadzip && (
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          color: "#38a169",
                          fontSize: "0.875rem",
                        }}
                      >
                        <CheckCircleOutlineIcon
                          fontSize="small"
                          sx={{ mr: 1 }}
                        />
                        File uploaded: {formData.neoInputPayloadzip.name || formData.neoInputPayloadContentzip}
                      </Box>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: "flex", flexDirection: "row", gap: 3 }}>
          <NEOFormSection formData={formData} setFormData={setFormData} />
          <CPINeoFormSection formData={formData} setFormData={setFormData} />
        </Box>

        <Snackbar
          open={showMessage}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "top", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={deployMessage?.severity || "info"}
            sx={{ width: "100%" }}
          >
            {deployMessage?.text}
          </Alert>
        </Snackbar>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mt: 4,
            pt: 3,
            borderTop: "1px solid #e2e8f0",
          }}
        >
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={onBack}
            sx={{
              px: 3,
              py: 1.5,
              color: "#4a5568",
              borderColor: "#cbd5e0",
              "&:hover": {
                backgroundColor: "#edf2f7",
                borderColor: "#a0aec0",
              },
            }}
          >
            Back to Connection
          </Button>

          <Box sx={{ display: "flex", gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<PlayCircleOutlineIcon />}
              onClick={handleProceedClick}
              disabled={isProceeding}
              sx={{
                px: 3,
                py: 1.5,
                backgroundColor: "#3182ce",
                "&:hover": {
                  backgroundColor: "#2c5282",
                },
                "&:disabled": {
                  backgroundColor: "#e2e8f0",
                  color: "#a0aec0",
                },
              }}
            >
              {isProceeding ? "Processing..." : "Extract Data"}
            </Button>

            <Button
              variant="contained"
              startIcon={<AssessmentIcon />}
              onClick={handleGenerateReportClick}
              disabled={isGeneratingReport}
              sx={{
                px: 3,
                py: 1.5,
                backgroundColor: "#38a169",
                "&:hover": {
                  backgroundColor: "#2f855a",
                },
                "&:disabled": {
                  backgroundColor: "#e2e8f0",
                  color: "#a0aec0",
                },
              }}
            >
              {isGeneratingReport ? "Generating..." : "Generate Report"}
            </Button>
          </Box>
        </Box>
      </Paper>

      <TriggerFlowModal
        isOpen={showTriggerModal}
        onClose={() => setShowTriggerModal(false)}
        iFlowName={formData.iFlowName}
        startTime={formData.startTime}
        endTime={formData.endTime}
      />

      {formData.adapterName === "SFTP" && (
        <ConfigureSchedulerModal
          isOpen={showConfigureModal}
          onClose={() => setShowConfigureModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "IDOC" && (
        <ConfigureIDOC
          isOpen={showIDOCModal}
          onClose={() => setShowIDOCModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "HTTPS" && (
        <ConfigureHTTP
          isOpen={showHTTPModal}
          onClose={() => setShowHTTPModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "SOAP" && (
        <ConfigureSOAP
          isOpen={showSOAPModal}
          onClose={() => setShowSOAPModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}
    </Box>
  );
};

export default NeoMultipleTestCaseForm;