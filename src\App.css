* {
  box-sizing: border-box;
}
.testcase-section .heading {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.testcase-section table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.testcase-section th {
  background-color:rgb(39, 45, 79);
  font-weight: bold;
  padding: 8px;
  text-align: left;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}


.container{
    display: flex;
    /* height: 85vh; */
    justify-content: center;
    align-items: center;
}
body {
  margin: 0;
  font-family:sans-serif;
  background-color: #f4f6f8;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 30px;
  background-color: #fff;
  border-bottom: 1px solid #ddd;
}

.logo-left {
  font-size: 24px;
  color: #0a66c2;
  font-weight: bold;
}

.logo-right {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #0077cc;
}

.logo-right span {
  margin-right: 10px;
}

.app-icon {
  width: 30px;
  height: 30px;
}
.main-content {
  margin-top: 4rem;
  margin-left: 222px; /* offset to account for fixed sidebar */
  display: flex;
  flex: 1;
  /* overflow-y: auto; */
}



.sidebar {
  position: fixed;
  top: 4rem; /* matches your .main-content margin-top */
  left: 0;
  width: 242px;
  height: calc(100vh - 4rem); /* full height minus header */
  background-color: #f8fbfe;
  padding: 25px 25px 25px 16px;
  border-right: 1px solid #ccc;
  overflow-y: auto; /* allow scroll inside sidebar if needed */
  z-index: 1000; /* ensure it stays on top */
}

.sidebar-btn {
 font-size: 12px;
  display: block;
  width: 100%;
  padding: 12px;
  margin-bottom: 15px;
  background-color:#272D4F;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

.sidebar-btn:hover {
  background-color:rgb(51, 165, 231);
}

.content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  /* padding: 20px; */ 
    padding: 4px 0px 15px 22px;
  text-align: center;
}



.content h2 {
  font-size: 2.2rem; /* ~35px */
  /* text-decoration: underline; */
  color: #333;
  margin-bottom: 1.5rem;
}

.content p {
 font-size: 1.2rem; /* ~21.6px */
  margin-top: 20px;
  color: #333;
  line-height: 1.6;

}

footer {
    font-size: 1rem;
  margin-top: 50px;
  font-size: 14px;
  color: #666;
}

footer a {
  display: block;
  margin-bottom: 10px;
  color: #0a66c2;
  text-decoration: underline;
}


/* Potesting css */

.po-testing-container {
  padding: 20px;
  text-align: center;
}

.testing-title {
  display: flex;
  font-weight: bold;
  margin-bottom: 20px;
}

.button-group {
  display: flex;
  
  gap: 2rem;
 
}
.button-group.shift-up {
  margin-top: 0;
}

.testing-title {
  margin-bottom: 20px;
}

.button-group {
  transition: margin-top 0.3s ease;
}

.scenario-button {
      border-radius: 14px;
  background-color: #272D4F;
  color: white;
  padding: 10px 20px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  width: 15rem;
}
.vertical-line {
  position: relative;
}

.vertical-line::after {
  content: "";
  position: absolute;
  left: 50%; /* Adjust this for positioning */
  top: 0;
  bottom: 0;
  width: 2px; /* Adjust width for thickness */
  background-color: #000; /* Line color */
}


.scenario-button.active {
  background-color:rgb(41, 168, 224);;
  border-bottom: 4px solid #034a84;
}

.single-testcase-content {
  display: flex;
  gap: 50px;
  /* margin-left:4rem; */
  /* margin-top: 30px; */
}
.testcase-section input,
.testcase-section select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.testcase-section {
  text-align: left;
  width: 50%;
}

.testcase-section .heading {
  background-color:#272D4F;
  color: white;
  padding: 8px;
  margin: 0;
}

.testcase-section table {
  border-collapse: collapse;
  width: 100%;
}

.testcase-section td {
  border: 1px solid #ccc;
  /* vertical-align: top; */
  padding: 8px;
}

.proceed-button {
  margin-top: 20px;
  padding: 10px 30px;
  background-color:#272D4F;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}
.footer-links {
    margin-top: 20rem;
    font-size: 14px;
    color: #333;
}
.footer-links a {
  text-decoration: underline;
  color:rgb(11, 114, 133);;
}

.back-button-container {
  /* margin-top: 30px; */
}
.back-button:hover {
    background-color: #999;
}

.back-button {
    margin-top: 20px;
    background-color: #ccc;
    color: #000;
    padding: 8px 12px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
}
.multiple-testcase-content {
  display: flex;
  flex-direction: row;
  /* align-items: center; */
  gap: 30px;
  margin-top: 30px;
}

.upload-options {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: -10px;
  font-size: 14px;
}

.upload-item {
  margin: 4px 0;
  color: #000;
}

.upload-key {
  margin-top: 10px;
}

.key-format {
  color: #4169e1;
  font-weight: bold;
}


/* comparison table*/

/* Comparison Results Styles */
.comparison-results {
  padding: 20px;
  /* max-width: 1200px; */
  margin: 0 auto;
}

.comparison-results h2 {
 display: flex;
}

.summary-section {
  background-color: #f5f9ff;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #d0e0ff;
}

.summary-section h3 {
  margin-top: 0;
  color: #084a94;
}

.report-link {
  display: inline-block;
  margin-top: 10px;
  color: #0a6ed1;
  text-decoration: none;
  font-weight: bold;
}

.report-link:hover {
  text-decoration: underline;
}

.table-container {
  overflow-x: auto;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.comparison-table th {
  background-color:#272D4F;
  color: white;
  padding: 12px;
  /* text-align: left; */
}
.vl {
  border-left: 6px solid green;
  height: 500px;
}
.comparison-table td {
  padding: 10px;
  border-bottom: 1px solid #ddd;
}

.comparison-table tr:hover {
  background-color: #f5f5f5;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-weight: bold;
  text-align: center;
}

.status-match {
  color: #4CAF50; /* Green */
  background-color: #fefefe;
}

.status-difference {
  color: rgb(237, 16, 16);
  background-color: #fefefe; /* Red */
}

.status-cannot-process {
  color: #FF9800; /* Orange */
  background-color: #fefefe;
}

.details-button {
  background-color:#272D4F;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
}

.details-button:hover {
  background-color: #084a94;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 1.5em;
  cursor: pointer;
}

.differences-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.differences-table th {
  background-color: #f0f0f0;
  padding: 8px;
  text-align: left;
}

.differences-table td {
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

.loading-message {
  text-align: center;
  padding: 40px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .comparison-table th, 
  .comparison-table td {
    padding: 8px 5px;
    font-size: 0.9em;
  }
  
  .details-button {
    padding: 3px 6px;
    font-size: 0.8em;
  }
}




/* payload */


/* Payload Modal Styles */
.payload-content-container {
  margin-top: 15px;
  max-height: 60vh;
  overflow: auto;
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 4px;
}

.payload-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  margin: 0;
}

.view-payload-button {
  background: none;
  border: none;
  color:#272D4F;
  text-decoration: underline;
  cursor: pointer;
  padding: 0;
  font: inherit;
}

.view-payload-button:hover {
  color: #084a94;
}

/* Make sure your modal styles support scrolling */
.modal-content {
  max-height: 80vh;
  overflow-y: auto;
}

/* intermediatepage css */

.intermediate-page {
  width: 80%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  /* background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1); */
}

.intermediate-page h3 {
  color: #1fa1cc;
  margin-bottom: 15px;
}

.requirements-list {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin: 20px 0;
}

.requirements-list ul {
  padding-left: 20px;
}

.requirements-list li {
  margin-bottom: 8px;
}


/* time range table */


/* Add to App.css */
.time-range-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

.time-range-table th, 
.time-range-table td {
  padding: 12px;
  border: 1px solid #ddd;
  text-align: left;
}

.time-range-table th {
  background-color: #272D4F;
  color: white;
}

.time-range-table tr:nth-child(even) {
  background-color: #f2f2f2;
}

.time-range-table input[type="datetime-local"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}


.diff-modal {
  width: 90vw;
  max-width: 1200px;
  padding: 20px;
}

.diff-container {
  border: 1px solid #ddd;
  margin: 15px 0;
  height: 70vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.diff-legend {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

.po-color {
  background-color: #d4edda;
}

.cpi-color {
  background-color: #f8d7da;
}

.details-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}




.custom-date-input {
  display: flex;
  align-items: center;
  border: 1px solid #ccc;
  padding: 6px 8px;
  border-radius: 4px;
  width: 100%;
  max-width: 300px;
  background-color: white;
  cursor: pointer;
}

.custom-date-input input {
  border: none;
  flex-grow: 1;
  outline: none;
  cursor: pointer;
}

.calendar-icon {
  margin-left: 8px;
  color: #555;
  font-size: 18px;
}

/* download button */
.download-button {
    margin-top: 20px;
    height: 36px;
    width: 70px;
  /* padding: 10px 20px; */
    background-color: #272D4F;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.download-button:hover {
  background-color: #1565c0;
}



/* custom date time */

.custom-datetime input {
  padding: 6px 8px;
  font-size: 14px;
}

.rdtPicker {
  font-size: 12px;
  width: 250px; /* Or less */
}

.rdtPicker td, .rdtPicker th {
  padding: 4px;
}

.rdtTime input {
  width: 100px;
  font-size: 12px;
}





/* Comparison Results Styles */
/* .comparison-results {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.results-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.summary-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.summary-stats p {
  margin: 0;
}

.match {
  color: #2e7d32;
}

.mismatch {
  color: #c62828;
}

.download-button {
  background-color: #1976d2;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.download-button:hover {
  background-color: #1565c0;
}

.back-button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 20px;
}

.back-button:hover {
  background-color: #e0e0e0;
} */





/* Mobile back button - hidden on larger screens by default */
.mobile-back-button {
  display: none;
  background-color: #29a8e0;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  margin-bottom: 15px;
  cursor: pointer;
}

@media (max-width: 768px) {
  .mobile-back-button {
    display: inline-block;
  }
  
  /* You might want to hide the sidebar on mobile and show a hamburger menu instead */
  .sidebar {
    display: none;
  }
  
  /* Show sidebar when mobile menu is toggled */
  .sidebar.active {
    display: block;
  }
}