

import React, { useState } from "react";
import API_ENDPOINT from "../config";
import { Snackbar, Alert } from "@mui/material";
import { useNavigate } from "react-router-dom";
import "./IDOCConfigurationParam.css";

const IDOCConfigurationParam = ({ artifactId, onClose }) => {
  const [parameterValue, setParameterValue] = useState(""); // Changed from fieldName
  const [fieldValue, setFieldValue] = useState("");
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [severity, setSeverity] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [showHttpsWarning, setShowHttpsWarning] = useState(false);

  const handleFieldValueChange = (e) => {
    const value = e.target.value;
    setFieldValue(value);
    
    // Show warning if user types http:// or doesn't start with https://
    if (value.startsWith("http://") || 
        (!value.startsWith("https://") && value.length > 0)) {
      setShowHttpsWarning(true);
    } else {
      setShowHttpsWarning(false);
    }
  };

  const handleBack = () => {
    if (onClose) {
      onClose();
    } else {
      navigate("/SelectTask");
    }
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const validateInputs = () => {
    if (!parameterValue.trim()) { // Changed from fieldName
      setSnackbarMessage("Parameter name is required");
      return false;
    }
    if (!fieldValue.trim()) {
      setSnackbarMessage("Field value is required");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    if (!validateInputs()) {
      setSeverity("error");
      setSnackbarOpen(true);
      setLoading(false);
      return;
    }

    const payload = {
      parameterValue: fieldValue // This should probably be fieldValue, not parameterValue
    };

    // Fixed the endpoint URL - using parameterValue state variable
    const endpoint = `${API_ENDPOINT}/api/updateParam/${artifactId}/${parameterValue}`;

    try {
      const response = await fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        setSnackbarMessage("Configuration Updated Successfully");
        setSeverity("success");
        setSnackbarOpen(true);
      } else {
        const errorData = await response.json();
        setSnackbarMessage(
          errorData.message || "Failed to update configuration"
        );
        setSeverity("error");
        setSnackbarOpen(true);
      }
    } catch (error) {
      console.error("Error updating configuration:", error);
      setSnackbarMessage("Network error occurred");
      setSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="idoc-configuration-container">
      {loading && (
        <div className="loading-container">
          {/* Loading spinner/gif can be placed here */}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <h3>IDOC Address Configuration</h3>
        <div className="form-section">
          <div className="form-group">
            <label htmlFor="parameterValue" style={{width:"14rem"}}>Parameter Name:</label>
            <input
              type="text"
              id="parameterValue"
              value={parameterValue} // Now uses the correct state variable
              onChange={(e) => setParameterValue(e.target.value)} // Fixed onChange handler
              className="form-control"
              placeholder="Enter your custom address field name"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="fieldValue" style={{width:"14rem"}}>Parameter Value:</label>
            <input
              type="text"
              id="fieldValue"
              value={fieldValue}
              onChange={handleFieldValueChange}
              className="form-control"
              placeholder="Enter address value"
              required
            />
          </div>
          <div>
            {showHttpsWarning && (
              <div className="alert alert-warning mt-2">
                <small>Warning: Address must start with https:// for secure connection</small>
              </div>
            )}
          </div>
        </div>

        <div className="form-group button-group">
          <button
            type="button"
            className="boldBtn btn btn-dark"
            onClick={handleBack}
          >
            Back
          </button>
          <button
            type="submit"
            className="boldBtn btn btn-primary"
            disabled={loading}
          >
            {loading ? "Updating..." : "Update"}
          </button>
        </div>
      </form>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
        sx={{ marginTop: "100px" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={severity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default IDOCConfigurationParam;