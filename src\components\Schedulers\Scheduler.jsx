import React, { useState } from 'react';
import { TextField } from '@mui/material';
import 'react-datepicker/dist/react-datepicker.css';

const Scheduler = ({ onCronExpressionUpdate }) => {
  const [selectedTime, setSelectedTime] = useState(null);
  const [selectedDate, setSelectedDate] = useState('');

  const handleTimeSelection = (event) => {
    setSelectedTime(event.target.value);
    updateCronExpression(event.target.value, selectedDate);
  };

  const handleDateSelection = (event) => {
    setSelectedDate(event.target.value);
    updateCronExpression(selectedTime, event.target.value);
  };

  const updateCronExpression = (timeString, dateString) => {
    if (!timeString || !dateString) {
      return;
    }

    const [hours, minutes] = timeString.split(':').map(Number);
    const [year, month, day] = dateString.split('-');

    const cronMinutes = String(minutes);
    const cronHours = String(hours);
    const cronDayOfMonth = String(parseInt(day, 10));
    const cronMonth = String(parseInt(month, 10));
    const cronYear = String(year);

    const cronExpression = `0 ${cronMinutes} ${cronHours} ${cronDayOfMonth} ${cronMonth} ? ${cronYear} --tz=IST`;
    onCronExpressionUpdate(cronExpression);
  };

  return (
    <div style={{ margin: '20px 0', fontFamily: 'Arial, sans-serif' }}>
      <div style={{ marginBottom: '20px' }}>
        <div style={{ display: 'flex', alignItems: 'flex-start', flexDirection: 'column' }}>
          <label style={{ marginRight: '10px' }}>Time (24 Hour format):</label>
          <TextField
            label="Select Time"
            type="time"
            value={selectedTime}
            onChange={handleTimeSelection}
            style={{ marginTop: "10px", width: "14rem" }}
            InputLabelProps={{
              shrink: true,
            }}
            inputProps={{
              step: 300,
            }}
          />
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <div style={{ display: 'flex', alignItems: 'flex-start', flexDirection: 'column' }}>
          <label style={{ marginRight: '10px' }}>Date:</label>
          <TextField
            label="Select Date"
            type="date"
            value={selectedDate}
            onChange={handleDateSelection}
            style={{ marginTop: "10px", width: "14rem" }}
            InputLabelProps={{
              shrink: true,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default Scheduler;