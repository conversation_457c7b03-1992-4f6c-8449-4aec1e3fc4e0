import React, { useState } from "react";
import API_ENDPOINT from "../config";
import {
  Snackbar,
  Alert,
  CircularProgress,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import "./HTTPConfigurationParam.css";

const HTTPConfigurationParam = ({ artifactId, onClose }) => {
  const [formData, setFormData] = useState({
    fieldName: "",
    fieldValue: "",
    authType: "",
    userRole: "",
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "info",
  });
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [showHttpsWarning, setShowHttpsWarning] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // When authType changes to user_role, set default userRole
    if (name === "authType" && value === "user_role") {
      setFormData((prev) => ({
        ...prev,
        authType: value,
        userRole: "ESBMessaging.send", // Set default value
      }));
      return;
    }

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Special handling for fieldValue to check HTTPS
    if (name === "fieldValue") {
      setShowHttpsWarning(
        value.startsWith("http://") ||
          (!value.startsWith("https://") && value.length > 0)
      );
    }
  };

  const handleBack = () => {
    onClose ? onClose() : navigate("/SelectTask");
  };

  const handleSnackbarClose = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  const validateInputs = () => {
    const { fieldName, fieldValue, authType, userRole } = formData;

    if (!fieldName.trim()) {
      setSnackbar({
        open: true,
        message: "Parameter name is required",
        severity: "error",
      });
      return false;
    }

    if (!fieldValue.trim()) {
      setSnackbar({
        open: true,
        message: "Parameter value is required",
        severity: "error",
      });
      return false;
    }

    if (authType === "user_role" && !userRole.trim()) {
      setSnackbar({
        open: true,
        message: "User role is required when authorization type is 'User Role'",
        severity: "error",
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateInputs()) return;

    setLoading(true);

    try {
      const payload = {
        parameterValue: formData.fieldValue,
        // authType: formData.authType,
        // ...(formData.authType === "user_role" && {
        //   userRole: formData.userRole,
        // }),
      };

      const response = await fetch(
        `${API_ENDPOINT}/api/updateParam/${artifactId}/${formData.fieldName}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        }
      );

      const result = await response.json();

      if (response.ok) {
        setSnackbar({
          open: true,
          message: "Configuration updated successfully",
          severity: "success",
        });
        // Reset form after successful submission
        setFormData({
          fieldName: "",
          fieldValue: "",
          authType: "",
          userRole: "",
        });
      } else {
        throw new Error(result.message || "Failed to update configuration");
      }
    } catch (error) {
      console.error("Update error:", error);
      setSnackbar({
        open: true,
        message: error.message || "Network error occurred",
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="idoc-configuration-container">
      {loading && (
        <div className="loading-overlay">
          <CircularProgress />
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <h3>HTTPS Address Configuration</h3>

        <div className="form-section">
          <div className="form-group">
            <label htmlFor="fieldName">Parameter Name:</label>
            <input
              type="text"
              id="fieldName"
              name="fieldName"
              value={formData.fieldName}
              onChange={handleInputChange}
              className="form-control"
              placeholder="Enter parameter name"
              required
              disabled={loading}
            />
          </div>

          <div className="form-group">
            <label htmlFor="fieldValue">Parameter Value:</label>
            <input
              type="url"
              id="fieldValue"
              name="fieldValue"
              value={formData.fieldValue}
              onChange={handleInputChange}
              className="form-control"
              placeholder="https://example.com"
              required
              disabled={loading}
              pattern="https://.*"
              title="URL must start with https://"
            />
          </div>

          {showHttpsWarning && (
            <div className="alert alert-warning mt-2">
              <small>Warning: For security, please use HTTPS URLs</small>
            </div>
          )}

          {/* <div className="form-group">
            <FormControl fullWidth>
              <InputLabel id="auth-type-label">Authorization Type</InputLabel>
              <Select
                labelId="auth-type-label"
                id="authType"
                name="authType"
                value={formData.authType}
                onChange={handleInputChange}
                label="Authorization Type"
                disabled={loading}
              >
                <MenuItem value="">None</MenuItem>
                <MenuItem value="client_certificate">
                  Client Certificate
                </MenuItem>
                <MenuItem value="user_role">User Role</MenuItem>
              </Select>
            </FormControl>
          </div>

          {formData.authType === "user_role" && (
            <div className="form-group">
              <label htmlFor="userRole">User Role:</label>
              <input
                type="text"
                id="userRole"
                name="userRole"
                value={formData.userRole}
                onChange={handleInputChange}
                className="form-control"
                placeholder="Enter user role"
                required
                disabled={loading}
              />
            </div>
          )} */}
        </div>

        <div className="form-group button-group">
          <button
            type="button"
            className="boldBtn btn btn-dark"
            onClick={handleBack}
            disabled={loading}
          >
            Back
          </button>
          <button
            type="submit"
            className="boldBtn btn btn-primary"
            disabled={loading}
          >
            {loading ? (
              <span className="spinner-border spinner-border-sm" />
            ) : (
              "Update"
            )}
          </button>
        </div>
      </form>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
        sx={{ marginTop: "100px" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default HTTPConfigurationParam;
