import React, { useState, useEffect } from "react";
import APIEndpoint from "../config";
import CircularProgress from "@mui/material/CircularProgress";

const SolutionsModal = ({ 
  isOpen, 
  onClose, 
  solutionApiEndpoint, 
  title = "Possible Solutions",
  interfaceName = "",
  messageId = ""
}) => {
  const [solutions, setSolutions] = useState([]);
  const [loadingSolutions, setLoadingSolutions] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isOpen && solutionApiEndpoint) {
      fetchSolutions();
    } else if (isOpen && !solutionApiEndpoint) {
      setSolutions([]);
      setError(null);
    }
  }, [isOpen, solutionApiEndpoint]);

  const fetchSolutions = async () => {
    setLoadingSolutions(true);
    setError(null);

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${APIEndpoint}${solutionApiEndpoint}`, {
        headers: {   "Authorization": `Basic ${localStorage.getItem("basicAuth")}`, },
      });

      if (response.ok) {
        const data = await response.json();
        // Handle different response formats
        if (Array.isArray(data)) {
          setSolutions(data);
        } else if (data.solutions && Array.isArray(data.solutions)) {
          setSolutions(data.solutions);
        } else if (data.data && Array.isArray(data.data)) {
          setSolutions(data.data);
        } else {
          setSolutions([]);
        }
      } else {
        throw new Error(`Failed to fetch solutions: ${response.statusText}`);
      }
    } catch (error) {
      console.error("Error fetching solutions:", error);
      setError(error.message);
      setSolutions([]);
    } finally {
      setLoadingSolutions(false);
    }
  };

  const handleClose = () => {
    setSolutions([]);
    setError(null);
    onClose();
  };

  const renderSolution = (solution, index) => {
    if (typeof solution === 'string') {
      return solution;
    } else if (typeof solution === 'object' && solution !== null) {
      // If it's an object with a specific structure, handle it appropriately
      if (solution.description) {
        return solution.description;
      } else if (solution.message) {
        return solution.message;
      } else if (solution.solution) {
        return solution.solution;
      } else {
        return JSON.stringify(solution, null, 2);
      }
    }
    return `Solution ${index + 1}`;
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content solutions-modal">
        <div className="modal-header">
          <h3>
            {title}
            {interfaceName && (
              <small> - {interfaceName}</small>
            )}
            {/* {messageId && (
              <small> (Message ID: {messageId})</small>
            )} */}
          </h3>
          <button className="close-modal" onClick={handleClose}>
            ×
          </button>
        </div>

        <div className="solutions-container">
          {loadingSolutions ? (
            <div className="loading-message">
              <CircularProgress />
              <p>Loading solutions...</p>
            </div>
          ) : error ? (
            <div className="error-message">
              <p>Error loading solutions: {error}</p>
              <button 
                className="retry-button" 
                onClick={fetchSolutions}
              >
                Retry
              </button>
            </div>
          ) : solutions.length > 0 ? (
            <div className="solutions-list">
              {solutions.map((solution, index) => (
                <div key={index} className="solution-item">
                  <div className="solution-number" style={{display: 'flex'}}>
                    Proposed Solution {index + 1}
                  </div>
                  <div className="solution-content">
                    ➤{renderSolution(solution, index)}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-solutions">
              <p>No solutions available for this error.</p>
              {solutionApiEndpoint && (
                <button 
                  className="retry-button" 
                  onClick={fetchSolutions}
                >
                  Refresh
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SolutionsModal;