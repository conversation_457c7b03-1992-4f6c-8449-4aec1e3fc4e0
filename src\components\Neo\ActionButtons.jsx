import React from "react";
import { But<PERSON>, Box, LinearProgress } from "@mui/material";
import SettingsIcon from '@mui/icons-material/Settings';
import CloudIcon from '@mui/icons-material/Cloud';

const ActionButtons = ({ 
  onConfigure, 
  onDeploy, 
  isConfigureEnabled, 
  isDeploying 
}) => {
  return (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <Button
        variant="outlined"
        startIcon={<SettingsIcon />}
        onClick={onConfigure}
        disabled={!isConfigureEnabled}
        sx={{
          px: 3,
          py: 1.5,
          color: isConfigureEnabled ? '#4a5568' : '#a0aec0',
          borderColor: isConfigureEnabled ? '#cbd5e0' : '#e2e8f0',
          '&:hover': isConfigureEnabled ? {
            backgroundColor: '#edf2f7',
            borderColor: '#a0aec0'
          } : {}
        }}
      >
        Configure
      </Button>
      
      <Button
        variant="outlined"
        startIcon={<CloudIcon />}
        onClick={onDeploy}
        disabled={isDeploying}
        sx={{
          px: 3,
          py: 1.5,
          color: isDeploying ? '#a0aec0' : '#4a5568',
          borderColor: isDeploying ? '#e2e8f0' : '#cbd5e0',
          '&:hover': !isDeploying ? {
            backgroundColor: '#edf2f7',
            borderColor: '#a0aec0'
          } : {}
        }}
      >
        {isDeploying ? 'Deploying...' : 'Deploy'}
        {isDeploying && <LinearProgress sx={{ ml: 1, width: 24 }} />}
      </Button>
    </Box>
  );
};

export default ActionButtons;