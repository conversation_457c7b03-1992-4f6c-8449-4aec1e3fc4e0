import "../App.css";
import './ConfigureIDOCModel.css';
import HTTPConfigurationParam from "./HTTPConfigurationParam";

const ConfigureHTTP = ({ isOpen, onClose, iFlowName }) => {
  if (!isOpen) return null;

  return (
    <div className="scheduler-modal-overlay">
      <div className="scheduler-modal-content">
        <div className="scheduler-modal-header">
          <h3>Configure HTTPS for {iFlowName}</h3>
        </div>
        <div className="scheduler-modal-body">
          <HTTPConfigurationParam artifactId={iFlowName} onClose={onClose} />
        </div>
      </div>
    </div>
  );
};

export default ConfigureHTTP;