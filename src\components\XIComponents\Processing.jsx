import React, { useState } from "react";
import API_ENDPOINT from "../../config";

const Processing = ({ onClose, setSnackbar, artifactId }) => {
  const [processingData, setProcessingData] = useState({
    communicationParty: { param: "", value: "" },
    communicationComponent: { param: "", value: "DefaultXIService" }
  });
  const [loading, setLoading] = useState(false);

  const handleCancel = () => {
    if (onClose) onClose();
  };

  const handleProcessingChange = (e) => {
    const { name, value } = e.target;
    setProcessingData(prev => ({
      ...prev,
      [name]: { ...prev[name], value }
    }));
  };

  const handleParamChange = (e) => {
    const { name, value } = e.target;
    setProcessingData(prev => ({
      ...prev,
      [name]: { ...prev[name], param: value }
    }));
  };

  const handleSubmit = async () => {
    setLoading(true);
    
    try {
      // Create an array of all parameter updates
      const updates = [
        { name: processingData.communicationParty.param, value: processingData.communicationParty.value },
        { name: processingData.communicationComponent.param, value: processingData.communicationComponent.value }
      ].filter(update => update.name); // Only include parameters with names

      if (updates.length === 0) {
        setSnackbar({
          open: true,
          message: "Please enter at least one parameter name",
          severity: "error"
        });
        setLoading(false);
        return;
      }

      // Execute all updates sequentially
      let lastResponse = null;
      for (const update of updates) {
        const endpoint = `${API_ENDPOINT}/api/updateParam/${artifactId}/${update.name}`;
        
        const response = await fetch(endpoint, {
          method: "PUT",
          headers: {
              "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            parameterValue: update.value,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update parameter ${update.name}`);
        }
        
        // Store the last successful response
        lastResponse = await response.json();
      }

      // Show the success message from the API response
      if (lastResponse && lastResponse.status) {
        setSnackbar({
          open: true,
          message: lastResponse.status,
          severity: "success"
        });
      } else {
        setSnackbar({
          open: true,
          message: "Parameters updated successfully",
          severity: "success"
        });
      }
    } catch (error) {
      console.error("Error updating parameters:", error);
      setSnackbar({
        open: true,
        message: error.message || "Failed to update some parameters",
        severity: "error"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h4 style={{ 
        display: 'flex',
        color: '#6c757d', 
        fontSize: '14px', 
        fontWeight: 'bold',
        marginBottom: '20px',
        textTransform: 'uppercase'
      }}>
        XI RECEIVER IDENTIFIERS FOR RESPONSE
      </h4>
      
      <div style={{ display: 'grid', gap: '20px' }}>
        <div style={{ display: 'grid', gridTemplateColumns: '200px 1fr 200px', gap: '10px', alignItems: 'center' }}>
          <label style={{ color: '#333', fontSize: '14px' }}>Communication Party:</label>
          <input
            type="text"
            name="communicationParty"
            value={processingData.communicationParty.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <input
            type="text"
            name="communicationParty"
            value={processingData.communicationParty.value}
            onChange={handleProcessingChange}
            placeholder="Define Value"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: '200px 1fr 200px', gap: '10px', alignItems: 'center' }}>
          <label style={{ color: '#333', fontSize: '14px' }}>Communication Component:</label>
          <input
            type="text"
            name="communicationComponent"
            value={processingData.communicationComponent.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <input
            type="text"
            name="communicationComponent"
            value={processingData.communicationComponent.value}
            onChange={handleProcessingChange}
            placeholder="DefaultXIService"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
        </div>
      </div>
      
      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '10px',
        padding: '15px 20px',
        borderTop: '1px solid #dee2e6',
        backgroundColor: '#f8f9fa',
        marginTop: '20px'
      }}>
        <button
          onClick={handleSubmit}
          disabled={loading}
          style={{
            padding: '8px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer',
            fontWeight: 'bold',
            opacity: loading ? 0.7 : 1
          }}
        >
          {loading ? 'Updating...' : 'OK'}
        </button>
        <button
          onClick={handleCancel}
          style={{
            padding: '8px 20px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default Processing;