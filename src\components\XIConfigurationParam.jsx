import React, { useState } from "react";
import {
  Snac<PERSON><PERSON>,
  Alert,
  CircularProgress,
} from "@mui/material";
import ConnectionTab from "./AS2Components/ConnectionTab";
import SecurityTab from "./AS2Components//SecurityTab";
import MDNTab from "./AS2Components//MDNTab";
import DelieveryAssurance from "./XIComponents/DelieveryAssurance";
import ConditionsTab from "./AS2Components//ConditionsTab";
import Processing from "./XIComponents/Processing";

const XIConfigurationParam = ({ artifactId, onClose }) => {
  const [activeTab, setActiveTab] = useState("Connection");
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "info",
  });
  const [loading, setLoading] = useState(false);

  const handleSnackbarClose = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  const handleBack = () => {
    if (onClose) onClose();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSnackbar({
        open: true,
        message: "Configuration updated successfully",
        severity: "success",
      });
    } catch (error) {
      setSnackbar({
        open: true,
        message: error.message || "Failed to update configuration",
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };


  return (
    <div style={{  
      fontFamily: 'Arial, sans-serif', 
      maxWidth: '50rem', 
      margin: '0 auto', 
      padding: '20px',
      backgroundColor: '#f5f5f5'
    }}>
      {loading && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(255,255,255,0.8)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 9999
        }}>
          <CircularProgress />
        </div>
      )}

      <div style={{
        maxwidth: '50rem',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        {/* AS2 Label */}
        <div style={{
          display: 'flex',
          padding: '10px 20px',
          color: '#6c757d',
          fontSize: '14px'
        }}>
          XI
        </div>

        {/* Tabs */}
        <div style={{ display: 'flex', borderBottom: '1px solid #dee2e6' }}>
          {["Connection", "Processing","Delivery Assurance", "Conditions"].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 20px',
                border: 'none',
                backgroundColor: activeTab === tab ? 'white' : '#f8f9fa',
                borderBottom: activeTab === tab ? '2px solid #007bff' : '2px solid transparent',
                cursor: 'pointer',
                color: activeTab === tab ? '#007bff' : '#333',
                fontSize: '14px',
                fontWeight: activeTab === tab ? 'bold' : 'normal'
              }}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Content */}
        <div style={{padding: "10px 102px 10px 10px" }}>
          {activeTab === "Connection" && <ConnectionTab onClose={onClose} artifactId={artifactId} setSnackbar={setSnackbar} />}
          {activeTab === "Processing" && <Processing onClose={onClose} artifactId={artifactId} setSnackbar={setSnackbar} />}
          {activeTab === "Security" && <SecurityTab onClose={onClose} artifactId={artifactId} setSnackbar={setSnackbar} />}
          {activeTab === "MDN" && <MDNTab onClose={onClose} artifactId={artifactId} setSnackbar={setSnackbar} />}
          {activeTab === "Delivery Assurance" && <DelieveryAssurance onClose={onClose} artifactId={artifactId} setSnackbar={setSnackbar} />}
          {activeTab === "Conditions" && <ConditionsTab onClose={onClose} artifactId={artifactId} setSnackbar={setSnackbar} />}
        </div>

        {/* Footer buttons */}
        <div style={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '10px',
          padding: '15px 20px',
          borderTop: '1px solid #dee2e6',
          backgroundColor: '#f8f9fa'
        }}>
          
        </div>
      </div>

      <Snackbar
      style={{
        position: 'fixed',
        marginTop:"61px"
      }}
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default XIConfigurationParam;