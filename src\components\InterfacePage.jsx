

import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { FaCheckCircle } from "react-icons/fa";
import "../App.css";

const InterfacePage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedInterfaces, setSelectedInterfaces] = useState([]);

  // Get the interface data passed from TimestampPage
  const interfaceData = location.state?.interfaceData || {
    success: false,
    count: 0,
    messages: []
  };

  const handleGoBack = () => {
    navigate("/dashboard/timestamp");
  };

  const handleInterfaceSelect = (index) => {
    setSelectedInterfaces(prev => {
      if (prev.includes(index)) {
        // If already selected, remove it
        return prev.filter(i => i !== index);
      } else {
        // If not selected, add it
        return [...prev, index];
      }
    });
  };

  const handleTestInterface = () => {
    if (selectedInterfaces.length === 0) {
      alert("Please select at least one interface first");
      return;
    }
    
    const selected = selectedInterfaces.map(index => interfaceData.messages[index]);
    console.log("Testing interfaces:", selected);
    
    // Navigate to po-testing page with the selected interfaces data
    navigate("/dashboard/potesting", { 
      state: { 
        selectedInterfaces: selected,
        interfaceIndices: selectedInterfaces 
      } 
    });
  };

  return (
    <div style={{
      padding: "20px",
      maxWidth: "900px",
      margin: "0 auto",
      minHeight: "calc(100vh - 200px)",
      display: "flex",
      flexDirection: "column",
      border: "1px solid #e5e7eb",
      borderRadius: "12px",
      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)",
      backgroundColor: "#fff",
      justifyContent: "space-between"
    }}>
      <div className="intermediate-page">
        <h3>Interfaces Fetched From SAP PO 7.5</h3>
        <p style={{fontSize:"1rem"}}>
          {interfaceData.success 
            ? `After execution, we have found ${interfaceData.count} interfaces within the entered timestamp. Select one or more interfaces to test.` 
            : "No interface data available"}
        </p>
        
        {interfaceData.count > 0 ? (
          <table className="time-range-table" style={{ width: "100%" }}>
            <thead>
              <tr style={{ backgroundColor: "#272D4F", color: "white" }}>
                <th style={{ padding: "12px", border: "1px solid #ccc", textAlign: "left", width: "50px" }}>
                  Select
                </th>
                <th style={{ padding: "12px", border: "1px solid #ccc", textAlign: "left" }}>
                  Interface Name
                </th>
              </tr>
            </thead>
            <tbody>
              {interfaceData.messages.map((message, index) => (
                <tr 
                  key={index}
                  style={{
                    backgroundColor: selectedInterfaces.includes(index) ? "#e0e7ff" : "white",
                    cursor: "pointer",
                    fontSize: "14px"
                  }}
                  onClick={() => handleInterfaceSelect(index)}
                >
                  <td style={{ padding: "12px", border: "1px solid #ccc", textAlign: "center" }}>
                    <input
                      type="checkbox"
                      name="interface"
                      checked={selectedInterfaces.includes(index)}
                      onChange={() => handleInterfaceSelect(index)}
                      style={{ cursor: "pointer" }}
                      onClick={(e) => e.stopPropagation()} // Prevent double trigger
                    />
                  </td>
                  <td style={{ 
                    padding: "12px", 
                    border: "1px solid #ccc",
                    fontWeight: selectedInterfaces.includes(index) ? "600" : "normal",
                    color: selectedInterfaces.includes(index) ? "#1e40af" : "#1e293b",
                    fontSize: "14px"
                  }}>
                    {message.interface.name}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div style={{
            padding: "40px",
            textAlign: "center",
            color: "#6b7280",
            fontStyle: "italic",
            border: "1px solid #e2e8f0",
            borderRadius: "8px",
            backgroundColor: "#f8fafc"
          }}>
            No interfaces found for the selected time range
          </div>
        )}

        <div className="button-group" style={{ marginTop: "24px" }}>
          <button onClick={handleGoBack} className="back-button">
            ← Back to Timestamp
          </button>
          <button
            onClick={handleTestInterface}
            className="back-button"
            disabled={selectedInterfaces.length === 0}
            style={{
              backgroundColor: selectedInterfaces.length === 0 ? "#94a3b8" : "#084a94",
              color: "white",
              cursor: selectedInterfaces.length === 0 ? "not-allowed" : "pointer"
            }}
          >
            Test Selected Interfaces ({selectedInterfaces.length}) <FaCheckCircle style={{ marginLeft: "8px" }} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default InterfacePage;