import React, { useState } from 'react';
import { FaUserShield, FaKey, FaBuilding, FaLock, FaSave, FaGlobe, FaLink, FaMagic } from 'react-icons/fa';
import { FaExclamationCircle, FaCheckCircle } from 'react-icons/fa';
import { FaUser } from 'react-icons/fa';
import { useNavigate } from "react-router-dom";
import APIEndpoint from '../../config';

const NeoTest = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    // NEO CPI Credentials
    neoCpiClientid: '',
    neoCpiClientSecret: '',
    neoCpiBaseUrl: '',
    neoCpiTokenUrl: '',
    
    // NEO API Credentials
    neoClientId: '',
    neoClientSecret: '',
    neoBaseUrl: '',
    neoTokenUrl: '',
    
    // Common Configuration
    configName: 'Dev'
  });

  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAutoFill = () => {
    setFormData({
      neoCpiClientid: 'b3c04447-e6b9-31bf-84a0-3a1e05fa4e6e',
      neoCpiClientSecret: 'NEO@BTP_Incture@2024',
      neoCpiBaseUrl: 'https://p550010-iflmap.hcisbp.ae1.hana.ondemand.com',
      neoCpiTokenUrl: 'https://oauthasservices-ijv8j6wewk.ae1.hana.ondemand.com/oauth2/api/v1/token',
      neoClientId: 'b905f8ff-22e9-37e5-82db-3479297907f8',
      neoClientSecret: 'Incture@123',
      neoTokenUrl: 'https://oauthasservices-ijv8j6wewk.ae1.hana.ondemand.com/oauth2/api/v1/token',
      neoBaseUrl: 'https://p550010-tmn.hci.ae1.hana.ondemand.com',
      configName: 'Dev'
    });
    setSuccessMessage('Form autofilled with data. Please verify and submit!');
    setErrorMessage('');
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleRegister();
    }
  };

  const handleRegister = async () => {
    // Validate all fields
    if (!formData.neoCpiClientid || !formData.neoCpiClientSecret || !formData.neoCpiBaseUrl || !formData.neoCpiTokenUrl ||
        !formData.neoClientId || !formData.neoClientSecret || !formData.neoBaseUrl || !formData.neoTokenUrl) {
      setErrorMessage('Please fill in all fields');
      setSuccessMessage('');
      return;
    }

    setIsLoading(true);
    setErrorMessage('');
    setSuccessMessage('');

    try {
      // Prepare data for NEO CPI credentials
      const neoCpiData = {
        neoCpiClientid: formData.neoCpiClientid,
        neoCpiClientSecret: formData.neoCpiClientSecret,
        neoCpiBaseUrl: formData.neoCpiBaseUrl,
        neoCpiTokenUrl: formData.neoCpiTokenUrl,
        env: formData.configName
      };

      // Prepare data for NEO API credentials
      const neoApiData = {
        neoClientId: formData.neoClientId,
        neoClientSecret: formData.neoClientSecret,
        neoTokenUrl: formData.neoTokenUrl,
        neoBaseUrl: formData.neoBaseUrl,
        env: formData.configName
      };

      // Make API calls
      const [cpiResponse, apiResponse] = await Promise.all([
        fetch(`${APIEndpoint}/api/setneocpicredentials`, {
          method: 'POST',
          headers: {
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(neoCpiData)
        }),
        fetch(`${APIEndpoint}/api/setneoapicredentials`, {
          method: 'POST',
          headers: {
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(neoApiData)
        })
      ]);

      // Check responses
      const cpiResult = await cpiResponse.json().catch(() => null);
      const apiResult = await apiResponse.json().catch(() => null);

      if (!cpiResponse.ok || !apiResponse.ok) {
        throw new Error(
          (cpiResult?.message ? cpiResult.message : 'Failed to save NEO CPI credentials') +
          (apiResult?.message ? ' | ' + apiResult.message : '')
        );
      }

      // All calls succeeded
      setSuccessMessage(`Credentials successfully registered for ${formData.configName} environment`);
      
      // Reset form after successful registration
      setFormData({
        neoCpiClientid: '',
        neoCpiClientSecret: '',
        neoCpiBaseUrl: '',
        neoCpiTokenUrl: '',
        neoClientId: '',
        neoClientSecret: '',
        neoBaseUrl: '',
        neoTokenUrl: '',
        configName: 'Dev'
      });
      
      // Navigate to selection page after successful registration
      setTimeout(() => {
        navigate("/dashboard/neo/neoselection");
      }, 1500);
      
    } catch (error) {
      setErrorMessage(error.message || 'Failed to register credentials');
      console.error('Registration error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <div style={styles.header}>
          <div style={styles.logo}>
            <FaUserShield size={36} color="#084a94" />
          </div>
          <h2 style={styles.title}>NEO User Login Page</h2>
          <p style={styles.subtitle}>Register credentials for Dev or QA environment</p>
        </div>

        {errorMessage && (
          <div style={styles.errorAlert}>
            <FaExclamationCircle style={styles.errorIcon} />
            <span>{errorMessage}</span>
          </div>
        )}

        {successMessage && (
          <div style={styles.successAlert}>
            <FaCheckCircle style={styles.successIcon} />
            <span>{successMessage}</span>
          </div>
        )}

        <label style={styles.sectionLabel}>
          <span>NEO CPI Credentials</span>
        </label>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaUser style={styles.inputIcon} />
            <span>Client ID</span>
          </label>
          <input
            type="text"
            name="neoCpiClientid"
            value={formData.neoCpiClientid}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter NEO CPI Client ID"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaKey style={styles.inputIcon} />
            <span>Client Secret</span>
          </label>
          <input
            type="password"
            name="neoCpiClientSecret"
            value={formData.neoCpiClientSecret}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter NEO CPI Client Secret"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaGlobe style={styles.inputIcon} />
            <span>Base URL</span>
          </label>
          <input
            type="url"
            name="neoCpiBaseUrl"
            value={formData.neoCpiBaseUrl}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter NEO CPI Base URL"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaLink style={styles.inputIcon} />
            <span>Token URL</span>
          </label>
          <input
            type="url"
            name="neoCpiTokenUrl"
            value={formData.neoCpiTokenUrl}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter NEO CPI Token URL"
            style={styles.input}
          />
        </div>

        {/* NEO API Credentials Section */}
        <label style={styles.sectionLabel}>
          <span>NEO API Credentials</span>
        </label>
        
        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaUser style={styles.inputIcon} />
            <span>Client ID</span>
          </label>
          <input
            type="text"
            name="neoClientId"
            value={formData.neoClientId}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter NEO API Client ID"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaKey style={styles.inputIcon} />
            <span>Client Secret</span>
          </label>
          <input
            type="password"
            name="neoClientSecret"
            value={formData.neoClientSecret}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter NEO API Client Secret"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaGlobe style={styles.inputIcon} />
            <span>Base URL</span>
          </label>
          <input
            type="url"
            name="neoBaseUrl"
            value={formData.neoBaseUrl}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter NEO API Base URL"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaLink style={styles.inputIcon} />
            <span>Token URL</span>
          </label>
          <input
            type="url"
            name="neoTokenUrl"
            value={formData.neoTokenUrl}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter NEO API Token URL"
            style={styles.input}
          />
        </div>

        {/* Common Configuration Section */}
        <label style={styles.sectionLabel}>
          <span>Environment Configuration</span>
        </label>
        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaBuilding style={styles.inputIcon} />
            <span>Environment</span>
          </label>
          <select
            name="configName"
            value={formData.configName}
            onChange={handleChange}
            style={styles.select}
          >
            <option value="Dev">Development</option>
            <option value="QA">Quality Assurance</option>
          </select>
        </div>

        <div style={styles.buttonGroup}>
          <button
            onClick={handleAutoFill}
            style={styles.secondaryButton}
            disabled={isLoading}
          >
            <FaMagic style={styles.buttonIcon} />
            Autofill
          </button>
          <button
            onClick={handleRegister}
            style={styles.primaryButton}
            disabled={isLoading}
          >
            <FaSave style={styles.buttonIcon} />
            {isLoading ? 'Registering...' : 'Register Credentials'}
          </button>
        </div>

        <div style={styles.footer}>
          <p style={styles.helpText}>
            Note: All credentials will be encrypted and stored securely
          </p>
        </div>
      </div>
    </div>
  );
};
const styles = {
  container: {
    marginTop: "20px",
    display: "flex",
    justifyContent: "center",
    fontFamily: "'Segoe UI', 'Roboto', 'Oxygen', sans-serif"
  },
  card: {
    width: "46%",
    backgroundColor: "white",
    borderRadius: "12px",
    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
    padding: "20px",
  },
  header: {
    textAlign: "center",
    marginBottom: "32px"
  },
  logo: {
    marginBottom: "16px",
    display: "flex",
    justifyContent: "center"
  },
  title: {
    fontSize: "24px",
    fontWeight: "600",
    color: "#1e293b",
    marginBottom: "8px"
  },
  subtitle: {
    fontSize: "14px",
    color: "#64748b",
    margin: 0
  },
  formGroup: {
    marginBottom: "20px"
  },
  sectionLabel: {
    display: "block",
    fontSize: "14px",
    fontWeight: "600",
    color: "#475569",
    marginBottom: "16px",
    paddingBottom: "8px",
    borderBottom: "1px solid #e2e8f0"
  },
  label: {
    display: "flex",
    alignItems: "center",
    fontSize: "14px",
    fontWeight: "500",
    color: "#475569",
    marginBottom: "8px"
  },
  inputIcon: {
    width: "16px",
    height: "16px",
    marginRight: "8px",
    color: "#64748b"
  },
  input: {
    width: "100%",
    padding: "12px 16px 12px 40px",
    fontSize: "14px",
    border: "1px solid #e2e8f0",
    borderRadius: "8px",
    backgroundColor: "#f8fafc",
    transition: "all 0.2s",
    boxSizing: "border-box",
    outline: "none",
    color: "#1e293b"
  },
  select: {
    width: "100%",
    padding: "12px 16px 12px 40px",
    fontSize: "14px",
    border: "1px solid #e2e8f0",
    borderRadius: "8px",
    backgroundColor: "#f8fafc",
    color: "#1e293b",
    appearance: "none",
    backgroundImage: "url(\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\")",
    backgroundRepeat: "no-repeat",
    backgroundPosition: "right 1rem center",
    backgroundSize: "1em"
  },
  buttonGroup: {
    display: "flex",
    gap: "12px",
    marginTop: "16px"
  },
  primaryButton: {
    flex: 1,
    padding: "14px 16px",
    fontSize: "15px",
    fontWeight: "500",
    backgroundColor: "#084a94",
    color: "white",
    border: "none",
    borderRadius: "8px",
    cursor: "pointer",
    transition: "all 0.2s",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    boxShadow: "0 2px 4px rgba(8, 74, 148, 0.2)"
  },
  secondaryButton: {
    flex: 1,
    padding: "14px 16px",
    fontSize: "15px",
    fontWeight: "500",
    backgroundColor: "#e2e8f0",
    color: "#475569",
    border: "none",
    borderRadius: "8px",
    cursor: "pointer",
    transition: "all 0.2s",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },
  buttonIcon: {
    marginRight: "8px"
  },
  errorAlert: {
    display: "flex",
    alignItems: "flex-start",
    padding: "12px 16px",
    backgroundColor: "#fee2e2",
    color: "#b91c1c",
    borderRadius: "8px",
    fontSize: "14px",
    marginBottom: "20px",
    wordBreak: "break-word"
  },
  successAlert: {
    display: "flex",
    alignItems: "center",
    padding: "12px 16px",
    backgroundColor: "#dcfce7",
    color: "#166534",
    borderRadius: "8px",
    fontSize: "14px",
    marginBottom: "20px"
  },
  errorIcon: {
    width: "16px",
    height: "16px",
    marginRight: "8px",
    flexShrink: 0
  },
  successIcon: {
    width: "16px",
    height: "16px",
    marginRight: "8px",
    flexShrink: 0
  },
  footer: {
    marginTop: "24px",
    textAlign: "center",
    borderTop: "1px solid #f1f5f9",
    paddingTop: "16px"
  },
  helpText: {
    fontSize: "13px",
    color: "#64748b",
    margin: 0
  }
};

export default NeoTest;