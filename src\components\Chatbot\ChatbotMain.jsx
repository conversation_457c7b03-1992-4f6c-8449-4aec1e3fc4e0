import React, { useState, useEffect, useRef } from 'react';
import {
    Box,
    TextField,
    Button,
    Typography,
    Paper,
    IconButton,
    Chip,
    CircularProgress,
    Avatar,
    Tooltip,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Alert,
    AlertTitle
} from '@mui/material';
import {
    Send as SendIcon,
    SmartToy as BotIcon,
    Person as PersonIcon,
    ContentCopy as CopyIcon,
    Code as CodeIcon,
    Clear as ClearIcon,
    Download as DownloadIcon,
    ExpandMore as ExpandMoreIcon,
    Error as ErrorIcon,
    Transform as TransformIcon,
    BugReport as BugReportIcon
} from '@mui/icons-material';
import ReplayIcon from '@mui/icons-material/Replay';

const ChatbotMain = () => {
    const [messages, setMessages] = useState([]);
    const [inputValue, setInputValue] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState('');
    const [selectedMode, setSelectedMode] = useState(''); // 'conversion' or 'error'
    const [awaitingCode, setAwaitingCode] = useState(false);
    const [awaitingError, setAwaitingError] = useState(false);
    const messagesEndRef = useRef(null);

    // Available languages for conversion
    const languages = [
        { value: 'java', label: 'Java', enabled: true },
        { value: 'javascript', label: 'JavaScript', enabled: true },
        { value: 'dwl', label: 'DWL', enabled: true }
    ];

    // Mode options
    const modes = [
        { value: 'conversion', label: 'Would You Like To Convert Your Code to Groovy?', icon: <TransformIcon />, description: 'Convert code to Groovy' },
        { value: 'error', label: 'Would You Like To Get Help With Error Solutions?', icon: <BugReportIcon />, description: 'Get help with error solutions' }
    ];

    // Initial welcome message
    useEffect(() => {
        setMessages([
            {
                id: 1,
                text: "Hey! I’m your Intswitch Assistant 😊 What can I do for you today?",
                sender: 'bot',
                timestamp: new Date(),
                showModeOptions: true
            }
        ]);
    }, []);

    // Auto scroll to bottom
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    const addMessage = (message) => {
        setMessages(prev => [...prev, { ...message, id: Date.now() + Math.random() }]);
    };

    const updateMessage = (id, updates) => {
        setMessages(prev => prev.map(msg =>
            msg.id === id ? { ...msg, ...updates } : msg
        ));
    };

    const handleModeSelect = (mode) => {
        setSelectedMode(mode);
        const modeObj = modes.find(m => m.value === mode);

        addMessage({
            text: `Selected Prompt: ${modeObj.label}`,
            sender: 'user',
            timestamp: new Date()
        });

        if (mode === 'conversion') {
            setTimeout(() => {
                addMessage({
                    text: "Great! Please select the programming language you want to convert to Groovy.",
                    sender: 'bot',
                    timestamp: new Date(),
                    showLanguageOptions: true
                });
            }, 500);
        } else if (mode === 'error') {
            setTimeout(() => {
                addMessage({
                    text: "I'll help you with error suggestions. Please paste your error message or description below:",
                    sender: 'bot',
                    timestamp: new Date()
                });
                setAwaitingError(true);
            }, 500);
        }
    };

    const handleLanguageSelect = (language) => {
        const langObj = languages.find(lang => lang.value === language);
        setSelectedLanguage(language);

        addMessage({
            text: `Selected Language: ${langObj.label}`,
            sender: 'user',
            timestamp: new Date()
        });

        setTimeout(() => {
            addMessage({
                text: `Perfect! Please paste your ${langObj.label} code that you want to convert to Groovy.`,
                sender: 'bot',
                timestamp: new Date()
            });
            setAwaitingCode(true);
        }, 500);
    };

    const convertCode = async (code, language) => {
        setIsLoading(true);

        try {
            const response = await fetch(
                "https://test-automation-tool.cfapps.us10-001.hana.ondemand.com/api/groovy/convertScriptsToGroovy",
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Basic ${btoa("intswitchjavaX92:aK7wPq93TdYxR14FbE")}`,
                    },
                    body: JSON.stringify({
                        type: language,
                        code: code,
                    }),
                }
            );

            const data = await response.json();
            return data.groovyScript || "// No output from API";
        } catch (error) {
            console.error('Conversion error:', error);
            return "// Error while converting. Please try again.";
        } finally {
            setIsLoading(false);
        }
    };

    const fetchSuggestedSolutions = async (errorDescription) => {
        setIsLoading(true);
        try {
            const response = await fetch(
                "https://test-automation-tool.cfapps.us10-001.hana.ondemand.com/api/errorsetails/getsuggestedsolution",
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": `Basic ${btoa("intswitchjavaX92:aK7wPq93TdYxR14FbE")}`,
                    },
                    body: JSON.stringify({
                        errorDescription: errorDescription,
                    }),
                }
            );

            if (response.ok) {
                const solutions = await response.json();
                return solutions;
            } else {
                console.error("Failed to fetch suggested solutions");
                return null;
            }
        } catch (error) {
            console.error("Error fetching suggested solutions:", error);
            return null;
        } finally {
            setIsLoading(false);
        }
    };

    const handleSendMessage = async () => {
        if (!inputValue.trim()) return;

        const userMessage = {
            text: inputValue,
            sender: 'user',
            timestamp: new Date()
        };

        if (awaitingCode && selectedLanguage) {
            userMessage.code = inputValue;
            userMessage.language = selectedLanguage;
        } else if (awaitingError) {
            userMessage.errorDescription = inputValue;
        }

        addMessage(userMessage);
        const currentInput = inputValue;
        setInputValue('');

        if (awaitingCode && selectedLanguage) {
            setIsLoading(true);

            // Add converting message
            const convertingMessage = {
                text: "Converting your code to Groovy...",
                sender: 'bot',
                timestamp: new Date(),
                isConverting: true
            };
            addMessage(convertingMessage);

            // Convert the code
            const convertedCode = await convertCode(currentInput, selectedLanguage);

            // Replace the converting message with the result
            setMessages(prev => prev.map(msg =>
                msg.isConverting
                    ? {
                        ...msg,
                        text: "Here's your converted Groovy code:",
                        isConverting: false,
                        code: convertedCode,
                        language: 'groovy',
                        showActions: true
                    }
                    : msg
            ));

            setAwaitingCode(false);

            setTimeout(() => {
                addMessage({
                    text: "Would you like to do something else?",
                    sender: 'bot',
                    timestamp: new Date(),
                    showModeOptions: true
                });
            }, 1000);

        } else if (awaitingError) {
            setIsLoading(true);

            // Add analyzing message
            const analyzingMessage = {
                text: "Analyzing your error and finding suggested solutions...",
                sender: 'bot',
                timestamp: new Date(),
                isAnalyzing: true
            };
            addMessage(analyzingMessage);

            // Fetch error suggestions
            const solutions = await fetchSuggestedSolutions(currentInput);

            // Replace the analyzing message with the result
            setMessages(prev => prev.map(msg =>
                msg.isAnalyzing
                    ? {
                        ...msg,
                        text: solutions ? "Here are the suggested solutions for your error:" : "Sorry, I couldn't find specific solutions for this error. Please try rephrasing or contact support.",
                        isAnalyzing: false,
                        errorSolutions: solutions,
                        showActions: true
                    }
                    : msg
            ));

            setAwaitingError(false);

            setTimeout(() => {
                addMessage({
                    text: "Would you like help with another error or convert some code?",
                    sender: 'bot',
                    timestamp: new Date(),
                    showModeOptions: true
                });
            }, 1000);

        } else {
            // Handle general conversation
            setTimeout(() => {
                addMessage({
                    text: "I can help you with code conversion or error suggestions. Please select what you'd like to do:",
                    sender: 'bot',
                    timestamp: new Date(),
                    showModeOptions: true
                });
            }, 500);
        }
    };

    const handleKeyPress = (event) => {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            handleSendMessage();
        }
    };

    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text).then(() => {
            // Could add a toast notification here
        });
    };

    const downloadCode = (code) => {
        const blob = new Blob([code], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'converted-code.groovy';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const resetChat = () => {
        setMessages([
            {
                id: 1,
                text: "Hey! I’m your Intswitch Assistant 😊 What can I do for you today?",
                sender: 'bot',
                timestamp: new Date(),
                showModeOptions: true
            }
        ]);
        setSelectedLanguage('');
        setSelectedMode('');
        setAwaitingCode(false);
        setAwaitingError(false);
        setInputValue('');
    };

    const formatTime = (timestamp) => {
        return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    return (
        <Box className="chatbot-container" sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            bgcolor: 'background.paper'
        }}>
            {/* Header */}
            <Box sx={{
                p: 2,
                borderBottom: 1,
                borderColor: 'divider',
                bgcolor: '#1565c0',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
            }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Avatar sx={{ width: 32, height: 32, bgcolor: 'rgba(255,255,255,0.2)' }}>
                        <BotIcon fontSize="small" />
                    </Avatar>
                    <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', lineHeight: 1 }}>
                            Intswitch Assistant
                        </Typography>
                        {/* <Typography variant="caption" sx={{ opacity: 0.8, lineHeight: 1 }}>
                            Code Converter & Error Helper
                        </Typography> */}
                    </Box>
                </Box>
                <Tooltip title="Reset Chat">
                    <IconButton onClick={resetChat} sx={{ color: 'white' }}>
                        <ReplayIcon fontSize="small" />
                    </IconButton>
                </Tooltip>
            </Box>

            {/* Messages */}
            <Box sx={{
                flex: 1,
                overflow: 'auto',
                p: 1,
                bgcolor: '#f5f5f5'
            }}>
                {messages.map((message) => (
                    <Box key={message.id} sx={{ mb: 2 }}>
                        <Box sx={{
                            display: 'flex',
                            justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
                            alignItems: 'flex-start',
                            gap: 1
                        }}>
                            {message.sender === 'bot' && (
                                <Avatar sx={{ width: 24, height: 24, bgcolor: '#1565c0' }}>
                                    <BotIcon fontSize="small" />
                                </Avatar>
                            )}

                            <Box sx={{ maxWidth: '80%' }}>
                                <div>
                                    <Paper
                                        elevation={1}
                                        sx={{
                                            p: 1.5,
                                            bgcolor: message.sender === 'user' ? '#a2caf8ff' : 'white',
                                            color: message.sender === 'user' ? 'white !important' : 'text.primary',
                                            borderRadius: message.sender === 'user' ? '16px 16px 4px 16px' : '16px 16px 16px 4px'
                                        }}
                                       
                                    >
                                        <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }} style={{ fontSize: '14px', marginTop: '2px', textAlign: 'left' }}>
                                            {message.text}
                                        </Typography>

                                        {(message.isConverting || message.isAnalyzing) && (
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                                                <CircularProgress size={16} />
                                            </Box>
                                        )}

                                        {/* Code Display - Only show in bot messages */}
                                        {message.code && message.sender === 'bot' && (
                                            <Box sx={{ mt: 1 }}>
                                                <Paper sx={{ bgcolor: '#1e1e1e', p: 1, borderRadius: 1 }}>
                                                    <Box sx={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center',
                                                        mb: 1
                                                    }}>
                                                        <Typography variant="caption" sx={{ color: '#888', fontFamily: 'monospace' }}>
                                                            {message.language || 'code'}
                                                        </Typography>
                                                        {message.showActions && (
                                                            <Box>
                                                                <Tooltip title="Copy Code">
                                                                    <IconButton
                                                                        size="small"
                                                                        onClick={() => copyToClipboard(message.code)}
                                                                        sx={{ color: '#888' }}
                                                                    >
                                                                        <CopyIcon fontSize="small" />
                                                                    </IconButton>
                                                                </Tooltip>
                                                                <Tooltip title="Download">
                                                                    <IconButton
                                                                        size="small"
                                                                        onClick={() => downloadCode(message.code)}
                                                                        sx={{ color: '#888' }}
                                                                    >
                                                                        <DownloadIcon fontSize="small" />
                                                                    </IconButton>
                                                                </Tooltip>
                                                            </Box>
                                                        )}
                                                    </Box>
                                                    <Typography
                                                        variant="caption"
                                                        component="pre"
                                                        sx={{
                                                            color: '#f8f8f2',
                                                            fontFamily: 'monospace',
                                                            whiteSpace: 'pre-wrap',
                                                            wordBreak: 'break-word',
                                                            display: 'flex',
                                                            textAlign: 'left'
                                                        }}
                                                    >
                                                        {message.code}
                                                    </Typography>
                                                </Paper>
                                            </Box>
                                        )}

                                        {/* Error Solutions Display */}
                                        {message.errorSolutions && message.sender === 'bot' && (
                                            <Box sx={{ mt: 2, textAlign: 'left' }}>
                                                {/* Human Readable Message */}
                                                {message.errorSolutions.humanReadableMessage && (
                                                    <Alert severity="info" sx={{ mb: 2 }}>
                                                        <AlertTitle>Error Analysis</AlertTitle>
                                                        {message.errorSolutions.humanReadableMessage}
                                                    </Alert>
                                                )}

                                                {/* Error Causes and Solutions */}
                                                {message.errorSolutions.errorCauseSolutions && message.errorSolutions.errorCauseSolutions.length > 0 && (
                                                    <Box>
                                                        <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                                                            Possible Causes & Solutions:
                                                        </Typography>
                                                        {message.errorSolutions.errorCauseSolutions.map((item, index) => (
                                                            <Accordion key={index} sx={{ mb: 1 }}>
                                                                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                                        <ErrorIcon fontSize="small" color="warning" />
                                                                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                                                                            Cause {index + 1}
                                                                        </Typography>
                                                                    </Box>
                                                                </AccordionSummary>
                                                                <AccordionDetails>
                                                                    <Box>
                                                                        {/* Cause */}
                                                                        <Typography variant="caption" sx={{ display: 'block', mb: 1 }}>
                                                                            <strong>Cause:</strong>{" "}
                                                                            <Typography component="span" variant="caption" sx={{ fontWeight: 'normal' }}>
                                                                                {item.cause}
                                                                            </Typography>
                                                                        </Typography>

                                                                        {/* Solution */}
                                                                        <Typography variant="caption" sx={{ display: 'block', color: 'success.main' }}>
                                                                            <strong>Solution:</strong>{" "}
                                                                            <Typography component="span" variant="caption" sx={{ fontWeight: 'normal' }}>
                                                                                {item.solution}
                                                                            </Typography>
                                                                        </Typography>
                                                                    </Box>

                                                                </AccordionDetails>
                                                            </Accordion>
                                                        ))}
                                                    </Box>
                                                )}

                                                {/* Source Information */}
                                                {message.errorSolutions.source && (
                                                    <Typography variant="caption" sx={{ display: 'block', mt: 1, color: 'text.secondary' }}>
                                                        Source: {message.errorSolutions.source}
                                                    </Typography>
                                                )}
                                            </Box>
                                        )}
                                    </Paper>
                                </div>

                                {/* Mode Selection Options */}
                                <div>{message.showModeOptions && (
                                    <Box sx={{ mt: 1.5, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                        {modes.map((mode) => (
                                            <Chip
                                                key={mode.value}
                                                label={mode.label}
                                                onClick={() => handleModeSelect(mode.value)}
                                                variant="outlined"
                                                size="small"
                                                sx={{
                                                    bgcolor: "white",
                                                    '&:hover': {  bgcolor: '#6a8db6ff !important',
                                                     color: 'white'
                                                    },
                                                    justifyContent: "flex-start",
                                                    textAlign: "left",
                                                    width: '100%',
                                                    borderRadius: "12px",
                                                    height: 'auto',
                                                    minHeight: '44px',
                                                    py: 1.5,
                                                    color: "#37474f",
                                                    fontSize: '0.9rem',
                                                    '& .MuiChip-label': {
                                                        display: 'block',
                                                        whiteSpace: 'normal',
                                                        padding: '0 12px'
                                                    },
                                                    '& .MuiChip-icon': {
                                                        color: 'inherit',
                                                        marginLeft: '12px'
                                                    }
                                                }}
                                            />
                                        ))}
                                    </Box>
                                )}</div>

                                {/* Language Selection Options */}
                                <div>{message.showLanguageOptions && (
                                    <Box sx={{ mt: 1.5, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                        {languages.map((lang) => (
                                            <Chip
                                                key={lang.value}
                                                label={lang.label}
                                                onClick={() => lang.enabled && handleLanguageSelect(lang.value)}
                                                disabled={!lang.enabled}
                                                variant="outlined"
                                                size="small"
                                                icon={<CodeIcon />}
                                                sx={{
                                                    bgcolor: "white",
                                                    '&:hover': {  bgcolor: '#6a8db6ff !important',
                                                     color: 'white'
                                                    },
                                                    justifyContent: "flex-start",
                                                    textAlign: "left",
                                                    width: '100%',
                                                    borderRadius: "10px",
                                                    height: '2rem',
                                                    color: "black",
                                                    '& .MuiChip-label': {
                                                        display: 'block',
                                                        whiteSpace: 'normal',
                                                    }
                                                }}
                                            />
                                        ))}
                                    </Box>
                                )}</div>

                                <Typography variant="caption" sx={{
                                    color: 'text.secondary',
                                    display: 'block',
                                    textAlign: message.sender === 'user' ? 'right' : 'left',
                                    mt: 0.5,
                                    px: 1
                                }}>
                                    {formatTime(message.timestamp)}
                                </Typography>
                            </Box>

                            {message.sender === 'user' && (
                                <Avatar sx={{ width: 24, height: 24, bgcolor: '#4caf50' }}>
                                    <PersonIcon fontSize="small" />
                                </Avatar>
                            )}
                        </Box>
                    </Box>
                ))}
                <div ref={messagesEndRef} />
            </Box>

            {/* Input Area */}
            <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider', bgcolor: 'white' }}>
                <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
                    <TextField
                        fullWidth
                        multiline
                        maxRows={4}
                        placeholder={
                            awaitingCode
                                ? `Paste your ${selectedLanguage} code here...`
                                : awaitingError
                                    ? "Paste your error message or description here..."
                                    : "Type your message..."
                        }
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyPress={handleKeyPress}
                        disabled={isLoading}
                        variant="outlined"
                        size="small"
                        sx={{
                            '& .MuiOutlinedInput-root': {
                                borderRadius: 2
                            }
                        }}
                    />
                    <Button
                        variant="contained"
                        onClick={handleSendMessage}
                        disabled={!inputValue.trim() || isLoading}
                        sx={{
                            minWidth: 48,
                            height: 40,
                            bgcolor: '#1565c0',
                            '&:hover': { bgcolor: '#6a8db6ff' },
                            borderRadius: 2
                        }}
                    >
                        {isLoading ? (
                            <CircularProgress size={20} sx={{ color: 'white' }} />
                        ) : (
                            <SendIcon />
                        )}
                    </Button>
                </Box>
            </Box>
        </Box>
    );
};

export default ChatbotMain;