import React, { useState } from "react";
import APIEndpoint from "../config";
import CircularProgress from "@mui/material/CircularProgress";
import { DiffEditor } from "@monaco-editor/react";
import xmlFormatter from "xml-formatter";

const ComparisonPayloadTable = ({
  formdata,
  comparisons,
  onRowClick,
  onViewPayload,
  hoveredRow,
  setHoveredRow,
}) => {
  const [diffViewerOpen, setDiffViewerOpen] = useState(false);
  const [currentComparison, setCurrentComparison] = useState(null);
  const [leftPayload, setLeftPayload] = useState("");
  const [rightPayload, setRightPayload] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const formatXml = (xmlString) => {
    try {
      return xmlFormatter(xmlString, {
        indentation: "  ",
        collapseContent: false,
        lineSeparator: "\n",
      });
    } catch (e) {
      console.error("Error formatting XML:", e);
      return xmlString;
    }
  };

  const handleViewDiff = async (comparison) => {
    // Don't proceed if there's a match
    if (comparison.match === true || comparison.match === "true") {
      return;
    }
    
    setIsLoading(true);
    setCurrentComparison(comparison);

    try {
      const token = localStorage.getItem("token");

      // Fetch both payloads in parallel
      const [poRes, cpiRes] = await Promise.all([
        fetch(`${APIEndpoint}${comparison.poOutputPayload}`, {
          headers: {   "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,},
        }),
        fetch(`${APIEndpoint}${comparison.cpiPayload}`, {
          headers: {   "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,},
        }),
      ]);

      const [poText, cpiText] = await Promise.all([
        poRes.text(),
        cpiRes.text(),
      ]);

      // Format both XML payloads
      setLeftPayload(formatXml(poText));
      setRightPayload(formatXml(cpiText));
      setDiffViewerOpen(true);
    } catch (error) {
      console.error("Error loading payloads:", error);
      setLeftPayload("Error loading PO payload");
      setRightPayload("Error loading CPI payload");
      setDiffViewerOpen(true);
    } finally {
      setIsLoading(false);
    }
  };

  // For single test case, we'll only have one comparison in the array
  const comparison = comparisons[0] || {};

  return (
    <>
      <div className="table-container">
        <table className="comparison-table">
          <thead>
            <tr>
              <th>Test Case</th>
              <th>File Name</th>
              <th>PO Input Payload</th>
              <th>PO Output Payload</th>
              <th>CPI Output Payload</th>
              <th>Test Result</th>
              <th>Actions</th>
              <th>CPI Output</th>
              <th>PO Output</th>
            </tr>
          </thead>
          <tbody>
            {comparisons.map((comparison, index) => {
              const isMatch = comparison.match === true || comparison.match === "true";
              
              return (
                <tr
                  key={index}
                  className={`${isMatch ? "Passed" : "Failed"} ${
                    hoveredRow === index ? "row-hover" : ""
                  }`}
                  onClick={() => onRowClick(comparison)}
                  onMouseEnter={() => setHoveredRow(index)}
                  onMouseLeave={() => setHoveredRow(null)}
                  style={{ cursor: "pointer" }}
                >
                  <td>Test {index + 1}</td>
                  <td>{comparison.fileName || `File ${index + 1}`}</td>
                  
                  <td>
                    <button
                      className="view-payload-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewPayload(comparison.poInputPayload, "PO Input");
                      }}
                    >
                      View PO Input
                    </button>
                  </td>
                  <td>
                    <button
                      className="view-payload-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewPayload(comparison.poOutputPayload, "PO Output");
                      }}
                    >
                      View PO Output
                    </button>
                  </td>
                  <td>
                    <button
                      className="view-payload-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewPayload(comparison.cpiPayload, "CPI Output");
                      }}
                    >
                      View CPI Output
                    </button>
                  </td>
                  <td>
                    <div
                      className={`status-badge ${
                        isMatch ? "status-match" : "status-difference"
                      }`}
                    >
                      {isMatch ? "Match" : "Difference"}
                    </div>
                  </td>
                  <td>
                    <button
                      className={`details-button ${isMatch ? "disabled-button" : ""}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewDiff(comparison);
                      }}
                      disabled={isMatch}
                    >
                      {isMatch ? "No Differences" : "View Diff"}
                    </button>
                  </td>
                  <td>{comparison.cpiOutput ? comparison.cpiOutput : "Yes"}</td>
                  <td>{comparison.poOutput ? comparison.poOutput : "Yes"}</td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Diff Viewer Modal */}
      {diffViewerOpen && (
        <div className="modal-overlay">
          <div className="modal-content diff-modal">
            <div className="modal-header">
              <h3>
                Payload Comparison: {currentComparison?.interfaceName}
                <small> (Message ID: {currentComparison?.messageId})</small>
              </h3>
              <button
                className="close-modal"
                onClick={() => setDiffViewerOpen(false)}
              >
                ×
              </button>
            </div>

            <div className="diff-container">
              {isLoading ? (
                <div className="loading-message">
                  <CircularProgress />
                  <p>Loading payloads for comparison...</p>
                </div>
              ) : (
                leftPayload &&
                rightPayload && (
                  <DiffEditor
                    key={currentComparison?.id}
                    height="70vh"
                    language="xml"
                    original={leftPayload}
                    modified={rightPayload}
                    options={{
                      readOnly: true,
                      renderSideBySide: true,
                      enableSplitViewResizing: true,
                      scrollBeyondLastLine: false,
                      minimap: { enabled: false },
                    }}
                  />
                )
              )}
            </div>

            <div className="diff-legend">
              <div className="legend-item">
                <span className="legend-color po-color"></span>
                <span>PO Output Payload</span>
              </div>
              <div className="legend-item">
                <span className="legend-color cpi-color"></span>
                <span>CPI Output Payload</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ComparisonPayloadTable;