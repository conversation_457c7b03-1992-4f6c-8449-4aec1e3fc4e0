import React, { useState } from "react";
import { MenuItem, Select, FormControl } from "@mui/material";
import API_ENDPOINT from "../../config";

const DeliveryAssurance = ({ onClose, setSnackbar, artifactId }) => {
  const [deliveryData, setDeliveryData] = useState({
    qualityOfService: { param: "", value: "Exactly Once" },
    temporaryStorage: { param: "", value: "Data Store" },
    lockTimeout: { param: "", value: "10" },
    pollInterval: { param: "", value: "1" },
    expirationPeriod: { param: "", value: "30" },
    retryInterval: { param: "", value: "1" },
    exponentialBackoff: { param: "", value: true },
    maximumRetryInterval: { param: "", value: "60" },
    numberOfConcurrentProcesses: { param: "", value: "1" },
    deadLetterQueue: { param: "", value: false },
    compressStoredMessage: { param: "", value: false },
    encryptMessageDuringPersistence: { param: "", value: false }
  });
  const [loading, setLoading] = useState(false);

  const handleCancel = () => {
    if (onClose) onClose();
  };

  const handleDeliveryChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    
    setDeliveryData(prev => ({
      ...prev,
      [name]: { ...prev[name], value: newValue }
    }));
  };

  const handleParamChange = (e) => {
    const { name, value } = e.target;
    setDeliveryData(prev => ({
      ...prev,
      [name]: { ...prev[name], param: value }
    }));
  };

  const handleSubmit = async () => {
    setLoading(true);
    
    try {
      // Create an array of all parameter updates
      const updates = Object.entries(deliveryData)
        .map(([key, data]) => ({ name: data.param, value: data.value }))
        .filter(update => update.name); // Only include parameters with names

      if (updates.length === 0) {
        setSnackbar({
          open: true,
          message: "Please enter at least one parameter name",
          severity: "error"
        });
        setLoading(false);
        return;
      }

      // Execute all updates sequentially
      let lastResponse = null;
      for (const update of updates) {
        const endpoint = `${API_ENDPOINT}/api/updateParam/${artifactId}/${update.name}`;
        
        const response = await fetch(endpoint, {
          method: "PUT",
          headers: {
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            parameterValue: update.value,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update parameter ${update.name}`);
        }
        
        lastResponse = await response.json();
      }

      if (lastResponse && lastResponse.status) {
        setSnackbar({
          open: true,
          message: lastResponse.status,
          severity: "success"
        });
      } else {
        setSnackbar({
          open: true,
          message: "Parameters updated successfully",
          severity: "success"
        });
      }
    } catch (error) {
      console.error("Error updating parameters:", error);
      setSnackbar({
        open: true,
        message: error.message || "Failed to update some parameters",
        severity: "error"
      });
    } finally {
      setLoading(false);
    }
  };

  // Determine which fields to show based on Quality of Service
  const showExactlyOnceFields = deliveryData.qualityOfService.value === "Exactly Once";
  const showAtLeastOnceFields = deliveryData.qualityOfService.value === "At Least Once";
  const showBestEffortFields = deliveryData.qualityOfService.value === "Best Effort";
  const showHandledByFlowFields = deliveryData.qualityOfService.value === "Handled by Integration Flow";
  
  // Show JMS Queue specific fields (only for Exactly Once)
  const showJMSQueueFields = deliveryData.temporaryStorage.value === "JMS Queue" && showExactlyOnceFields;
  
  // Show Maximum Retry Interval only when Exponential Backoff is checked
  const showMaxRetryInterval = deliveryData.exponentialBackoff.value && (showExactlyOnceFields || showAtLeastOnceFields || showJMSQueueFields);

  const renderField = (label, name, type = "text", required = false, options = null, isCheckbox = false) => {
    return (
      <div style={{ display: 'grid', gridTemplateColumns: '250px 1fr 200px', gap: '10px', alignItems: 'center', marginBottom: '15px' }}>
        <label style={{ color: '#333', fontSize: '14px', textAlign: 'right' }}>
          {label}:
          {required && <span style={{ color: 'red' }}> *</span>}
        </label>
        <input
          type="text"
          name={name}
          value={deliveryData[name].param}
          onChange={handleParamChange}
          placeholder="Define Parameter"
          style={{
            padding: '8px 12px',
            border: '1px solid #ccc',
            borderRadius: '4px',
            fontSize: '14px'
          }}
        />
        {isCheckbox ? (
          <input
            type="checkbox"
            name={name}
            checked={deliveryData[name].value}
            onChange={handleDeliveryChange}
            style={{
              width: '20px',
              height: '20px'
            }}
          />
        ) : options ? (
          <FormControl size="small" style={{ minWidth: '200px' }}>
            <Select
              name={name}
              value={deliveryData[name].value}
              onChange={handleDeliveryChange}
              style={{ fontSize: '14px' }}
            >
              {options.map(option => (
                <MenuItem key={option} value={option}>{option}</MenuItem>
              ))}
            </Select>
          </FormControl>
        ) : (
          <input
            type={type}
            name={name}
            value={deliveryData[name].value}
            onChange={handleDeliveryChange}
            placeholder="Define Value"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
        )}
      </div>
    );
  };

  return (
    <div>
      <h4 style={{ 
        display: 'flex',
        color: '#6c757d', 
        fontSize: '14px', 
        fontWeight: 'bold',
        marginBottom: '20px',
        textTransform: 'uppercase'
      }}>
        DELIVERY ASSURANCE PARAMETERS
      </h4>
      
      <div style={{ display: 'grid', gap: '5px' }}>
        {/* Quality of Service - Always shown */}
        {renderField(
          "Quality Of Service", 
          "qualityOfService", 
          "text", 
          false, 
          ["Exactly Once", "At Least Once", "Best Effort", "Handled by Integration Flow"]
        )}

        {/* Conditional fields based on Quality of Service */}
        {(showExactlyOnceFields || showAtLeastOnceFields) && (
          <>
            {showExactlyOnceFields && renderField(
              "Temporary Storage", 
              "temporaryStorage", 
              "text", 
              false, 
              ["Data Store", "JMS Queue"]
            )}
            
            {showAtLeastOnceFields && renderField(
              "Temporary Storage", 
              "temporaryStorage", 
              "text", 
              false, 
              ["Data Store"]
            )}
            
            {!showJMSQueueFields && (
              <>
                {renderField("Lock Timeout (in min)", "lockTimeout", "text", true)}
                {renderField("Poll Interval (in s)", "pollInterval", "text", true)}
              </>
            )}
            
            {showJMSQueueFields && (
              renderField("Number of Concurrent Processes", "numberOfConcurrentProcesses", "text", true)
            )}
            
            {renderField("Expiration Period (in d)", "expirationPeriod", "text", true)}
            {renderField("Retry Interval (in min)", "retryInterval", "text", true)}
            {renderField("Exponential Backoff", "exponentialBackoff", "text", true, null, true)}
            
            {showMaxRetryInterval && renderField("Maximum Retry Interval (in min)", "maximumRetryInterval", "text", true)}
            
            {showJMSQueueFields && (
              <>
                {renderField("Dead-Letter Queue", "deadLetterQueue", "text", false, null, true)}
                {renderField("Compress Stored Message", "compressStoredMessage", "text", true, null, true)}
              </>
            )}
            
            {renderField("Encrypt Message During Persistence", "encryptMessageDuringPersistence", "text", true, null, true)}
          </>
        )}

        {/* Best Effort and Handled by Integration Flow show only Quality of Service */}
      </div>
      
      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '10px',
        padding: '15px 20px',
        borderTop: '1px solid #dee2e6',
        backgroundColor: '#f8f9fa',
        marginTop: '20px'
      }}>
        <button
          onClick={handleSubmit}
          disabled={loading}
          style={{
            padding: '8px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer',
            fontWeight: 'bold',
            opacity: loading ? 0.7 : 1
          }}
        >
          {loading ? 'Updating...' : 'OK'}
        </button>
        <button
          onClick={handleCancel}
          style={{
            padding: '8px 20px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default DeliveryAssurance;