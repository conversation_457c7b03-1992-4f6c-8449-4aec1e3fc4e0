import React, { useState } from "react";
import axios from "axios";
import APIEndpoint from "../config";

const MMAPfile = () => {
  const [artifactId, setArtifactId] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [sourceXsd, setSourceXsd] = useState(null);
  const [targetXsd, setTargetXsd] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("");

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file && file.name.endsWith(".mmap")) {
      setSelectedFile(file);
      setMessage("");
    } else {
      setSelectedFile(null);
      setMessage("Please select a valid .mmap file");
    }
  };

  const handleSourceXsdChange = (event) => {
    const file = event.target.files[0];
    if (file && file.name.endsWith(".xsd")) {
      setSourceXsd(file);
      setMessage("");
    } else {
      setSourceXsd(null);
      setMessage("Please select a valid .xsd file for source");
    }
  };

  const handleTargetXsdChange = (event) => {
    const file = event.target.files[0];
    if (file && file.name.endsWith(".xsd")) {
      setTargetXsd(file);
      setMessage("");
    } else {
      setTargetXsd(null);
      setMessage("Please select a valid .xsd file for target");
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!artifactId || !selectedFile || !sourceXsd || !targetXsd) {
      setMessage("Please fill all fields and select all required files");
      return;
    }

    setIsLoading(true);
    setMessage("");

    const formData = new FormData();
    formData.append("artifactId", artifactId);
    formData.append("mappingFile", selectedFile);
    formData.append("sourceXsd", sourceXsd);
    formData.append("targetXsd", targetXsd);

    try {
      const response = await axios.post(
        `${APIEndpoint}/api/groovy/addmappingxsdresource`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Basic ${localStorage.getItem("basicAuth")}`,
          },
        }
      );

      // Check if the response has overallStatus property and it's "success"
      if (response.data && response.data.overallStatus === "success") {
        // Create a detailed success message from the response
        const successMessage = `
        Upload successful!
        - Source XSD: ${response.data.sourceXsdMessage}
        - Target XSD: ${response.data.targetXsdMessage}
        - Mapping File: ${response.data.mappingFileMessage}
      `;
        setMessage(successMessage);

        // Optional: Clear the form after successful upload
        setArtifactId("");
        setSelectedFile(null);
        setSourceXsd(null);
        setTargetXsd(null);
      } else {
        setMessage(
          response.data?.message ||
            "Upload completed but with unexpected response"
        );
      }
    } catch (error) {
      if (error.response) {
        if (error.response.data) {
          setMessage(
            error.response.data.message ||
              error.response.data.overallStatus ||
              `Error: ${error.response.status} - ${error.response.statusText}`
          );
        } else {
          setMessage(
            `Error: ${error.response.status} - ${error.response.statusText}`
          );
        }
      } else if (error.request) {
        setMessage("No response received from server. Please try again.");
      } else {
        setMessage("Error setting up request: " + error.message);
      }
      console.error("Error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      style={{
        padding: "20px",
        minHeight: "100vh",
        backgroundColor: "white",
        position: "relative",
      }}
    >
      <div
        style={{
          marginBottom: "30px",
          borderBottom: "2px solid #e5e7eb",
          paddingBottom: "15px",
        }}
      >
        <h1
          style={{
            fontSize: "28px",
            fontWeight: "bold",
            color: "#1f2937",
            margin: "0 0 8px 0",
          }}
        >
          Mapping Automation
        </h1>
        <p
          style={{
            color: "#6b7280",
            fontSize: "16px",
            margin: "0",
          }}
        >
          Upload automatically generated .mmap files and XSDs to integration
          flows
        </p>
      </div>
      <div
        style={{
          maxWidth: "72%",
          margin: "0 auto",
          padding: "2rem",
          border: "1px solid #e0e0e0",
          borderRadius: "8px",
          boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
        }}
      >
        <div
          style={{ display: "flex", flexDirection: "column", gap: "1.5rem" }}
        >
          {/* Artifact ID Input */}
          <div>
            <label
              htmlFor="artifact-id"
              style={{
                display: "flex",
                marginBottom: "0.5rem",
                fontWeight: "bold",
                color: "#555",
              }}
            >
              Artifact ID:
            </label>
            <input
              id="artifact-id"
              type="text"
              value={artifactId}
              onChange={(e) => setArtifactId(e.target.value)}
              placeholder="Enter artifact ID"
              style={{
                width: "100%",
                padding: "12px",
                border: "2px solid #ddd",
                borderRadius: "4px",
                fontSize: "16px",
                outline: "none",
                transition: "border-color 0.3s",
              }}
              onFocus={(e) => (e.target.style.borderColor = "#85adeeff")}
              onBlur={(e) => (e.target.style.borderColor = "#ddd")}
            />
          </div>

          {/* Source XSD Input */}
          <div>
            <label
              htmlFor="source-xsd"
              style={{
                display: "flex",
                marginBottom: "0.5rem",
                fontWeight: "bold",
                color: "#555",
              }}
            >
              Source XSD File:
            </label>
            <input
              id="source-xsd"
              type="file"
              accept=".xsd"
              onChange={handleSourceXsdChange}
              style={{
                width: "100%",
                padding: "12px",
                border: "2px solid #ddd",
                borderRadius: "4px",
                fontSize: "16px",
                outline: "none",
                backgroundColor: "#fafafa",
              }}
            />
            {sourceXsd && (
              <p
                style={{
                  marginTop: "0.5rem",
                  color: "#4CAF50",
                  fontSize: "14px",
                }}
              >
                Selected: {sourceXsd.name}
              </p>
            )}
          </div>

          {/* Target XSD Input */}
          <div>
            <label
              htmlFor="target-xsd"
              style={{
                display: "flex",
                marginBottom: "0.5rem",
                fontWeight: "bold",
                color: "#555",
              }}
            >
              Target XSD File:
            </label>
            <input
              id="target-xsd"
              type="file"
              accept=".xsd"
              onChange={handleTargetXsdChange}
              style={{
                width: "100%",
                padding: "12px",
                border: "2px solid #ddd",
                borderRadius: "4px",
                fontSize: "16px",
                outline: "none",
                backgroundColor: "#fafafa",
              }}
            />
            {targetXsd && (
              <p
                style={{
                  marginTop: "0.5rem",
                  color: "#4CAF50",
                  fontSize: "14px",
                }}
              >
                Selected: {targetXsd.name}
              </p>
            )}
          </div>

          {/* Mapping File Input */}
          <div>
            <label
              htmlFor="file-input"
              style={{
                display: "flex",
                marginBottom: "0.5rem",
                fontWeight: "bold",
                color: "#555",
              }}
            >
              Select MMAP File:
            </label>
            <input
              id="file-input"
              type="file"
              accept=".mmap"
              onChange={handleFileChange}
              style={{
                width: "100%",
                padding: "12px",
                border: "2px solid #ddd",
                borderRadius: "4px",
                fontSize: "16px",
                outline: "none",
                backgroundColor: "#fafafa",
              }}
            />
            {selectedFile && (
              <p
                style={{
                  marginTop: "0.5rem",
                  color: "#4CAF50",
                  fontSize: "14px",
                }}
              >
                Selected: {selectedFile.name}
              </p>
            )}
          </div>

          {/* Submit Button */}
          <button
            onClick={handleSubmit}
            type="submit"
            disabled={isLoading}
            style={{
              padding: "15px 30px",
              backgroundColor: isLoading ? "#ccc" : "#3b82f6",
              color: "white",
              border: "none",
              borderRadius: "4px",
              fontSize: "16px",
              fontWeight: "bold",
              cursor: isLoading ? "not-allowed" : "pointer",
              transition: "background-color 0.3s",
              marginTop: "1rem",
            }}
            onMouseOver={(e) => {
              if (!isLoading) e.target.style.backgroundColor = "#85adeeff";
            }}
            onMouseOut={(e) => {
              if (!isLoading) e.target.style.backgroundColor = "#3b82f6";
            }}
          >
            {isLoading ? "Uploading..." : "Submit"}
          </button>
        </div>

        {/* Message Display */}
        {message && (
          <div
            style={{
              marginTop: "1rem",
              padding: "10px",
              borderRadius: "4px",
              textAlign: "left", // Changed from "center" to "left"
              whiteSpace: "pre-line", // This will preserve line breaks
              backgroundColor: message.includes("success")
                ? "#d4edda"
                : "#f8d7da",
              color: message.includes("success") ? "#155724" : "#721c24",
              border: `1px solid ${
                message.includes("success") ? "#c3e6cb" : "#f5c6cb"
              }`,
            }}
          >
            {message}
          </div>
        )}
      </div>
    </div>
  );
};

export default MMAPfile;
