/* BasicScheduler.css */

/* Main container styling */
.scheduler-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Form group styling */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: flex;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Radio button group styling */
.schedule-type {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.time {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.form-check {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.form-check input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: #3498db;
  cursor: pointer;
}

.form-check label {
  margin: 0;
  cursor: pointer;
}

/* Active scheduler styling */
.on-day, .recur {
  padding: 15px;
  background-color: #f8fafc;
  border-radius: 6px;
  margin-top: 15px;
  border-left: 3px solid transparent;
}

.on-day.active {
  border-left-color: #3498db;
}

/* Button styling */
.btn-group {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 25px;
}

.boldBtn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.boldBtn.btn-primary {
  background-color: #3498db;
  color: white;
}

.boldBtn.btn-primary:hover {
  background-color: #2980b9;
}

.boldBtn.btn-dark {
  background-color: #34495e;
  color: white;
}

.boldBtn.btn-dark:hover {
  background-color: #2c3e50;
}

.boldBtn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Loading overlay */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Date and time picker styling */
.date-time-pickers {
  display: flex;
  gap: 20px;
  margin-top: 15px;
}

.date-time-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-time-group label {
  min-width: 100px;
}

/* Snackbar positioning */
.MuiSnackbar-root {
  top: 20px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .schedule-type {
    flex-direction: column;
  }
  
  .date-time-pickers {
    flex-direction: column;
    gap: 15px;
  }
  
  .btn-group {
    flex-direction: column;
  }
  
  .boldBtn {
    width: 100%;
  }
}