import React from 'react'
// import HTTPConfigurationParam from './HTTPConfigurationParam';
import XIConfigurationParam from './XIConfigurationParam';

const ConfigureXI = ({ isOpen, onClose, iFlowName }) => {
  if (!isOpen) return null;

  return (
    <div className="scheduler-modal-overlay">
      <div className="scheduler-modal-content" style={{maxWidth:"50rem"}}>
        <div className="scheduler-modal-header">
          <h3>Configure XI for {iFlowName}</h3>
        </div>
        <div className="scheduler-modal-body">
          <XIConfigurationParam artifactId={iFlowName} onClose={onClose} />
        </div>
      </div>
    </div>
  );
}

export default ConfigureXI