/* ConfigureSchedulerModal.css */
.scheduler-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.scheduler-modal-content {
  margin-top: 5rem;
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 538px;
  max-height: 85vh;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease-out;
}

.scheduler-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eaeaea;
}

.scheduler-modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.4rem;
}

.scheduler-close-button {
  background: none;
  border: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: #7f8c8d;
  transition: color 0.2s;
  padding: 5px 10px;
}

.scheduler-close-button:hover {
  color: #e74c3c;
}

.scheduler-modal-body {
    display: flex
;
    align-content: stretch;
    justify-content: center;
  padding: 10px 0;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scheduler-modal-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 15px;
}

.scheduler-tab {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
}

.scheduler-tab:hover {
  color: #333;
}

.scheduler-tab.active {
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
  font-weight: bold;
}

.parameters-config {
  padding: 15px;
}