import React, { useState } from "react";
import APIEndpoint from "../config";
import CircularProgress from "@mui/material/CircularProgress";
import { DiffEditor } from "@monaco-editor/react";
import xmlFormatter from "xml-formatter";
import ChatIcon from '@mui/icons-material/Chat';
const SingleTestCaseResultsTable = ({
  formdata,
  comparisons,
  onRowClick,
  onViewPayload,
  hoveredRow,
  setHoveredRow,
}) => {
  const [diffViewerOpen, setDiffViewerOpen] = useState(false);
  const [currentComparison, setCurrentComparison] = useState(null);
  const [leftPayload, setLeftPayload] = useState("");
  const [rightPayload, setRightPayload] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Error modal states
  const [errorModalOpen, setErrorModalOpen] = useState(false);
  const [currentError, setCurrentError] = useState("");
  const [suggestedSolutions, setSuggestedSolutions] = useState(null);
  const [solutionsExpanded, setSolutionsExpanded] = useState(false);
  const [loadingSolutions, setLoadingSolutions] = useState(false);

  const formatXml = (xmlString) => {
    if (!xmlString || typeof xmlString !== 'string') {
      return xmlString;
    }

    try {
      if (xmlString.trim().startsWith('<')) {
        return xmlFormatter(xmlString, {
          indentation: '  ',
          collapseContent: false,
          lineSeparator: '\n',
        });
      }
      return xmlString;
    } catch (e) {
      console.error('Error formatting XML:', e);
      return xmlString;
    }
  };

  const handleViewDiff = async (comparison) => {
    setIsLoading(true);
    setCurrentComparison(comparison);

    try {
      const [poRes, cpiRes] = await Promise.all([
        fetch(`${APIEndpoint}${comparison.poOutputPayload}`, {
          headers: { "Authorization": `Basic ${localStorage.getItem("basicAuth")}`},
        }),
        fetch(`${APIEndpoint}${comparison.cpiPayload}`, {
          headers: { "Authorization": `Basic ${localStorage.getItem("basicAuth")}`},
        }),
      ]);

      const [poText, cpiText] = await Promise.all([
        poRes.text(),
        cpiRes.text(),
      ]);

      setLeftPayload(formatXml(poText));
      setRightPayload(formatXml(cpiText));
      setDiffViewerOpen(true);
    } catch (error) {
      console.error("Error loading payloads:", error);
      setLeftPayload("Error loading PO payload");
      setRightPayload("Error loading CPI payload");
      setDiffViewerOpen(true);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSuggestedSolutions = async (errorDescription) => {
    setLoadingSolutions(true);
    try {
      const response = await fetch(
        "https://test-automation-tool.cfapps.us10-001.hana.ondemand.com/api/errorsetails/getsuggestedsolution",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
          },
          body: JSON.stringify({
            errorDescription: errorDescription,
          }),
        }
      );

      if (response.ok) {
        const solutions = await response.json();
        setSuggestedSolutions(solutions);
      } else {
        console.error("Failed to fetch suggested solutions");
        setSuggestedSolutions(null);
      }
    } catch (error) {
      console.error("Error fetching suggested solutions:", error);
      setSuggestedSolutions(null);
    } finally {
      setLoadingSolutions(false);
    }
  };

  const handleViewError = (error) => {
    setCurrentError(error);
    setSuggestedSolutions(null);
    setSolutionsExpanded(false);
    setErrorModalOpen(true);
    fetchSuggestedSolutions(error);
  };

  const handleToggleSolutions = () => {
    if (!solutionsExpanded && !suggestedSolutions) {
      fetchSuggestedSolutions(currentError);
    }
    setSolutionsExpanded(!solutionsExpanded);
  };

  // For single test case, we'll only have one comparison in the array
  const comparison = comparisons[0] || {};

  return (
    <>
    <div className="summary-stats">
            <div className="stat-item">
              <span className="stat-label">Total Comparisons:</span>
              <span className="stat-value">{comparisons.length}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Matches:</span>
              <span className="stat-value match">
                {comparisons.passed?0:comparisons.filter((c) => c.match).length}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Cannot be Processed:</span>
              <span className="stat-value match">
                {comparisons.unprocessed?0:comparisons.filter((c) => c.match).length}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Differences:</span>
              <span className="stat-value no-match">
                {comparisons.failed?0:comparisons.filter((c) => c.match).length}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Success Rate:</span>
              <span className="stat-value">
                {comparisons.successRate?0:comparisons.filter((c) => c.match).length}
                %
              </span>
            </div>
          </div>

      <div className="table-container">
        <table className="comparison-table">
          <thead>
            <tr>
              <th>Test Case</th>
              <th>Message ID</th>
              <th>Interface Name</th>
              <th>Flow Name</th>
              <th>PO Input Payload</th>
              <th>PO Output Payload</th>
              <th>CPI Output Payload</th>
              <th>Test Result</th>
              <th>Actions</th>
              <th>CPI Output</th>
              <th>PO Output</th>
              <th>Status</th>
              <th>Error</th>
            </tr>
          </thead>
          <tbody>
            <tr
              className={`${comparison.match ? "Passed" : "Failed"} ${
                hoveredRow === 0 ? "row-hover" : ""
              }`}
              onClick={() => onRowClick(comparison)}
              onMouseEnter={() => setHoveredRow(0)}
              onMouseLeave={() => setHoveredRow(null)}
              style={{ cursor: "pointer" }}
            >
              <td>Test 1</td>
              <td>{comparison.messageId || "N/A"}</td>
              <td>
                {comparison.interfaceName ? comparison.interfaceName : "N/A"}
              </td>
              <td>{formdata?.iFlowName || "N/A"}</td>
              <td>
                <button
                  className="view-payload-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewPayload(formatXml(comparison.poInputPayload), "PO Input");
                  }}
                >
                  View PO Input
                </button>
              </td>
              <td>
                <button
                  className="view-payload-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewPayload(formatXml(comparison.poOutputPayload), "PO Output");
                  }}
                >
                  View PO Output
                </button>
              </td>
              <td>
                <button
                  className="view-payload-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewPayload(formatXml(comparison.cpiPayload), "CPI Output");
                  }}
                >
                  View CPI Output
                </button>
              </td>
              <td>
                <div
                  className={`status-badge ${
                    comparison.status === "Match"
                      ? "status-match"
                      : "status-difference"
                  }`}
                >
                  {comparison.status}
                </div>
              </td>
              <td>
                <button
                  className="details-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleViewDiff(comparison);
                  }}
                >
                  View Diff
                </button>
              </td>
              <td>{comparison.cpiOutput}</td>
              <td>{comparison.poOutput}</td>
              <td>{comparison.status}</td>
              <td>
                <button
                  className={`error-view-button ${!comparison.error ? "disabled" : ""}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (comparison.error) {
                      handleViewError(comparison.error);
                    }
                  }}
                  disabled={!comparison.error}
                  title={comparison.error ? "View Error Details" : "No Error"}
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="eye-icon"
                  >
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Diff Viewer Modal */}
      {diffViewerOpen && (
        <div className="modal-overlay">
          <div className="modal-content diff-modal">
            <div className="modal-header">
              <h3>
                Payload Comparison: {currentComparison?.interfaceName}
                <small> (Message ID: {currentComparison?.messageId})</small>
              </h3>
              <button
                className="close-modal"
                onClick={() => setDiffViewerOpen(false)}
              >
                ×
              </button>
            </div>

            <div className="diff-container">
              {isLoading ? (
                <div className="loading-message">
                  <CircularProgress />
                  <p>Loading payloads for comparison...</p>
                </div>
              ) : (
                leftPayload &&
                rightPayload && (
                  <DiffEditor
                    key={currentComparison?.id}
                    height="70vh"
                    language="xml"
                    original={leftPayload}
                    modified={rightPayload}
                    options={{
                      readOnly: true,
                      renderSideBySide: true,
                      enableSplitViewResizing: true,
                      scrollBeyondLastLine: false,
                      minimap: { enabled: false },
                    }}
                  />
                )
              )}
            </div>

            <div className="diff-legend">
              <div className="legend-item">
                <span className="legend-color po-color"></span>
                <span>PO Output Payload</span>
              </div>
              <div className="legend-item">
                <span className="legend-color cpi-color"></span>
                <span>CPI Output Payload</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Error Details Modal with Solutions */}
      {errorModalOpen && (
        <div className="modal-overlay">
          <div className="modal-content error-modal enhanced-error-modal">
            <div className="modal-header">
              <h3>Error Details</h3>
              <button
                className="close-modal"
                onClick={() => setErrorModalOpen(false)}
              >
                ×
              </button>
            </div>
            
            <div className="error-modal-content">
              {/* Error Section */}
              <div className="error-section">
                <h4 style={{textAlign:"left"}}>Error Message</h4>
                <div className="error-content">
                  <pre className="error-text">{currentError}</pre>
                </div>
                <h4 style={{textAlign:"left"}}>Description</h4>
                <div className="error-content">
                  <pre className="error-text">{suggestedSolutions?.humanReadableMessage}</pre>
                </div>
              </div>

              {/* Solutions Accordion */}
              <div className="solutions-accordion">
                <button 
                  className="accordion-header"
                  onClick={handleToggleSolutions}
                  disabled={loadingSolutions}
                >
                  <span>View Suggested Solutions</span>
                  {loadingSolutions ? (
                    <CircularProgress size={16} />
                  ) : (
                    <svg 
                      className={`accordion-icon ${solutionsExpanded ? 'expanded' : ''}`}
                      width="16" 
                      height="16" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="2"
                    >
                      <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                  )}
                </button>

                {solutionsExpanded && (
                  <div className="accordion-content">
                    {suggestedSolutions ? (
                      <div className="solutions-content">
                        {suggestedSolutions.errorCauseSolutions && 
                         suggestedSolutions.errorCauseSolutions.length > 0 ? (
                          <div className="solutions-table-container">
                            <table className="solutions-table">
                              <thead>
                                <tr>
                                  <th>#</th>
                                  <th>Possible Cause</th>
                                  <th>Suggested Solution</th>
                                </tr>
                              </thead>
                              <tbody>
                                {suggestedSolutions.errorCauseSolutions.map((item, index) => (
                                  <tr key={index}>
                                    <td className="index-cell">{index + 1}</td>
                                    <td className="cause-cell">{item.cause}</td>
                                    <td className="solution-cell">{item.solution}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        ) : (
                          <div className="no-solutions">
                            <p>No specific solutions available for this error.</p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="solutions-error">
                        <p>Unable to fetch suggested solutions. Please try again.</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SingleTestCaseResultsTable;