

import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import ScenarioSelector from "./ScenarioSelector";
import IntermediatePage from "./IntermediatePage";
import SingleTestCaseForm from "./SingleTestCaseForm";
import MultipleTestCaseForm from "./MultipleTestCaseForm";
import ComparisonResults from "./ComparisonResults";
import "../App.css";
import APIEndpoint from "../config";
import { useLocation } from "react-router-dom";
import "./popup.css";
import MultipleInterfacesForm from "./Multipleinterface/MultipleInterfacesForm";

// Custom Popup Component
const CustomPopup = ({ isOpen, onClose, title, message, type = "info" }) => {
  if (!isOpen) return null;

  const getIcon = () => {
    switch (type) {
      case "success":
        return "✓";
      case "error":
        return "✕";
      case "warning":
        return "⚠";
      default:
        return "ℹ";
    }
  };

  const getTypeClass = () => {
    switch (type) {
      case "success":
        return "popup-success";
      case "error":
        return "popup-error";
      case "warning":
        return "popup-warning";
      default:
        return "popup-info";
    }
  };

  return (
    <div className="popup-overlay">
      <div className={`popup-container ${getTypeClass()}`}>
        <div className="popup-header">
          <div className="popup-icon">{getIcon()}</div>
          <h3 className="popup-title">{title}</h3>
          <button className="popup-close" onClick={onClose}>
            ×
          </button>
        </div>
        <div className="popup-content">
          <p>{message}</p>
        </div>
        <div className="popup-footer">
          <button className="popup-button" onClick={onClose}>
            OK
          </button>
        </div>
      </div>
    </div>
  );
};

const POTesting = () => {
  const location = useLocation();

  // Updated to handle both single and multiple interface selections
  const selectedInterfaces = location.state?.selectedInterfaces || [];
  const selectedInterface = location.state?.selectedInterface; // For backward compatibility

  // Determine if we have multiple interfaces selected
  const hasMultipleInterfaces = selectedInterfaces.length > 1;
  const interfacesToProcess =
    selectedInterfaces.length > 0
      ? selectedInterfaces
      : selectedInterface
      ? [selectedInterface]
      : [];

  const [selection, setSelection] = useState(
    hasMultipleInterfaces ? "multiple-interfaces" : null
  );
  const [showResults, setShowResults] = useState(false);
  const [showSingleTestCase, setShowSingleTestCase] = useState(true);
  const [comparisonResult, setComparisonResult] = useState(null);
  const [isSingleTestCase, setIsSingleTestCase] = useState(false);

  // Popup state
  const [popup, setPopup] = useState({
    isOpen: false,
    title: "",
    message: "",
    type: "info",
  });

  const [formData, setFormData] = useState({
    serviceInterface:
      interfacesToProcess.length === 1
        ? interfacesToProcess[0]?.interface?.name || ""
        : "",
    selectedInterfaces: interfacesToProcess.map(
      (iface) => iface.interface?.name || ""
    ), // Store all interface names
    tenant: "",
    username: "",
    packageName: "",
    iFlowName: "",
    cpiClientId: "",
    cpiClientSecret: "",
    adapterName: "",
    startTime: "",
    endTime: "",
  });

  const [fetchedInterfaces, setFetchedInterfaces] =
    useState(interfacesToProcess);
  const navigate = useNavigate();

  // Popup helper functions
  const showPopup = (title, message, type = "info") => {
    setPopup({
      isOpen: true,
      title,
      message,
      type,
    });
  };

  const closePopup = () => {
    setPopup({
      isOpen: false,
      title: "",
      message: "",
      type: "info",
    });
  };

  const handleProceed = async () => {
    if (selection === "single") {
      setIsSingleTestCase(true);
      console.log("Proceeding to single test");
      await handleSingleTestProceed();
    } else if (selection === "multiple") {
      setIsSingleTestCase(false);
      console.log("Proceeding to multiple test");
      await handleMultipleTestProceed();
    } else if (selection === "multiple-interfaces") {
      setIsSingleTestCase(false);
      console.log("Proceeding to multiple interfaces test");
      await handleMultipleInterfacesProceed();
    }
  };

  const handleGenerateReport = async () => {
    if (selection === "single") {
      await handleSingleTestGenerateReport();
    } else if (selection === "multiple") {
      await handleMultipleTestGenerateReport();
    } else if (selection === "multiple-interfaces") {
      await handleMultipleInterfacesGenerateReport();
    }
  };

  const validateISOFormat = (dateString) => {
    if (!dateString) return false;
    const isoPattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;
    return isoPattern.test(dateString);
  };

  const handleSingleTestProceed = async () => {
    if (!validateISOFormat(formData.startTime)) {
      showPopup(
        "Invalid Format",
        "Please enter Start Time in valid ISO format: 2025-05-29T10:00:58.137Z",
        "error"
      );
      return;
    }
    if (!validateISOFormat(formData.endTime)) {
      showPopup(
        "Invalid Format",
        "Please enter End Time in valid ISO format: 2025-05-29T10:00:58.137Z",
        "error"
      );
      return;
    }

    try {
      const { startTime, endTime, serviceInterface, iFlowName, tenant } =
        formData;
      const isoStartTime = startTime;
      const isoEndTime = endTime;

      // 1. First API call - extractSingleInputPayload
      const inputParams = new URLSearchParams({
        interfaceName: serviceInterface,
        artifactName: iFlowName,
        startTime: isoStartTime,
        endTime: isoEndTime,
        configName: tenant,
        adapterType: formData.adapterName,
      });

      const inputResponse = await fetch(
        `${APIEndpoint}/extractSingleInputPayloadUpdated?${inputParams.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${localStorage.getItem("basicAuth")}`,
          },
        }
      );

      if (!inputResponse.ok) {
        const text = await inputResponse.text();
        throw new Error(`Input payload error: ${text}`);
      }

      const inputContentType = inputResponse.headers.get("Content-Type");
      const inputResult = inputContentType?.includes("application/json")
        ? await inputResponse.json()
        : await inputResponse.text();

      console.log("Step 1 - Input Payloads Extracted:", inputResult);

      // 2. Second API call - extractSingleOutputPayload
      const outParams = new URLSearchParams({
        interfaceName: serviceInterface,
        startTime: isoStartTime,
        endTime: isoEndTime,
        adapterType: formData.adapterName,
        configName: tenant,
      });

      const outResponse = await fetch(
        `${APIEndpoint}/extractSingleOutputPayload?${outParams.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${localStorage.getItem("basicAuth")}`,
          },
        }
      );

      if (!outResponse.ok) {
        const text = await outResponse.text();
        throw new Error(`Output payload error: ${text}`);
      }

      const outResult = await outResponse.json();
      console.log("Step 2 - Output payload result:", outResult);

      showPopup(
        "Success",
        "Data extraction completed successfully! Please wait a minute to generate the report.",
        "success"
      );
    } catch (error) {
      console.error("Submission error:", error);
      showPopup(
        "Processing Failed",
        `Processing failed: ${error.message}`,
        "error"
      );
      throw error;
    }
  };

  const handleSingleTestGenerateReport = async () => {
    try {
      const { startTime, endTime, serviceInterface, iFlowName, tenant } =
        formData;
      const isoStartTime = startTime;
      const isoEndTime = endTime;

      const outParams1 = new URLSearchParams({
        interfaceName: serviceInterface,
        startTime: isoStartTime,
        endTime: isoEndTime,
        artifactName: iFlowName,
        configName: tenant,
        adapterType: formData.adapterName,
      });

      // Third API call - compareWithDetailsUnit
      const compareResponse = await fetch(
        `${APIEndpoint}/compareWithDetailsUnit?${outParams1.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${localStorage.getItem("basicAuth")}`,
          },
        }
      );

      if (!compareResponse.ok) {
        const text = await compareResponse.text();
        throw new Error(`Comparison error: ${text}`);
      }

      const compareData = await compareResponse.json();
      console.log("Step 3 - Comparison result:", compareData);

      // Store comparison results and show results page
      setComparisonResult(compareData);
      setShowResults(true);
    } catch (error) {
      console.error("Report generation error:", error);
      showPopup(
        "Report Generation Failed",
        `Report generation failed: ${error.message}`,
        "error"
      );
      throw error;
    }
  };

  const handleMultipleTestProceed = async () => {
    try {
      const { startTime, endTime, serviceInterface, iFlowName } = formData;
      const isoStartTime = startTime;
      const isoEndTime = endTime;

      // 1. First API call - extractInputPayload
      const inputParams = new URLSearchParams({
        interfaceName: serviceInterface,
        artifactName: iFlowName,
        configName: formData.tenant,
        adapterType: formData.adapterName,
      });

      const inputResponse = await fetch(
        `${APIEndpoint}/extractInputPayloads2?${inputParams.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${localStorage.getItem("basicAuth")}`,
          },
        }
      );

      if (!inputResponse.ok) {
        const text = await inputResponse.text();
        throw new Error(`Input payload error: ${text}`);
      }

      const inputContentType = inputResponse.headers.get("Content-Type");
      const inputResult = inputContentType?.includes("application/json")
        ? await inputResponse.json()
        : await inputResponse.text();

      console.log("Step 1 - Input Payloads Extracted:", inputResult);

      // 2. Second API call - extractOutputPayload
      const outputParams = new URLSearchParams({
        startTime: isoStartTime,
        endTime: isoEndTime,
        interfaceName: serviceInterface,
        adapterType: formData.adapterName,
      });

      const outputResponse = await fetch(
        `${APIEndpoint}/extractOutputPayload44?${outputParams.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${localStorage.getItem("basicAuth")}`,
          },
        }
      );

      if (!outputResponse.ok) {
        const text = await outputResponse.text();
        throw new Error(`Output payload error: ${text}`);
      }

      const outputContentType = outputResponse.headers.get("Content-Type");
      const outputResult = outputContentType?.includes("application/json")
        ? await outputResponse.json()
        : await outputResponse.text();

      console.log("Step 2 - Output Payloads Extracted:", outputResult);

      showPopup(
        "Success",
        "Data extraction completed successfully! Please wait a minute to generate the report.",
        "success"
      );
    } catch (error) {
      console.error("Multiple Test Case Error:", error);
      showPopup(
        "Processing Failed",
        `Processing failed: ${error.message}`,
        "error"
      );
      throw error;
    }
  };

  const handleMultipleTestGenerateReport = async () => {
    try {
      const { serviceInterface, iFlowName } = formData;

      // Third API call - compareWithDetails
      const compareParams = new URLSearchParams({
        interfaceName: serviceInterface,
        artifactName: iFlowName,
        adapterType: formData.adapterName,
      });

      const compareResponse = await fetch(
        `${APIEndpoint}/compareDetailsWith3?${compareParams.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${localStorage.getItem("basicAuth")}`,
          },
        }
      );

      if (!compareResponse.ok) {
        const text = await compareResponse.text();
        throw new Error(`Comparison error: ${text}`);
      }

      const compareContentType = compareResponse.headers.get("Content-Type");
      const comparisonData = compareContentType?.includes("application/json")
        ? await compareResponse.json()
        : await compareResponse.text();

      console.log("Step 3 - Comparison Result:", comparisonData);

      // Final Step - Display in UI
      if (typeof comparisonData === "object") {
        setComparisonResult(comparisonData);
        setShowResults(true);
      } else {
        showPopup(
          "Non-JSON Response",
          `Comparison returned non-JSON response:\n${comparisonData}`,
          "warning"
        );
      }
    } catch (error) {
      console.error("Report generation error:", error);
      showPopup(
        "Report Generation Failed",
        `Report generation failed: ${error.message}`,
        "error"
      );
      throw error;
    }
  };

  // New handlers for multiple interfaces
  const handleMultipleInterfacesProceed = async () => {
    try {
      const { startTime, endTime, multipleInterfaceData } = formData;
      const isoStartTime = startTime;
      const isoEndTime = endTime;

      // Filter selected interfaces and prepare data
      const selectedInterfaceData =
        multipleInterfaceData?.filter((item) => item.selected) || [];

      if (selectedInterfaceData.length === 0) {
        showPopup(
          "No Selection",
          "Please select at least one interface to process.",
          "warning"
        );
        return;
      }

      // Prepare payload for the new API
      const interfaceNames = selectedInterfaceData.map(
        (item) => item.interfaceName
      );
      const artifactNames = selectedInterfaceData.map((item) => item.iFlow);
      const adapterTypes = selectedInterfaceData.map((item) => item.adapter);

      // Validate that all required fields are filled
      const missingData = selectedInterfaceData.filter(
        (item) => !item.iFlow || !item.adapter
      );
      if (missingData.length > 0) {
        showPopup(
          "Missing Data",
          "Please ensure all selected interfaces have I-Flow and Adapter selected.",
          "warning"
        );
        return;
      }

      // Check if all adapters are the same (API might require uniform adapter type)
      const uniqueAdapters = [...new Set(adapterTypes)];
      if (uniqueAdapters.length > 1) {
        showPopup(
          "Mixed Adapters",
          "All selected interfaces must use the same adapter type for bulk processing.",
          "warning"
        );
        return;
      }

      showPopup(
        "Processing",
        `Starting data extraction for ${selectedInterfaceData.length} interfaces...`,
        "info"
      );

      // 1. Extract input payloads using new POST API
      const inputPayload = {
        interfaceNames: interfaceNames,
        artifactNames: artifactNames,
        adapterType: uniqueAdapters[0], // Using the first (and only) adapter type
      };

      console.log("Input payload for extraction:", inputPayload);

      const inputResponse = await fetch(
        `${APIEndpoint}/api/multipleinterfaces/extractInputPayloads`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${localStorage.getItem("basicAuth")}`,
          },
          body: JSON.stringify(inputPayload),
        }
      );

      if (!inputResponse.ok) {
        const errorText = await inputResponse.text();
        throw new Error(`Input payload extraction failed: ${errorText}`);
      }

      const inputResult = await inputResponse.json();
      console.log("Step 1 - Input Payloads Extracted:", inputResult);

      // 2. Extract output payloads using new POST API
      const outputPayload = {
        interfaceNames: interfaceNames,
        artifactNames: artifactNames,
        adapterType: uniqueAdapters[0],
      };

      console.log("Output payload for extraction:", outputPayload);

      const outputResponse = await fetch(
        `${APIEndpoint}/api/multipleinterfaces/extractOutputPayloads`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${localStorage.getItem("basicAuth")}`,
          },
          body: JSON.stringify(outputPayload),
        }
      );

      if (!outputResponse.ok) {
        const errorText = await outputResponse.text();
        throw new Error(`Output payload extraction failed: ${errorText}`);
      }

      const outputResult = await outputResponse.json();
      console.log("Step 2 - Output Payloads Extracted:", outputResult);

      showPopup(
        "Success",
        `Successfully processed ${selectedInterfaceData.length} interface(s). Please wait a moment to generate the report.`,
        "success"
      );
    } catch (error) {
      console.error("Multiple Interfaces Processing Error:", error);
      showPopup(
        "Processing Failed",
        `Processing failed: ${error.message}`,
        "error"
      );
      throw error;
    }
  };

  const handleMultipleInterfacesGenerateReport = async () => {
    try {
      // Get the selected interface data from formData
      const { multipleInterfaceData } = formData;

      // Filter only the selected interfaces and prepare the data
      const selectedInterfaceData =
        multipleInterfaceData?.filter((item) => item.selected) || [];

      if (selectedInterfaceData.length === 0) {
        showPopup(
          "No Selection",
          "Please select at least one interface to process.",
          "warning"
        );
        return;
      }

      // Prepare the payload for the comparison API
      const interfaceNames = selectedInterfaceData.map(
        (item) => item.interfaceName
      );
      const artifactNames = selectedInterfaceData.map((item) => item.iFlow);
      const adapterType = selectedInterfaceData[0]?.adapter; // Assuming all use same adapter type

      // Validate that all required fields are filled
      const missingData = selectedInterfaceData.filter(
        (item) => !item.iFlow || !item.adapter
      );
      if (missingData.length > 0) {
        showPopup(
          "Missing Data",
          "Please ensure all selected interfaces have I-Flow and Adapter selected.",
          "warning"
        );
        return;
      }

      showPopup(
        "Generating Reports",
        `Generating comparison reports for ${selectedInterfaceData.length} interfaces...`,
        "info"
      );

      // Prepare the request payload
      const comparisonPayload = {
        interfaceNames: interfaceNames,
        artifactNames: artifactNames,
        adapterType: adapterType,
      };

      console.log("Comparison payload:", comparisonPayload);

      // Make the POST request to the comparison API
      const compareResponse = await fetch(
        `${APIEndpoint}/api/multipleinterfaces/compare`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${localStorage.getItem("basicAuth")}`,
          },
          body: JSON.stringify(comparisonPayload),
        }
      );

      if (!compareResponse.ok) {
        const errorText = await compareResponse.text();
        throw new Error(`Comparison failed: ${errorText}`);
      }

      const compareResult = await compareResponse.json();
      console.log("Comparison result:", compareResult);

      // Format the results for display - CHANGE THIS:
      const combinedResults = {
        type: "multiple-interfaces",
        timestamp: new Date().toISOString(),
        totalInterfaces: interfaceNames.length,
        results: compareResult.results || compareResult, // Handle both cases
        requestPayload: comparisonPayload,
      };

      setComparisonResult(combinedResults);

      setShowResults(true);
      showPopup(
        "Success",
        "Comparison reports generated successfully!",
        "success"
      );
    } catch (error) {
      console.error("Multiple Interfaces Report Generation Error:", error);
      showPopup(
        "Report Generation Failed",
        `Report generation failed: ${error.message}`,
        "error"
      );
      throw error;
    }
  };

  const handleBackToDashboard = () => {
    navigate(-1);
  };

  const handleProceedWithData = (apiResult) => {
    setFetchedInterfaces(apiResult.messages || []);
    setShowSingleTestCase(true);
  };

  const handleProceedToForm = () => {
    setShowSingleTestCase(true);
  };

  const handleBackToSelection = () => {
    setSelection(null);
    setShowSingleTestCase(false);
  };

  return (
    <div className="po-testing-container">
      {/* Custom Popup */}
      <CustomPopup
        isOpen={popup.isOpen}
        onClose={closePopup}
        title={popup.title}
        message={popup.message}
        type={popup.type}
      />

      {!showResults ? (
        <>
          <ScenarioSelector
            selection={selection}
            setSelection={setSelection}
            showTitle={!selection}
            hasMultipleInterfaces={hasMultipleInterfaces}
          />

          {selection === "single" && !showSingleTestCase && (
            <IntermediatePage
              onBack={handleBackToSelection}
              onProceed={handleProceedWithData}
            />
          )}

          {selection === "single" && showSingleTestCase && (
            <SingleTestCaseForm
              formData={formData}
              setFormData={setFormData}
              onBack={handleBackToDashboard}
              onProceed={handleProceed}
              onGenerateReport={handleGenerateReport}
              serviceInterfaces={fetchedInterfaces}
            />
          )}

          {selection === "multiple" && (
            <MultipleTestCaseForm
              formData={formData}
              setFormData={setFormData}
              onBack={handleBackToDashboard}
              onProceed={handleProceed}
              onGenerateReport={handleGenerateReport}
              serviceInterfaces={fetchedInterfaces}
            />
          )}

          {selection === "multiple-interfaces" && (
            <MultipleInterfacesForm
              formData={formData}
              setFormData={setFormData}
              onBack={handleBackToDashboard}
              onProceed={handleProceed}
              onGenerateReport={handleGenerateReport}
              serviceInterfaces={fetchedInterfaces}
              selectedInterfaces={interfacesToProcess}
            />
          )}
        </>
      ) : (
        <ComparisonResults
          comparisonResult={comparisonResult}
          formdata={formData}
          onBack={() => setShowResults(false)}
          isSingleTestCase={isSingleTestCase}
        />
      )}
    </div>
  );
};

export default POTesting;
