


import React from "react";
import Navbar from "./Navbar";
import Footer from "./Footer";
import "../App.css";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
// Import icons (assuming you're using react-icons or similar)
import { 
  FaHome, 
  FaUserPlus, 
  FaDownload, 
  FaCompressArrowsAlt, 
  FaChartLine, 
  FaSignOutAlt,
  FaBell,
  FaChartBar
} from 'react-icons/fa';

const Dashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const getButtonStyle = (path) => {
  // Handle the root dashboard case
  const rootMatch = path === '' && location.pathname === '/dashboard';
  
  // Special case for extract button and its nested routes
  const extractMatch = path === 'extract' && (
    location.pathname.startsWith('/dashboard/extract') ||
    location.pathname.startsWith('/dashboard/timestamp') ||
    location.pathname.startsWith('/dashboard/interface')||
    location.pathname.startsWith('/dashboard/po-login')||
    location.pathname.startsWith('/dashboard/potesting')
  );
  
  // Handle other nested paths
  const nestedMatch = path !== '' && 
                     path !== 'extract' && 
                     location.pathname.startsWith(`/dashboard/${path}`);
  
  const isActive = rootMatch || extractMatch || nestedMatch;
  
  return {
    backgroundColor: isActive ? "#e5e7eb" : "white",
    color: "#374151",
    borderRadius: "8px",
    width: "202px",
    fontWeight: "500",
    marginBottom: "10px",
    fontSize: "14px",
    border: "1px solid #e5e7eb",
    padding: "19px 28px",
    cursor: "pointer",
    transition: "all 0.3s ease",
    boxShadow: isActive ? "0 2px 4px rgba(0, 0, 0, 0.1)" : "0 1px 3px rgba(0, 0, 0, 0.1)",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    textAlign: "left"
  };
};
  // Function to handle navigation
  const handleNavigation = (path) => {
    if (path === '') {
      navigate('/dashboard');
    } else if (path === 'login') {
      navigate('/');
    } else {
      navigate(`/dashboard/${path}`);
    }
  };

  return (
    <div className="app-container">
      <Navbar />
      <div className="main-content">
        <div className="sidebar">
          <button 
            style={getButtonStyle('')}
            onClick={() => handleNavigation('')}
          >
            <FaHome size={16} />
            Back to Home
          </button>
          
          <button 
            style={getButtonStyle('register')}
            onClick={() => handleNavigation('register')}
          >
            <FaUserPlus size={16} />
            Register Tenant
          </button>
          
          <button 
            style={getButtonStyle('extract')}
            onClick={() => handleNavigation('extract')}
          >
            <FaDownload size={16} />
            Extract Payloads
          </button>
          
          <button 
            style={getButtonStyle('compare')}
            onClick={() => handleNavigation('compare')}
          >
            <FaCompressArrowsAlt size={16} />
            Compare Payloads
          </button>

          {/* Monitoring button */}
          <div style={{ marginTop: "auto" }}>
            <button 
              style={{
                backgroundColor: "white",
                color: "#374151",
                borderRadius: "8px",
                width: "202px",
                fontWeight: "500",
                marginBottom: "10px",
                fontSize: "14px",
                border: "1px solid #e5e7eb",
                padding: "19px 28px",
                cursor: "pointer",
                transition: "all 0.3s ease",
                boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                textAlign: "left"
              }}
              onClick={() => window.location.href = "https://incbtpcoe.launchpad.cfapps.us10.hana.ondemand.com/4f0ebc99-c527-4882-8d73-d18dcb14fce0.dashapprouter.dashmodule-0.0.1/index.html"}
            >
              <FaChartLine size={16} />
              Monitoring
            </button>
          </div> 

          <button 
            style={getButtonStyle('groovy')}
            onClick={() => handleNavigation('groovy')}
          >
            <FaCompressArrowsAlt size={16} />
            Reusable Components
          </button>

          <button 
            style={getButtonStyle('mmap')}
            onClick={() => handleNavigation('mmap')}
          >
            <FaCompressArrowsAlt size={16} />
            Mapping Automation
          </button>

           <button 
            style={getButtonStyle('convertor')}
            onClick={() => handleNavigation('convertor')}
          >
            <FaCompressArrowsAlt size={16} />
            Groovy Convertor
          </button>

          {/* Back to Login button */}
          <div>
            <button 
              style={getButtonStyle('login')}
              onClick={() => handleNavigation('login')}
            >
              <FaSignOutAlt size={16} />
              Back to Login
            </button>
          </div>
        </div>
        <div className="content">
          <Outlet />
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Dashboard;