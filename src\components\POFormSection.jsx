import React from "react";
import "../App.css";

const POFormSection = ({ formData, setFormData, serviceInterfaces }) => (
  <div className="testcase-section">
    <h5 style={{ display: "flex", marginBottom: "10px" ,marginTop:"20px"}}>Please mention the required details from SAP PO 7.5 Monitoring</h5>
    <table>
      <thead>
        <tr style={{ backgroundColor: '#272D4F', color: 'white' }}>
          <th style={{backgroundColor: '#272D4F', padding: '10px', border: '1px solid #ccc', textAlign: 'left' }}>SAP PO 7.5</th>
          <th style={{backgroundColor: '#272D4F' ,padding: '10px', border: '1px solid #ccc', textAlign: 'left' }}>Enter details</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Service Interface</td>
          <td>
            <select
              value={formData.serviceInterface}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  serviceInterface: e.target.value,
                })
              }
            >
              <option value="">Select Interface</option>
              {serviceInterfaces.map((item, index) => (
                <option key={index} value={item.interface.name}>
                  {item.interface.name} ({item.interface.namespace})
                </option>
              ))}
            </select>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
);

export default POFormSection;