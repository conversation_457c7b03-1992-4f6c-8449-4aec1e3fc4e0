import React, { useState } from "react";
import CircularProgress from "@mui/material/CircularProgress";
import ResultsTable from "./ResultsTable";
import ComparisonDetailsModal from "./ComparisonDetailsModal";
import PayloadModal from "./PayloadModal";
import SingleTestCaseResultsTable from "./SingleTestCaseResultsTable";
import MultipleInterfaceResultsTable from "../components/Multipleinterface/MultipleInterfaceResultsTable";
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  pdf,
} from "@react-pdf/renderer";
import { saveAs } from "file-saver";
import "../App.css";
import APIEndpoint from "../config";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Snackbar,
  Alert,
} from "@mui/material";
const styles = StyleSheet.create({
  statusPass: {
    color: "#008800",
  },
  statusFail: {
    color: "#CC0000",
  },
  statusWarning: {
    color: "#FFA500",
  },
  page: {
    padding: 30,
    backgroundColor: "#FFFFFF",
  },
  col4: { width: "25%" },
  section: {
    marginBottom: 15,
    padding: 10,
    backgroundColor: "#FAFAFA",
    borderRadius: 5,
    borderLeftWidth: 3,
    borderLeftColor: "#003366",
  },
  pageBreak: {
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
    marginVertical: 20,
  },
  boldText: {
    fontWeight: "bold",
  },
  header: {
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#CCCCCC",
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#003366",
  },
  subtitle: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 5,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
    marginTop: 15,
    color: "#003366",
  },
  comparisonHeader: {
    backgroundColor: "#EEF5FF",
    padding: 10,
    marginBottom: 10,
    borderRadius: 5,
  },
  fileHeader: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 5,
  },
  fileStatus: {
    fontSize: 12,
    marginBottom: 5,
  },
  tableHeader: {
    backgroundColor: "#EEEEEE",
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#CCCCCC",
    padding: 5,
  },
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
    padding: 5,
  },
  col1: { width: "30%" },
  col2: { width: "35%" },
  col3: { width: "35%" },
  text: {
    fontSize: 10,
    paddingRight: 5,
  },
  footer: {
    position: "absolute",
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: "center",
    color: "#666666",
    fontSize: 10,
    borderTopWidth: 1,
    borderTopColor: "#CCCCCC",
    paddingTop: 10,
  },
  infoRow: {
    flexDirection: "row",
    marginBottom: 5,
  },
  infoLabel: {
    width: "40%",
    fontSize: 10,
    fontWeight: "bold",
  },
  infoValue: {
    width: "60%",
    fontSize: 10,
  },
  summary: {
    marginTop: 15,
    marginBottom: 15,
    padding: 10,
    backgroundColor: "#F9F9F9",
    borderRadius: 5,
  },
  summaryText: {
    fontSize: 12,
    marginBottom: 5,
  },
  interfaceSection: {
    marginBottom: 20,
    padding: 10,
    backgroundColor: "#F8F9FA",
    borderRadius: 5,
  },
  interfaceTitle: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#007BFF",
  },
});

// PDF Report Component for Multiple Interfaces
// Fixed PDF Report Component for Multiple Interfaces
// Fixed PDF Report Component for Multiple Interfaces
const MultipleInterfaceReport = ({ reportData, formdata }) => {
  if (!reportData || !Array.isArray(reportData)) {
    return (
      <Document>
        <Page size="A4" style={styles.page}>
          <Text>No data available</Text>
        </Page>
      </Document>
    );
  }

  // Helper function to determine status from comparison object
  const getComparisonStatus = (comparison) => {
    if (comparison.status) return comparison.status;
    if (comparison.match === true) return "Match";
    if (comparison.match === false) return "Difference";
    if (comparison.error) return "Cannot be processed";
    return "Unknown";
  };

  // Helper function to categorize comparisons
  const categorizeComparison = (comparison) => {
    const status = getComparisonStatus(comparison);
    return {
      isMatch: status === "Match",
      isDifference: status === "Difference", 
      isCannotProcess: status === "Cannot be processed" || status === "Processing Error",
      isUnprocessed: status === "Unprocessed" || comparison.unprocessed === true
    };
  };

  // Calculate overall statistics across all interfaces
  const overallStats = reportData.reduce((acc, interfaceResult) => {
    const interfaceComparisons = interfaceResult.comparisons || [];
    
    interfaceComparisons.forEach(comparison => {
      const category = categorizeComparison(comparison);
      acc.total += 1;
      if (category.isMatch) acc.matches += 1;
      if (category.isDifference) acc.differences += 1;
      if (category.isCannotProcess) acc.cannotProcess += 1;
      if (category.isUnprocessed) acc.unprocessed += 1;
    });
    
    return acc;
  }, { total: 0, matches: 0, differences: 0, cannotProcess: 0, unprocessed: 0 });

  const successRate = overallStats.total > 0 ? 
    Math.round((overallStats.matches / overallStats.total) * 100) : 0;

  // Status style mapping
  const getStatusStyle = (status) => {
    switch (status) {
      case "Match":
        return styles.statusPass;
      case "Difference":
        return styles.statusFail;
      case "Cannot be processed":
      case "Processing Error":
        return styles.statusWarning;
      default:
        return {};
    }
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header}>
          <Text style={styles.title}>IntSwitch Multiple Interface Comparison Report</Text>
          <Text style={styles.subtitle}>
            Generated on: {new Date().toLocaleString()}
          </Text>
          <Text style={styles.subtitle}>
            Project: {formdata?.projectName || 'N/A'} | Environment: {formdata?.environment || 'N/A'}
          </Text>
        </View>

        <View style={styles.summary}>
          <Text style={[styles.summaryText, styles.boldText]}>
            Executive Summary
          </Text>
          <Text style={styles.summaryText}>
            Total Interfaces Tested: {reportData.length}
          </Text>
          <Text style={styles.summaryText}>
            Total Test Cases: {overallStats.total}
          </Text>
          <Text style={styles.summaryText}>
            ✓ Successful Matches: {overallStats.matches} ({Math.round((overallStats.matches/overallStats.total)*100) || 0}%)
          </Text>
          <Text style={styles.summaryText}>
            ✗ Data Differences: {overallStats.differences} ({Math.round((overallStats.differences/overallStats.total)*100) || 0}%)
          </Text>
          <Text style={styles.summaryText}>
            ⚠ Processing Errors: {overallStats.cannotProcess} ({Math.round((overallStats.cannotProcess/overallStats.total)*100) || 0}%)
          </Text>
          {overallStats.unprocessed > 0 && (
            <Text style={styles.summaryText}>
              ⏸ Unprocessed: {overallStats.unprocessed} ({Math.round((overallStats.unprocessed/overallStats.total)*100)}%)
            </Text>
          )}
          <Text style={[styles.summaryText, styles.boldText]}>
            Overall Success Rate: {successRate}%
          </Text>
        </View>

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header}>
          <Text style={styles.title}>IntSwitch Multiple Interface Comparison Report</Text>
          <Text style={styles.subtitle}>
            Generated on: {new Date().toLocaleString()}
          </Text>
          <Text style={styles.subtitle}>
            Project: {formdata?.projectName || 'N/A'} | Environment: {formdata?.environment || 'N/A'}
          </Text>
        </View>

        <View style={styles.summary}>
          <Text style={[styles.summaryText, styles.boldText]}>
            Executive Summary
          </Text>
          <Text style={styles.summaryText}>
            Total Interfaces Tested: {reportData.length}
          </Text>
          <Text style={styles.summaryText}>
            Total Test Cases: {overallStats.total}
          </Text>
          <Text style={styles.summaryText}>
            ✓ Successful Matches: {overallStats.matches} ({Math.round((overallStats.matches/overallStats.total)*100) || 0}%)
          </Text>
          <Text style={styles.summaryText}>
            ✗ Data Differences: {overallStats.differences} ({Math.round((overallStats.differences/overallStats.total)*100) || 0}%)
          </Text>
          <Text style={styles.summaryText}>
            ⚠ Processing Errors: {overallStats.cannotProcess} ({Math.round((overallStats.cannotProcess/overallStats.total)*100) || 0}%)
          </Text>
          {overallStats.unprocessed > 0 && (
            <Text style={styles.summaryText}>
              ⏸ Unprocessed: {overallStats.unprocessed} ({Math.round((overallStats.unprocessed/overallStats.total)*100)}%)
            </Text>
          )}
          <Text style={[styles.summaryText, styles.boldText]}>
            Overall Success Rate: {successRate}%
          </Text>
        </View>
      </Page>

      {/* Each interface gets its own page to prevent overlapping */}
      {reportData.map((interfaceResult, interfaceIndex) => {
        const interfaceName = interfaceResult.comparisons?.[0]?.interfaceName || `Interface ${interfaceIndex + 1}`;
        const interfaceComparisons = interfaceResult.comparisons || [];
        
        // Calculate interface-specific stats
        const interfaceStats = interfaceComparisons.reduce((acc, comparison) => {
          const category = categorizeComparison(comparison);
          acc.total += 1;
          if (category.isMatch) acc.matches += 1;
          if (category.isDifference) acc.differences += 1;
          if (category.isCannotProcess) acc.cannotProcess += 1;
          if (category.isUnprocessed) acc.unprocessed += 1;
          return acc;
        }, { total: 0, matches: 0, differences: 0, cannotProcess: 0, unprocessed: 0 });

        const interfaceSuccessRate = interfaceStats.total > 0 ? 
          Math.round((interfaceStats.matches / interfaceStats.total) * 100) : 0;

        // Determine interface health status
        const getInterfaceHealthStatus = () => {
          if (interfaceStats.matches === interfaceStats.total) return "✓ HEALTHY";
          if (interfaceStats.cannotProcess === interfaceStats.total) return "✗ CRITICAL";
          if (interfaceStats.matches > interfaceStats.differences) return "⚠ ATTENTION NEEDED";
          return "✗ REQUIRES INVESTIGATION";
        };
        
        return (
          <Page key={interfaceIndex} size="A4" style={styles.page}>
            <View style={styles.interfaceSection}>
              <Text style={styles.interfaceTitle}>
                {interfaceName} - {getInterfaceHealthStatus()}
              </Text>
              
              <View style={{ marginBottom: 10 }}>
                <Text style={[styles.text, styles.boldText]}>
                  Interface Statistics:
                </Text>
                <Text style={styles.text}>
                  • Total Test Cases: {interfaceStats.total}
                </Text>
                <Text style={styles.text}>
                  • Successful: {interfaceStats.matches} | Failed: {interfaceStats.differences} | 
                  Processing Errors: {interfaceStats.cannotProcess}
                  {interfaceStats.unprocessed > 0 && ` | Unprocessed: ${interfaceStats.unprocessed}`}
                </Text>
                <Text style={[styles.text, styles.boldText]}>
                  • Success Rate: {interfaceSuccessRate}%
                </Text>
              </View>

              {/* Show critical issues summary for problematic interfaces */}
              {interfaceStats.cannotProcess > 0 && (
                <View style={{ marginBottom: 10, padding: 5, backgroundColor: "#FFF3CD", borderRadius: 3 }}>
                  <Text style={[styles.text, styles.boldText, { color: "#856404" }]}>
                    Critical Issues Detected:
                  </Text>
                  <Text style={[styles.text, { fontSize: 8, color: "#856404" }]}>
                    • Common causes: XML validation failures, schema mismatches, connectivity issues
                  </Text>
                  <Text style={[styles.text, { fontSize: 8, color: "#856404" }]}>
                    • Recommendation: Review error logs and validate payload structures
                  </Text>
                </View>
              )}

              {/* Summary table instead of detailed results - cleaner approach */}
              {interfaceStats.total > 0 && (
                <View style={styles.section}>
                  <Text style={[styles.text, styles.boldText, { marginBottom: 5 }]}>
                    Test Results Summary:
                  </Text>
                  
                  {/* Show only first 5 entries as examples, then summarize */}
                  <View style={styles.tableHeader}>
                    <Text style={[styles.boldText, { width: "40%", fontSize: 8}]}>Sample Message IDs</Text>
                    <Text style={[styles.boldText, { width: "20%", fontSize: 8}]}>Status</Text>
                    <Text style={[styles.boldText, { width: "15%", fontSize: 8}]}>CPI/PO</Text>
                    <Text style={[styles.boldText, { width: "25%", fontSize: 8}]}>Issue Type</Text>
                  </View>
                  
                  {interfaceComparisons.slice(0, 5).map((comparison, compIndex) => {
                    const status = getComparisonStatus(comparison);
                    const messageId = comparison.messageId || "N/A";
                    const shortId = messageId.length > 30 ? messageId.substring(0, 30) + "..." : messageId;
                    const issueType = comparison.error ? "Processing Error" : 
                                     comparison.differences && comparison.differences.length > 0 ? "Data Mismatch" : 
                                     "Unknown";

                    return (
                      <View key={`${interfaceIndex}-${compIndex}`} style={styles.tableRow}>
                        <Text style={[styles.text, { width: "40%", fontSize: 7 }]}>
                          {shortId}
                        </Text>
                        <Text style={[
                          styles.text, 
                          { width: "20%", fontSize: 7 },
                          getStatusStyle(status)
                        ]}>
                          {status}
                        </Text>
                        <Text style={[styles.text, { width: "15%", fontSize: 7 }]}>
                          {(comparison.cpiOutput || "N") + "/" + (comparison.poOutput || "N")}
                        </Text>
                        <Text style={[styles.text, { width: "25%", fontSize: 7 }]}>
                          {issueType}
                        </Text>
                      </View>
                    );
                  })}
                  
                  {interfaceStats.total > 5 && (
                    <View style={[styles.tableRow, { backgroundColor: "#F8F9FA" }]}>
                      <Text style={[styles.text, { width: "100%", fontSize: 7, fontStyle: "italic" }]}>
                        ... and {interfaceStats.total - 5} more test cases with similar patterns
                      </Text>
                    </View>
                  )}
                </View>
              )}

              {/* Recommendations section for problematic interfaces */}
              {interfaceSuccessRate < 100 && (
                <View style={{ marginTop: 10, padding: 8, backgroundColor: "#E7F3FF", borderRadius: 3 }}>
                  <Text style={[styles.text, styles.boldText, { color: "#004085" }]}>
                    Recommended Actions:
                  </Text>
                  {interfaceStats.cannotProcess > 0 && (
                    <Text style={[styles.text, { fontSize: 8, color: "#004085" }]}>
                      • Investigate processing errors and validate XML payloads
                    </Text>
                  )}
                  {interfaceStats.differences > 0 && (
                    <Text style={[styles.text, { fontSize: 8, color: "#004085" }]}>
                      • Review data transformation logic for {interfaceStats.differences} failed comparison(s)
                    </Text>
                  )}
                  <Text style={[styles.text, { fontSize: 8, color: "#004085" }]}>
                    • Verify interface configuration and mapping rules
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.footer}>
              <Text>
                IntSwitch Testing Tool - Page {interfaceIndex + 2} of {reportData.length + 2}
              </Text>
            </View>
          </Page>
        );
      })}

      {/* Final summary page */}
      <Page size="A4" style={styles.page}>
        <View style={[styles.summary, { marginTop: 20 }]}>
          <Text style={[styles.summaryText, styles.boldText]}>
            Migration Readiness Assessment
          </Text>
          {successRate >= 90 && (
            <Text style={[styles.summaryText, { color: "#006600" }]}>
              ✓ READY FOR PRODUCTION: High success rate indicates stable migration
            </Text>
          )}
          {successRate >= 70 && successRate < 90 && (
            <Text style={[styles.summaryText, { color: "#CC6600" }]}>
              ⚠ REQUIRES ATTENTION: Moderate issues detected, address before production
            </Text>
          )}
          {successRate < 70 && (
            <Text style={[styles.summaryText, { color: "#CC0000" }]}>
              ✗ NOT READY: Significant issues require resolution before migration
            </Text>
          )}

          {/* Interface-wise summary */}
          <Text style={[styles.summaryText, styles.boldText, { marginTop: 15 }]}>
            Interface Summary:
          </Text>
          {reportData.map((interfaceResult, index) => {
            const interfaceName = interfaceResult.comparisons?.[0]?.interfaceName || `Interface ${index + 1}`;
            const interfaceComparisons = interfaceResult.comparisons || [];
            const interfaceStats = interfaceComparisons.reduce((acc, comparison) => {
              const category = categorizeComparison(comparison);
              acc.total += 1;
              if (category.isMatch) acc.matches += 1;
              if (category.isDifference) acc.differences += 1;
              if (category.isCannotProcess) acc.cannotProcess += 1;
              return acc;
            }, { total: 0, matches: 0, differences: 0, cannotProcess: 0 });
            
            const rate = interfaceStats.total > 0 ? Math.round((interfaceStats.matches / interfaceStats.total) * 100) : 0;
            const status = rate >= 90 ? "✓" : rate >= 70 ? "⚠" : "✗";
            
            return (
              <Text key={index} style={styles.summaryText}>
                {status} {interfaceName}: {rate}% success ({interfaceStats.matches}/{interfaceStats.total})
              </Text>
            );
          })}
        </View>

        <View style={styles.footer}>
          <Text>
            IntSwitch Testing Tool - SAP Integration Migration Comparison Report
          </Text>
          <Text>© {new Date().getFullYear()} IntSwitch | Generated: {new Date().toLocaleString()}</Text>
        </View>
      </Page>
    </Document>
  ); {interfaceStats.cannotProcess} test case(s) failed due to processing errors
                  </Text>
                  <Text style={[styles.text, { fontSize: 8, color: "#856404" }]}>
                    • Common causes: XML validation failures, schema mismatches, connectivity issues
                  </Text>
                  <Text style={[styles.text, { fontSize: 8, color: "#856404" }]}>
                    • Recommendation: Review error logs and validate payload structures
                  </Text>
                </View>
              )}

              {/* Summary table instead of detailed results - cleaner approach */}
              {interfaceStats.total > 0 && (
                <View style={styles.section}>
                  <Text style={[styles.text, styles.boldText, { marginBottom: 5 }]}>
                    Test Results Summary:
                  </Text>
                  
                  {/* Show only first 5 entries as examples, then summarize */}
                  <View style={styles.tableHeader}>
                    <Text style={[styles.boldText, { width: "40%", fontSize: 8}]}>Sample Message IDs</Text>
                    <Text style={[styles.boldText, { width: "20%", fontSize: 8}]}>Status</Text>
                    <Text style={[styles.boldText, { width: "15%", fontSize: 8}]}>CPI/PO</Text>
                    <Text style={[styles.boldText, { width: "25%", fontSize: 8}]}>Issue Type</Text>
                  </View>
                  
                  {interfaceComparisons.slice(0, 5).map((comparison, compIndex) => {
                    const status = getComparisonStatus(comparison);
                    const messageId = comparison.messageId || "N/A";
                    const shortId = messageId.length > 30 ? messageId.substring(0, 30) + "..." : messageId;
                    const issueType = comparison.error ? "Processing Error" : 
                                     comparison.differences && comparison.differences.length > 0 ? "Data Mismatch" : 
                                     "Unknown";

                    return (
                      <View key={`${interfaceIndex}-${compIndex}`} style={styles.tableRow}>
                        <Text style={[styles.text, { width: "40%", fontSize: 7 }]}>
                          {shortId}
                        </Text>
                        <Text style={[
                          styles.text, 
                          { width: "20%", fontSize: 7 },
                          getStatusStyle(status)
                        ]}>
                          {status}
                        </Text>
                        <Text style={[styles.text, { width: "15%", fontSize: 7 }]}>
                          {(comparison.cpiOutput || "N") + "/" + (comparison.poOutput || "N")}
                        </Text>
                        <Text style={[styles.text, { width: "25%", fontSize: 7 }]}>
                          {issueType}
                        </Text>
                      </View>
                    );
                  })}
                  
                  {interfaceStats.total > 5 && (
                    <View style={[styles.tableRow, { backgroundColor: "#F8F9FA" }]}>
                      <Text style={[styles.text, { width: "100%", fontSize: 7, fontStyle: "italic" }]}>
                        ... and {interfaceStats.total - 5} more test cases with similar patterns
                      </Text>
                    </View>
                  )}
                </View>
              )}

              {/* Recommendations section for problematic interfaces */}
              {interfaceSuccessRate < 100 && (
                <View style={{ marginTop: 10, padding: 8, backgroundColor: "#E7F3FF", borderRadius: 3 }}>
                  <Text style={[styles.text, styles.boldText, { color: "#004085" }]}>
                    Recommended Actions:
                  </Text>
                  {interfaceStats.cannotProcess > 0 && (
                    <Text style={[styles.text, { fontSize: 8, color: "#004085" }]}>
                      • Investigate processing errors and validate XML payloads
                    </Text>
                  )}
                  {interfaceStats.differences > 0 && (
                    <Text style={[styles.text, { fontSize: 8, color: "#004085" }]}>
                      • Review data transformation logic for {interfaceStats.differences} failed comparison(s)
                    </Text>
                  )}
                  <Text style={[styles.text, { fontSize: 8, color: "#004085" }]}>
                    • Verify interface configuration and mapping rules
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.footer}>
              <Text>
                IntSwitch Testing Tool - Page {interfaceIndex + 2} of {reportData.length + 2}
              </Text>
            </View>
          </Page>
        );
      })}

      {/* Final summary page */}
      <Page size="A4" style={styles.page}>
        <View style={[styles.summary, { marginTop: 20 }]}>
          <Text style={[styles.summaryText, styles.boldText]}>
            Migration Readiness Assessment
          </Text>
          {successRate >= 90 && (
            <Text style={[styles.summaryText, { color: "#006600" }]}>
              ✓ READY FOR PRODUCTION: High success rate indicates stable migration
            </Text>
          )}
          {successRate >= 70 && successRate < 90 && (
            <Text style={[styles.summaryText, { color: "#CC6600" }]}>
              ⚠ REQUIRES ATTENTION: Moderate issues detected, address before production
            </Text>
          )}
          {successRate < 70 && (
            <Text style={[styles.summaryText, { color: "#CC0000" }]}>
              ✗ NOT READY: Significant issues require resolution before migration
            </Text>
          )}

          {/* Interface-wise summary */}
          <Text style={[styles.summaryText, styles.boldText, { marginTop: 15 }]}>
            Interface Summary:
          </Text>
          {reportData.map((interfaceResult, index) => {
            const interfaceName = interfaceResult.comparisons?.[0]?.interfaceName || `Interface ${index + 1}`;
            const interfaceComparisons = interfaceResult.comparisons || [];
            const interfaceStats = interfaceComparisons.reduce((acc, comparison) => {
              const category = categorizeComparison(comparison);
              acc.total += 1;
              if (category.isMatch) acc.matches += 1;
              if (category.isDifference) acc.differences += 1;
              if (category.isCannotProcess) acc.cannotProcess += 1;
              return acc;
            }, { total: 0, matches: 0, differences: 0, cannotProcess: 0 });
            
            const rate = interfaceStats.total > 0 ? Math.round((interfaceStats.matches / interfaceStats.total) * 100) : 0;
            const status = rate >= 90 ? "✓" : rate >= 70 ? "⚠" : "✗";
            
            return (
              <Text key={index} style={styles.summaryText}>
                {status} {interfaceName}: {rate}% success ({interfaceStats.matches}/{interfaceStats.total})
              </Text>
            );
          })}
        </View>

        <View style={styles.footer}>
          <Text>
            IntSwitch Testing Tool - SAP Integration Migration Comparison Report
          </Text>
          <Text>© {new Date().getFullYear()} IntSwitch | Generated: {new Date().toLocaleString()}</Text>
        </View>
      </Page>
    </Document>
  ); Common causes: XML validation failures, schema mismatches, connectivity issues
                  </Text>
                  <Text style={[styles.text, { fontSize: 8, color: "#856404" }]}>
                    • Recommendation: Review error logs and validate payload structures
                  </Text>
                </View>
              )}

              {/* Results table for this interface - only show if there are test cases */}
              {interfaceStats.total > 0 && (
                <View style={styles.section}>
                  <Text style={[styles.text, styles.boldText, { marginBottom: 5 }]}>
                    Detailed Test Results:
                  </Text>
                  <View style={styles.tableHeader}>
                    <Text style={[styles.boldText, { width: "25%", fontSize: 8}]}>Message ID</Text>
                    <Text style={[styles.boldText, { width: "15%", fontSize: 8}]}>Status</Text>
                    <Text style={[styles.boldText, { width: "10%", fontSize: 8}]}>Issues</Text>
                    <Text style={[styles.boldText, { width: "8%", fontSize: 8}]}>CPI</Text>
                    <Text style={[styles.boldText, { width: "8%", fontSize: 8}]}>PO</Text>
                    <Text style={[styles.boldText, { width: "34%", fontSize: 8}]}>Error Details</Text>
                  </View>
                  {interfaceComparisons.map((comparison, compIndex) => {
                    const status = getComparisonStatus(comparison);
                    const errorSummary = comparison.error ? 
                      (comparison.error.length > 80 ? comparison.error.substring(0, 80) + "..." : comparison.error) : 
                      "No errors";

                    return (
                      <View key={`${interfaceIndex}-${compIndex}`} style={styles.tableRow}>
                        <Text style={[styles.text, { width: "25%", fontSize: 7 }]}>
                          {comparison.messageId || "N/A"}
                        </Text>
                        <Text style={[
                          styles.text, 
                          { width: "15%", fontSize: 7 },
                          getStatusStyle(status)
                        ]}>
                          {status}
                        </Text>
                        <Text style={[styles.text, { width: "10%", fontSize: 7 }]}>
                          {comparison.differences ? comparison.differences.length : 
                           comparison.error ? "1" : "0"}
                        </Text>
                        <Text style={[styles.text, { width: "8%", fontSize: 7 }]}>
                          {comparison.cpiOutput || "N/A"}
                        </Text>
                        <Text style={[styles.text, { width: "8%", fontSize: 7 }]}>
                          {comparison.poOutput || "N/A"}
                        </Text>
                        <Text style={[styles.text, { width: "34%", fontSize: 6 }]}>
                          {errorSummary}
                        </Text>
                      </View>
                    );
                  })}
                </View>
              )}

              {/* Recommendations section for problematic interfaces */}
              {interfaceSuccessRate < 100 && (
                <View style={{ marginTop: 10, padding: 8, backgroundColor: "#E7F3FF", borderRadius: 3 }}>
                  <Text style={[styles.text, styles.boldText, { color: "#004085" }]}>
                    Recommended Actions:
                  </Text>
                  {interfaceStats.cannotProcess > 0 && (
                    <Text style={[styles.text, { fontSize: 8, color: "#004085" }]}>
                      • Investigate processing errors and validate XML payloads
                    </Text>
                  )}
                  {interfaceStats.differences > 0 && (
                    <Text style={[styles.text, { fontSize: 8, color: "#004085" }]}>
                      • Review data transformation logic for {interfaceStats.differences} failed comparison(s)
                    </Text>
                  )}
                  <Text style={[styles.text, { fontSize: 8, color: "#004085" }]}>
                    • Verify interface configuration and mapping rules
                  </Text>
                </View>
              )}
            </View>
          );
        })}

        {/* Overall recommendations */}
        <View style={[styles.summary, { marginTop: 20 }]}>
          <Text style={[styles.summaryText, styles.boldText]}>
            Migration Readiness Assessment
          </Text>
          {successRate >= 90 && (
            <Text style={[styles.summaryText, { color: "#006600" }]}>
              ✓ READY FOR PRODUCTION: High success rate indicates stable migration
            </Text>
          )}
          {successRate >= 70 && successRate < 90 && (
            <Text style={[styles.summaryText, { color: "#CC6600" }]}>
              ⚠ REQUIRES ATTENTION: Moderate issues detected, address before production
            </Text>
          )}
          {successRate < 70 && (
            <Text style={[styles.summaryText, { color: "#CC0000" }]}>
              ✗ NOT READY: Significant issues require resolution before migration
            </Text>
          )}
        </View>

        <View style={styles.footer}>
          <Text>
            IntSwitch Testing Tool - SAP Integration Migration Comparison Report
          </Text>
          <Text>© {new Date().getFullYear()} IntSwitch | Generated: {new Date().toLocaleString()}</Text>
        </View>
      </Page>
    </Document>
  );
};
// Single Interface PDF Report Component (existing)
const ComparisonReport = ({ reportData, formdata }) => {
  if (!reportData || !Array.isArray(reportData)) {
    return (
      <Document>
        <Page size="A4" style={styles.page}>
          <Text>No data available</Text>
        </Page>
      </Document>
    );
  }

  // Calculate statistics
  const total = reportData.length;
  const matches = reportData.filter(item => item.status === "Match").length;
  const differences = reportData.filter(item => item.status === "Difference").length;
  const cannotProcess = reportData.filter(item => item.status === "Cannot be processed").length;
  const matchRate = total > 0 ? Math.round((matches / total) * 100) : 0;

  const getStatusStyle = (status) => {
    switch (status) {
      case "Match":
        return styles.statusPass;
      case "Difference":
        return styles.statusFail;
      case "Cannot be processed":
        return styles.statusWarning;
      default:
        return {};
    }
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header}>
          <Text style={styles.title}>IntSwitch Comparison Report</Text>
          <Text style={styles.subtitle}>
            Generated on: {new Date().toLocaleString()}
          </Text>
        </View>

        <View style={styles.summary}>
          <Text style={[styles.summaryText, styles.boldText]}>
            Summary Statistics
          </Text>
          <Text style={styles.summaryText}>
            Total Comparisons: {total}
          </Text>
          <Text style={styles.summaryText}>
            Matches: {matches} | Differences: {differences} | Cannot be processed: {cannotProcess}
          </Text>
          <Text style={styles.summaryText}>
            Match Rate: {matchRate}%
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Comparison Results Overview</Text>
          <View style={styles.tableHeader}>
            <Text style={[styles.boldText, { width: "50%", fontSize: 8 }]}>Message ID</Text>
            <Text style={[styles.boldText, { width: "20%", fontSize: 8 }]}>Interface Name</Text>
            <Text style={[styles.boldText, { width: "15%", fontSize: 8 }]}>Flow Name</Text>
            <Text style={[styles.boldText, { width: "15%", fontSize: 8 }]}>Status</Text>
            <Text style={[styles.boldText, { width: "15%", fontSize: 8 }]}>Differences</Text>
            <Text style={[styles.boldText, { width: "15%", fontSize: 8 }]}>CPI O/P</Text>
            <Text style={[styles.boldText, { width: "15%", fontSize: 8 }]}>PO O/P</Text>
          </View>
          {reportData.map((comparison, index) => (
            <View key={`summary-${index}`} style={styles.tableRow}>
              <Text style={[styles.text, { width: "50%", fontSize: 8 }]}>
                {comparison.messageId || "N/A"}
              </Text>
              <Text style={[styles.text, { width: "20%" }]}>
                {comparison.interfaceName || "N/A"}
              </Text>
              <Text style={[styles.text, { width: "15%" }]}>
                {formdata.iFlowName || "N/A"}
              </Text>
              <Text style={[
                styles.text,
                { width: "15%" },
                getStatusStyle(comparison.status)
              ]}>
                {comparison.status || "N/A"}
              </Text>
              <Text style={[styles.text, { width: "15%" }]}>
                {comparison.differences ? comparison.differences.length : "N/A"}
              </Text>
              <Text style={[styles.text, { width: "15%" }]}>
                {comparison.cpiOutput || "N/A"}
              </Text>
              <Text style={[styles.text, { width: "15%" }]}>
                {comparison.poOutput || "N/A"}
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.footer}>
          <Text>
            IntSwitch Testing Tool - SAP Integration Migration Comparison Report
          </Text>
          <Text>© {new Date().getFullYear()} IntSwitch</Text>
        </View>
      </Page>
    </Document>
  );
};

const ComparisonResults = ({ comparisonResult, onBack, formdata, isSingleTestCase }) => {
  const [selectedComparison, setSelectedComparison] = useState(null);
  const [payloadContent, setPayloadContent] = useState(null);
  const [payloadType, setPayloadType] = useState("");
  const [isPayloadModalOpen, setIsPayloadModalOpen] = useState(false);
  const [isLoadingPayload, setIsLoadingPayload] = useState(false);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  // Email dialog states
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);
  const [emailAddress, setEmailAddress] = useState("");
  const [emailSending, setEmailSending] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");
  const [generatedPdfBlob, setGeneratedPdfBlob] = useState(null);

  // Determine result type
  const isMultipleInterfaces = comparisonResult?.type === "multiple-interfaces" ||
    Array.isArray(comparisonResult?.results) ||
    Array.isArray(comparisonResult);

  const handleViewPayload = async (payloadUrl, type) => {
    setIsLoadingPayload(true);
    setIsPayloadModalOpen(true);
    setPayloadType(type);

    try {
      const response = await fetch(`${APIEndpoint}${payloadUrl}`, {
        headers: {
          "Authorization": `Basic ${localStorage.getItem("basicAuth")}`
        },
      });

      if (!response.ok)
        throw new Error(`Failed to fetch payload: ${response.status}`);
      const content = await response.text();
      setPayloadContent(content);
    } catch (error) {
      console.error("Error fetching payload:", error);
      setPayloadContent(`Error loading payload: ${error.message}`);
    } finally {
      setIsLoadingPayload(false);
    }
  };

  const handleDownloadReport = async () => {
    try {
      setIsGeneratingReport(true);

      let pdfBlob;

      if (isMultipleInterfaces) {
        // Handle multiple interfaces report
        const resultsData = comparisonResult.results || [];
        pdfBlob = await pdf(
          <MultipleInterfaceReport
            reportData={resultsData}
            formdata={formdata}
          />
        ).toBlob();
      } else {
        // Handle single interface report
        if (
          !comparisonResult ||
          !comparisonResult.comparisons ||
          !Array.isArray(comparisonResult.comparisons)
        ) {
          throw new Error("Invalid comparison data");
        }

        pdfBlob = await pdf(
          <ComparisonReport
            reportData={comparisonResult.comparisons}
            formdata={formdata}
          />
        ).toBlob();
      }

      // Save the blob for potential emailing
      setGeneratedPdfBlob(pdfBlob);

      // Create a download for the PDF
      const fileName = isMultipleInterfaces
        ? `intswitch-multiple-interfaces-report-${new Date().toISOString().slice(0, 10)}.pdf`
        : `intswitch-comparison-report-${new Date().toISOString().slice(0, 10)}.pdf`;

      saveAs(pdfBlob, fileName);

      // Show the email dialog after download
      setEmailDialogOpen(true);

    } catch (error) {
      console.error("Error generating PDF report:", error);
      setSnackbarMessage("Failed to generate the report. Please try again.");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const handleSendEmail = async () => {
    if (!emailAddress || !generatedPdfBlob) return;

    try {
      setEmailSending(true);

      const formData = new FormData();
      formData.append("to", emailAddress);
      formData.append("file", new File([generatedPdfBlob], "comparison_report.pdf", { type: "application/pdf" }));

      const response = await fetch(`${APIEndpoint}/api/email/send`, {
        method: "POST",
        headers: {
          "Authorization": `Basic ${localStorage.getItem("basicAuth")}`
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to send email: ${response.status}`);
      }

      setSnackbarMessage("Report sent successfully!");
      setSnackbarSeverity("success");
      setEmailDialogOpen(false);
    } catch (error) {
      console.error("Error sending email:", error);
      setSnackbarMessage("Failed to send email. Please try again.");
      setSnackbarSeverity("error");
    } finally {
      setEmailSending(false);
      setSnackbarOpen(true);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  console.log("isSingleTestCase", isSingleTestCase);
  // Render the appropriate results table
  const renderResultsTable = () => {
    if (isMultipleInterfaces) {
      // Handle the case where comparisonResult might be the results array directly
      const resultsData = comparisonResult?.results || comparisonResult || [];

      return (
        <MultipleInterfaceResultsTable
          formdata={formdata}
          results={resultsData} // Use the processed resultsData
          onRowClick={setSelectedComparison}
          onViewPayload={handleViewPayload}
          hoveredRow={hoveredRow}
          setHoveredRow={setHoveredRow}
        />
      );
    } else if (isSingleTestCase) {
      return (
        <SingleTestCaseResultsTable
          formdata={formdata}
          comparisons={comparisonResult.comparisons || []}
          onRowClick={setSelectedComparison}
          onViewPayload={handleViewPayload}
          hoveredRow={hoveredRow}
          setHoveredRow={setHoveredRow}
        />
      );
    } else {
      return (
        <ResultsTable
          formdata={formdata}
          comparisons={comparisonResult.comparisons || []}
          onRowClick={setSelectedComparison}
          onViewPayload={handleViewPayload}
          hoveredRow={hoveredRow}
          setHoveredRow={setHoveredRow}
        />
      );
    }
  };

  return (
    <div className="comparison-results">
      {comparisonResult ? (
        <div className="results-container">
          <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <h2>
              {isMultipleInterfaces ? "Multiple Interface Comparison Results" : "Comparison Results"}
            </h2>
            <button
              onClick={handleDownloadReport}
              className="download-button"
              disabled={isGeneratingReport}
            >
              {isGeneratingReport ? "Generating Report..." : "Download Report"}
            </button>
          </div>

          {renderResultsTable()}

          <ComparisonDetailsModal
            formdata={formdata}
            selectedComparison={selectedComparison}
            onClose={() => setSelectedComparison(null)}
          />

          <PayloadModal
            isOpen={isPayloadModalOpen}
            payloadType={payloadType}
            payloadContent={payloadContent}
            isLoading={isLoadingPayload}
            onClose={() => {
              setIsPayloadModalOpen(false);
              setPayloadContent(null);
            }}
          />
        </div>
      ) : (
        <div className="loading-message">
          <CircularProgress />
          <p>Loading comparison results...</p>
        </div>
      )}

      {/* Email Dialog */}
      <Dialog open={emailDialogOpen} onClose={() => setEmailDialogOpen(false)}>
        <DialogTitle>Email Report</DialogTitle>
        <DialogContent>
          <p>Would you like to email this report to someone?</p>
          <TextField
            autoFocus
            margin="dense"
            label="Email Address"
            type="email"
            fullWidth
            variant="standard"
            value={emailAddress}
            onChange={(e) => setEmailAddress(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmailDialogOpen(false)}>No Thanks</Button>
          <Button
            onClick={handleSendEmail}
            disabled={!emailAddress || emailSending}
          >
            {emailSending ? "Sending..." : "Send Email"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>

      <button onClick={onBack} className="back-button">
        ← Back to Form
      </button>
    </div>
  );
};

export default ComparisonResults;




// Define PDF document styles
// const styles = StyleSheet.create({
//   statusPass: {
//     color: "#008800",
//   },
//   statusFail: {
//     color: "#CC0000",
//   },
//   statusWarning: {
//     color: "#FFA500",
//   },
//   page: {
//     padding: 30,
//     backgroundColor: "#FFFFFF",
    
//   },
//   col4: { width: "25%" },
//   section: {
//     marginBottom: 15,
//     padding: 10,
//     backgroundColor: "#FAFAFA",
//     borderRadius: 5,
//     borderLeftWidth: 3,
//     borderLeftColor: "#003366",
//   },
//   pageBreak: {
//     borderBottomWidth: 1,
//     borderBottomColor: "#EEEEEE",
//     marginVertical: 20,
//   },
//   boldText: {
//     fontWeight: "bold",
//   },
//   header: {
//     marginBottom: 20,
//     borderBottomWidth: 1,
//     borderBottomColor: "#CCCCCC",
//     paddingBottom: 10,
//   },
//   title: {
//     fontSize: 24,
//     fontWeight: "bold",
//     marginBottom: 10,
//     color: "#003366",
//   },
//   subtitle: {
//     fontSize: 14,
//     color: "#666666",
//     marginBottom: 5,
//   },
//   sectionTitle: {
//     fontSize: 16,
//     fontWeight: "bold",
//     marginBottom: 10,
//     marginTop: 15,
//     color: "#003366",
//   },
//   comparisonHeader: {
//     backgroundColor: "#EEF5FF",
//     padding: 10,
//     marginBottom: 10,
//     borderRadius: 5,
//   },
//   fileHeader: {
//     fontSize: 14,
//     fontWeight: "bold",
//     marginBottom: 5,
//   },
//   fileStatus: {
//     fontSize: 12,
//     marginBottom: 5,
//   },
//   tableHeader: {
//     backgroundColor: "#EEEEEE",
//     flexDirection: "row",
//     borderBottomWidth: 1,
//     borderBottomColor: "#CCCCCC",
//     padding: 5,
//   },
//   tableRow: {
//     flexDirection: "row",
//     borderBottomWidth: 1,
//     borderBottomColor: "#EEEEEE",
//     padding: 5,
//   },
//   col1: { width: "30%" },
//   col2: { width: "35%" },
//   col3: { width: "35%" },
//   text: {
//     fontSize: 10,
//     paddingRight: 5,
//   },
//   footer: {
//     position: "absolute",
//     bottom: 30,
//     left: 30,
//     right: 30,
//     textAlign: "center",
//     color: "#666666",
//     fontSize: 10,
//     borderTopWidth: 1,
//     borderTopColor: "#CCCCCC",
//     paddingTop: 10,
//   },
//   infoRow: {
//     flexDirection: "row",
//     marginBottom: 5,
//   },
//   infoLabel: {
//     width: "40%",
//     fontSize: 10,
//     fontWeight: "bold",
//   },
//   infoValue: {
//     width: "60%",
//     fontSize: 10,
//   },
//   summary: {
//     marginTop: 15,
//     marginBottom: 15,
//     padding: 10,
//     backgroundColor: "#F9F9F9",
//     borderRadius: 5,
//   },
//   summaryText: {
//     fontSize: 12,
//     marginBottom: 5,
//   },
//   interfaceSection: {
//     marginBottom: 20,
//     padding: 10,
//     backgroundColor: "#F8F9FA",
//     borderRadius: 5,
//   },
//   interfaceTitle: {
//     fontSize: 14,
//     fontWeight: "bold",
//     marginBottom: 10,
//     color: "#007BFF",
//   },
// });

// // PDF Report Component for Multiple Interfaces
// const MultipleInterfaceReport = ({ reportData, formdata }) => {
//   if (!reportData || !Array.isArray(reportData)) {
//     return (
//       <Document>
//         <Page size="A4" style={styles.page}>
//           <Text>No data available</Text>
//         </Page>
//       </Document>
//     );
//   }

//   // Calculate overall statistics across all interfaces
//   const overallStats = reportData.reduce((acc, interfaceResult) => {
//     const interfaceComparisons = interfaceResult.comparisons || [];
//     const matches = interfaceComparisons.filter(item => item.status === "Match" || item.match === true).length;
//     const differences = interfaceComparisons.filter(item => item.status === "Difference" || item.match === false).length;
//     const cannotProcess = interfaceComparisons.filter(item => item.status === "Cannot be processed").length;

//     acc.total += interfaceComparisons.length;
//     acc.matches += matches;
//     acc.differences += differences;
//     acc.cannotProcess += cannotProcess;
//     return acc;
//   }, { total: 0, matches: 0, differences: 0, cannotProcess: 0 });

//   const matchRate = overallStats.total > 0 ? Math.round((overallStats.matches / overallStats.total) * 100) : 0;

//   // Status style mapping
//   const getStatusStyle = (status, match) => {
//     if (status === "Match" || match === true) return styles.statusPass;
//     if (status === "Difference" || match === false) return styles.statusFail;
//     if (status === "Cannot be processed") return styles.statusWarning;
//     return {};
//   };

//   const getStatusText = (comparison) => {
//     if (comparison.status) return comparison.status;
//     if (comparison.match === true) return "Match";
//     if (comparison.match === false) return "Difference";
//     return "Unknown";
//   };

//   return (
//     <Document>
//       <Page size="A4" style={styles.page}>
//         {/* Header Section */}
//         <View style={styles.header}>
//           <Text style={styles.title}>IntSwitch Multiple Interface Comparison Report</Text>
//           <Text style={styles.subtitle}>
//             SAP Integration Migration Analysis & Validation
//           </Text>
//           <Text style={styles.subtitle}>
//             Generated on: {new Date().toLocaleString()}
//           </Text>
//         </View>

//         {/* Executive Summary with Charts */}
//         <View style={styles.summary}>
//           <Text style={[styles.summaryText, styles.boldText, { fontSize: 14, marginBottom: 8 }]}>
//             Executive Summary
//           </Text>
//           <Text style={[styles.summaryText, { marginBottom: 5 }]}>
//             Total Interfaces Analyzed: {reportData.length}
//           </Text>
//           <Text style={[styles.summaryText, { marginBottom: 5 }]}>
//             Total Message Comparisons: {overallStats.total}
//           </Text>
//           <Text style={[styles.summaryText, { marginBottom: 15 }]}>
//             Overall Success Rate: {matchRate}%
//           </Text>

//           {/* Visual Charts Section */}
//           <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 20 }}>

//             {/* Results Breakdown Bar Chart */}
//             <View style={{ width: '48%' }}>
//               <Text style={[styles.boldText, { fontSize: 11, marginBottom: 10, textAlign: 'center' }]}>
//                 Results Breakdown
//               </Text>
//               <View style={{ backgroundColor: '#f8f9fa', padding: 10, borderRadius: 5 }}>

//                 {/* Matches Bar */}
//                 <View style={{ marginBottom: 8 }}>
//                   <Text style={[styles.text, { fontSize: 9, marginBottom: 2 }]}>
//                     Matches: {overallStats.matches}
//                   </Text>
//                   <View style={{ backgroundColor: '#e9ecef', height: 20, borderRadius: 3 }}>
//                     <View style={{
//                       backgroundColor: '#28a745',
//                       height: 20,
//                       width: `${overallStats.total > 0 ? (overallStats.matches / overallStats.total) * 100 : 0}%`,
//                       borderRadius: 3,
//                       justifyContent: 'center',
//                       alignItems: 'center'
//                     }}>
//                       <Text style={{ color: 'white', fontSize: 8 }}>{overallStats.matches}</Text>
//                     </View>
//                   </View>
//                 </View>

//                 {/* Differences Bar */}
//                 <View style={{ marginBottom: 8 }}>
//                   <Text style={[styles.text, { fontSize: 9, marginBottom: 2 }]}>
//                     Differences: {overallStats.differences}
//                   </Text>
//                   <View style={{ backgroundColor: '#e9ecef', height: 20, borderRadius: 3 }}>
//                     <View style={{
//                       backgroundColor: '#eb808bff',
//                       height: 20,
//                       width: `${overallStats.total > 0 ? (overallStats.differences / overallStats.total) * 100 : 0}%`,
//                       borderRadius: 3,
//                       justifyContent: 'center',
//                       alignItems: 'center'
//                     }}>
//                       <Text style={{ color: 'white', fontSize: 8 }}>{overallStats.differences}</Text>
//                     </View>
//                   </View>
//                 </View>

//                 {/* Cannot Process Bar */}
//                 {overallStats.cannotProcess > 0 && (
//                   <View style={{ marginBottom: 8 }}>
//                     <Text style={[styles.text, { fontSize: 9, marginBottom: 2 }]}>
//                       Cannot Process: {overallStats.cannotProcess}
//                     </Text>
//                     <View style={{ backgroundColor: '#e9ecef', height: 20, borderRadius: 3 }}>
//                       <View style={{
//                         backgroundColor: '#ffc107',
//                         height: 20,
//                         width: `${overallStats.total > 0 ? (overallStats.cannotProcess / overallStats.total) * 100 : 0}%`,
//                         borderRadius: 3,
//                         justifyContent: 'center',
//                         alignItems: 'center'
//                       }}>
//                         <Text style={{ color: 'white', fontSize: 8 }}>{overallStats.cannotProcess}</Text>
//                       </View>
//                     </View>
//                   </View>
//                 )}
//               </View>
//             </View>

//             {/* Summary Statistics Bar Chart */}
//             <View style={{ width: '48%' }}>
//               <Text style={[styles.boldText, { fontSize: 11, marginBottom: 10, textAlign: 'center' }]}>
//                 Summary Statistics
//               </Text>
//               <View style={{ backgroundColor: '#f8f9fa', padding: 10, borderRadius: 5 }}>

//                 {/* Total Messages Bar */}
//                 <View style={{ marginBottom: 8 }}>
//                   <Text style={[styles.text, { fontSize: 9, marginBottom: 2 }]}>
//                     Messages: {overallStats.total}
//                   </Text>
//                   <View style={{ backgroundColor: '#e9ecef', height: 20, borderRadius: 3 }}>
//                     <View style={{
//                       backgroundColor: '#28a745',
//                       height: 20,
//                       width: `${Math.min(100, (overallStats.total / Math.max(overallStats.total, 10)) * 100)}%`,
//                       borderRadius: 3,
//                       justifyContent: 'center',
//                       alignItems: 'center'
//                     }}>
//                       <Text style={{ color: 'white', fontSize: 8 }}>{overallStats.total}</Text>
//                     </View>
//                   </View>
//                 </View>

//                 {/* Total Interfaces Bar */}
//                 <View style={{ marginBottom: 8 }}>
//                   <Text style={[styles.text, { fontSize: 9, marginBottom: 2 }]}>
//                     Interfaces: {reportData.length}
//                   </Text>
//                   <View style={{ backgroundColor: '#e9ecef', height: 20, borderRadius: 3 }}>
//                     <View style={{
//                       backgroundColor: '#007bff',
//                       height: 20,
//                       width: `${Math.min(100, (reportData.length / Math.max(reportData.length, 5)) * 100)}%`,
//                       borderRadius: 3,
//                       justifyContent: 'center',
//                       alignItems: 'center'
//                     }}>
//                       <Text style={{ color: 'white', fontSize: 8 }}>{reportData.length}</Text>
//                     </View>
//                   </View>
//                 </View>

//                 <View>
//                   <Text style={[styles.text, { fontSize: 9, marginBottom: 2 }]}>
//                     Success Rate: {matchRate}%
//                   </Text>
//                   <View style={{ backgroundColor: '#e9ecef', height: 20, borderRadius: 3 }}>
//                     <View style={{
//                       backgroundColor: matchRate >= 80 ? '#28a745' : matchRate >= 60 ? '#ffc107' : '#dc3545',
//                       height: 20,
//                       width: `${matchRate}%`,
//                       borderRadius: 3,
//                       justifyContent: 'center',
//                       alignItems: 'center'
//                     }}>
//                       <Text style={{ color: 'white', fontSize: 8 }}>{matchRate}%</Text>
//                     </View>
//                   </View>
//                 </View>

                
//               </View>
//             </View>
//           </View>
//           {/* Key Performance Indicator */}
//                 <View style={{ marginTop: 10, padding: 8, backgroundColor: matchRate >= 80 ? '#d4edda' : matchRate >= 60 ? '#fff3cd' : '#f8d7da', borderRadius: 3 }}>
//                   <Text style={[styles.boldText, { fontSize: 10, textAlign: 'center', color: matchRate >= 80 ? '#155724' : matchRate >= 60 ? '#856404' : '#721c24' }]}>
//                     {matchRate >= 80 ? 'EXCELLENT' : matchRate >= 60 ? 'GOOD' : 'NEEDS ATTENTION'}
//                   </Text>
//                   <Text style={[styles.text, { fontSize: 8, textAlign: 'center', marginTop: 2 }]}>
//                     Migration Quality Index
//                   </Text>
//                 </View>
//         </View>

//         {/* Status Definitions */}
//         <View style={[styles.section, { marginBottom: 15 }]}>
//           <Text style={[styles.boldText, { fontSize: 12, marginBottom: 8 }]}>
//             Status Definitions
//           </Text>

//           <View style={{ marginBottom: 5 }}>
//             <Text style={[styles.text, styles.boldText, { fontSize: 10 }]}>
//               • Matches ({overallStats.matches}):
//             </Text>
//             <Text style={[styles.text, { fontSize: 9, marginLeft: 10 }]}>
//               Messages where CPI and PO outputs are identical, indicating successful migration with no data transformation issues.
//             </Text>
//           </View>

//           <View style={{ marginBottom: 5 }}>
//             <Text style={[styles.text, styles.boldText, { fontSize: 10 }]}>
//               • Differences ({overallStats.differences}):
//             </Text>
//             <Text style={[styles.text, { fontSize: 9, marginLeft: 10 }]}>
//               Messages where CPI and PO outputs differ, requiring investigation and potential remediation of mapping or transformation logic.
//             </Text>
//           </View>

//           <View style={{ marginBottom: 5 }}>
//             <Text style={[styles.text, styles.boldText, { fontSize: 10 }]}>
//               • Cannot be Processed ({overallStats.cannotProcess}):
//             </Text>
//             <Text style={[styles.text, { fontSize: 9, marginLeft: 10 }]}>
//               Messages that failed to process due to technical errors, invalid formats, or system unavailability.
//             </Text>
//           </View>
//         </View>

//         {/* Interface-by-Interface Results */}
//         {reportData.map((interfaceResult, interfaceIndex) => {
//           const interfaceName = interfaceResult.comparisons?.[0]?.interfaceName || `Interface ${interfaceIndex + 1}`;
//           const interfaceComparisons = interfaceResult.comparisons || [];

//           // Calculate interface-specific stats
//           const interfaceMatches = interfaceComparisons.filter(item => item.status === "Match" || item.match === true).length;
//           const interfaceDifferences = interfaceComparisons.filter(item => item.status === "Difference" || item.match === false).length;
//           const interfaceCannotProcess = interfaceComparisons.filter(item => item.status === "Cannot be processed").length;
//           const interfaceTotal = interfaceComparisons.length;
//           const interfaceSuccessRate = interfaceTotal > 0 ? Math.round((interfaceMatches / interfaceTotal) * 100) : 0;

//           return (
//             <View key={interfaceIndex} style={styles.interfaceSection} wrap={false}>
//               <Text style={[styles.interfaceTitle, { fontSize: 13, marginBottom: 8 }]}>
//                 Interface: {interfaceName}
//               </Text>

//               {/* Interface Statistics */}
//               <View style={{ marginBottom: 10, backgroundColor: '#f5f5f5', padding: 8 }}>
//                 <Text style={[styles.text, { fontSize: 10 }]}>
//                   Total Messages: {interfaceTotal} |
//                   Passed: {interfaceMatches} |
//                   Failed: {interfaceDifferences} |
//                   Cannot Be Processed: {interfaceCannotProcess} |
//                   Success Rate: {interfaceSuccessRate}%
//                 </Text>
//               </View>

//               {/* Results table for this interface */}
//               <View style={styles.section}>
//                 <Text style={[styles.boldText, { fontSize: 11, marginBottom: 15 }]}>
//                   Message Comparison Results
//                 </Text>
//                 <View style={styles.tableHeader}>
//                   <Text style={[styles.boldText, { width: "35%", fontSize: 8 }]}>Message ID</Text>
//                   <Text style={[styles.boldText, { width: "15%", fontSize: 8 }]}>Status</Text>
//                   <Text style={[styles.boldText, { width: "10%", fontSize: 8 }]}>Issues</Text>
//                   <Text style={[styles.boldText, { width: "10%", fontSize: 8 }]}>CPI O/P</Text>
//                   <Text style={[styles.boldText, { width: "10%", fontSize: 8 }]}>PO O/P</Text>
//                   <Text style={[styles.boldText, { width: "20%", fontSize: 8 }]}>Error Details</Text>
//                 </View>

//                 {interfaceComparisons.map((comparison, compIndex) => (
//                   <View key={`${interfaceIndex}-${compIndex}`} style={styles.tableRow}>
//                     <Text style={[styles.text, { width: "35%", fontSize: 7 }]}>
//                       {comparison.messageId || "N/A"}
//                     </Text>
//                     <Text style={[
//                       styles.text,
//                       { width: "15%", fontSize: 7 },
//                       getStatusStyle(comparison.status, comparison.match)
//                     ]}>
//                       {getStatusText(comparison)}
//                     </Text>
//                     <Text style={[styles.text, { width: "10%", fontSize: 7 }]}>
//                       {comparison.differences ? comparison.differences.length : "0"}
//                     </Text>
//                     <Text style={[styles.text, { width: "10%", fontSize: 7 }]}>
//                       {comparison.cpiOutput || "No"}
//                     </Text>
//                     <Text style={[styles.text, { width: "10%", fontSize: 7 }]}>
//                       {comparison.poOutput || "No"}
//                     </Text>
//                     <Text style={[styles.text, { width: "20%", fontSize: 7 }]}>
//                       {comparison.error ? comparison.error.substring(0, 60) + "..." : "No Error"}
//                     </Text>
//                   </View>
//                 ))}
//               </View>

//               {/* Detailed Differences Section for this interface */}
//               {interfaceComparisons.some(comp => comp.differences && comp.differences.length > 0) && (
//                 <View style={[styles.section, { marginTop: 15 }]}>
//                   <Text style={[styles.boldText, { fontSize: 11, marginBottom: 8 }]}>
//                     Detailed Difference Analysis - {interfaceName}
//                   </Text>

//                   {interfaceComparisons.map((comparison, compIndex) =>
//                     comparison.differences && comparison.differences.length > 0 && (
//                       <View key={`details-${interfaceIndex}-${compIndex}`} style={{ marginBottom: 12, borderLeft: '2px solid #e74c3c', paddingLeft: 8 }}>
//                         <Text style={[styles.boldText, { fontSize: 10, marginBottom: 4, color: '#2c3e50' }]}>
//                           Message ID: {comparison.messageId}
//                         </Text>
//                         <Text style={[styles.text, { fontSize: 8, marginBottom: 6, color: '#7f8c8d' }]}>
//                           Interface: {comparison.interfaceName} | CPI Interface: {comparison.CpiInterfaceName}
//                         </Text>

//                         {comparison.differences.map((diff, diffIndex) => (
//                           <View key={`diff-${interfaceIndex}-${compIndex}-${diffIndex}`}
//                             style={{ marginBottom: 8, backgroundColor: '#fdf2f2', padding: 6, borderRadius: 3 }}>
//                             <Text style={[styles.boldText, { fontSize: 9, color: '#c0392b', marginBottom: 2 }]}>
//                               Issue #{diffIndex + 1}: {diff.differenceType}
//                             </Text>
//                             <Text style={[styles.text, { fontSize: 8, marginBottom: 1 }]}>
//                               <Text style={styles.boldText}>Location:</Text> {diff.xpath || "Root Element"}
//                             </Text>
//                             <Text style={[styles.text, { fontSize: 8, marginBottom: 1 }]}>
//                               <Text style={styles.boldText}>Element:</Text> {diff.elementName}
//                             </Text>
//                             <Text style={[styles.text, { fontSize: 8, marginBottom: 1 }]}>
//                               <Text style={styles.boldText}>Expected (PO):</Text> {diff.expectedValue || 'N/A'}
//                             </Text>
//                             <Text style={[styles.text, { fontSize: 8, marginBottom: 1 }]}>
//                               <Text style={styles.boldText}>Actual (CPI):</Text> {diff.actualValue || 'N/A'}
//                             </Text>
//                             {diff.description && (
//                               <Text style={[styles.text, { fontSize: 7, color: '#666', fontStyle: 'italic' }]}>
//                                 Description: {diff.description}
//                               </Text>
//                             )}
//                           </View>
//                         ))}
//                       </View>
//                     )
//                   )}
//                 </View>
//               )}
//             </View>
//           );
//         })}

//         {/* Recommendations Section */}
//         <View style={[styles.section, { marginTop: 20, backgroundColor: '#f8f9fa', padding: 10 }]}>
//           <Text style={[styles.boldText, { fontSize: 12, marginBottom: 8 }]}>
//             Recommendations & Next Steps
//           </Text>
//           <Text style={[styles.text, { fontSize: 9, marginBottom: 4 }]}>
//             • Review all failed comparisons and investigate root causes of differences
//           </Text>
//           <Text style={[styles.text, { fontSize: 9, marginBottom: 4 }]}>
//             • Validate mapping logic and transformation rules in CPI for interfaces with high failure rates
//           </Text>
//           <Text style={[styles.text, { fontSize: 9, marginBottom: 4 }]}>
//             • Address technical errors preventing message processing
//           </Text>
//           <Text style={[styles.text, { fontSize: 9, marginBottom: 4 }]}>
//             • Conduct regression testing after remediation efforts
//           </Text>
//         </View>

//         <View style={styles.footer}>
//           <Text style={{ fontSize: 8 }}>
//             IntSwitch Testing Tool - SAP Integration Migration Comparison Report
//           </Text>
//           <Text style={{ fontSize: 8 }}>
//             © {new Date().getFullYear()} IntSwitch | Confidential & Proprietary
//           </Text>
//         </View>
//       </Page>
//     </Document>
//   );
// };