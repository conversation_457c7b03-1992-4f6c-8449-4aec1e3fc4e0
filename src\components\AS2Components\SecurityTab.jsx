import React, { useState } from "react";
import { MenuItem, Select, FormControl } from "@mui/material";
import API_ENDPOINT from "../../config";

const SecurityTab = ({ onClose, setSnackbar, artifactId }) => {
  const [securityData, setSecurityData] = useState({
    decryptionConfig: { param: "", value: "Channel Configuration" },
    decryptMessage: { param: "", value: "Define Parameter" },
    verifySignature: { param: "", value: "Not Required" },
    showPrivateKeyAlias: false,
    privateKeyAlias: { param: "", value: "Define Parameter" }
  });
  const [loading, setLoading] = useState(false);

  const handleCancel = () => {
    if (onClose) onClose();
  };

  const handleSecurityChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSecurityData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : { ...prev[name], value }
    }));
  };

  const handleParamChange = (e) => {
    const { name, value } = e.target;
    setSecurityData(prev => ({
      ...prev,
      [name]: { ...prev[name], param: value }
    }));
  };

  const handleSubmit = async () => {
    setLoading(true);
    
    try {
      // Create an array of all parameter updates
      const updates = [
        { name: securityData.decryptionConfig.param, value: securityData.decryptionConfig.value },
        { name: securityData.decryptMessage.param, value: securityData.decryptMessage.value },
        { name: securityData.verifySignature.param, value: securityData.verifySignature.value },
        ...(securityData.showPrivateKeyAlias ? 
          [{ name: securityData.privateKeyAlias.param, value: securityData.privateKeyAlias.value }] : 
          [])
      ].filter(update => update.name); // Only include parameters with names

      if (updates.length === 0) {
        setSnackbar({
          open: true,
          message: "Please enter at least one parameter name",
          severity: "error"
        });
        setLoading(false);
        return;
      }

      // Execute all updates sequentially
      let lastResponse = null;
      for (const update of updates) {
        const endpoint = `${API_ENDPOINT}/api/updateParam/${artifactId}/${update.name}`;
        
        const response = await fetch(endpoint, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            parameterValue: update.value,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update parameter ${update.name}`);
        }
        
        // Store the last successful response
        lastResponse = await response.json();
      }

      // Show the success message from the API response
      if (lastResponse && lastResponse.status) {
        setSnackbar({
          open: true,
          message: lastResponse.status,
          severity: "success"
        });
      } else {
        setSnackbar({
          open: true,
          message: "Security parameters updated successfully",
          severity: "success"
        });
      }
    } catch (error) {
      console.error("Error updating security parameters:", error);
      setSnackbar({
        open: true,
        message: error.message || "Failed to update some security parameters",
        severity: "error"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', paddingBottom: '80px' }}>
      {/* MESSAGE DECRYPTION Section */}
      <h4 style={{ 
        display: 'flex', 
        color: '#6c757d', 
        fontSize: '12px', 
        fontWeight: 'bold',
        marginBottom: '20px',
        textTransform: 'uppercase',
        letterSpacing: '0.5px',
        margin: '0 0 20px 0'
      }}>
        MESSAGE DECRYPTION
      </h4>
      
      <div style={{ marginBottom: '40px' }}>
        {/* Decryption Configuration Type */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: '150px 1fr 200px', 
          gap: '10px', 
          alignItems: 'center',
          marginBottom: '15px'
        }}>
          <label style={{ color: '#333', fontSize: '14px' }}>
            Decryption Configuration Type:
          </label>
          <input
            type="text"
            name="decryptionConfig"
            value={securityData.decryptionConfig.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <FormControl size="small">
            <Select
              name="decryptionConfig"
              value={securityData.decryptionConfig.value}
              onChange={handleSecurityChange}
              style={{ fontSize: '14px' }}
            >
              <MenuItem value="Channel Configuration">Channel Configuration</MenuItem>
              <MenuItem value="Dynamic">Dynamic</MenuItem>
              <MenuItem value="Incoming AS2 Headers">Incoming AS2 Headers</MenuItem>
            </Select>
          </FormControl>
        </div>

        {/* Decrypt Message */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: '150px 1fr 200px', 
          gap: '10px', 
          alignItems: 'center',
          marginBottom: '15px'
        }}>
          <label style={{ color: '#333', fontSize: '14px' }}>
            Decrypt Message:
          </label>
          <input
            type="text"
            name="decryptMessage"
            value={securityData.decryptMessage.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <input
              type="checkbox"
              name="showPrivateKeyAlias"
              checked={securityData.showPrivateKeyAlias}
              onChange={(e) => setSecurityData(prev => ({
                ...prev,
                showPrivateKeyAlias: e.target.checked
              }))}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer'
              }}
            />
            <span style={{ fontSize: '14px' }}>Show Private Key Alias</span>
          </div>
        </div>

        {/* Private Key Alias - conditionally rendered */}
        {securityData.showPrivateKeyAlias && (
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: '150px 1fr 200px', 
            gap: '10px', 
            alignItems: 'center',
            marginBottom: '15px'
          }}>
            <label style={{ color: '#007bff', fontSize: '14px' }}>
              Private Key Alias: *
            </label>
            <input
              type="text"
              name="privateKeyAlias"
              value={securityData.privateKeyAlias.param}
              onChange={handleParamChange}
              placeholder="Define Parameter"
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <input
              type="text"
              name="privateKeyAlias"
              value={securityData.privateKeyAlias.value}
              onChange={handleSecurityChange}
              placeholder="Define Value"
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
          </div>
        )}
      </div>

      {/* SIGNATURE VERIFICATION Section */}
      <h4 style={{ 
        display: 'flex',
        color: '#6c757d', 
        fontSize: '12px', 
        fontWeight: 'bold',
        marginBottom: '20px',
        textTransform: 'uppercase',
        letterSpacing: '0.5px',
        margin: '0 0 20px 0'
      }}>
        SIGNATURE VERIFICATION
      </h4>
      
      <div>
        {/* Verify Signature */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: '150px 1fr 200px', 
          gap: '10px', 
          alignItems: 'center',
          marginBottom: '15px'
        }}>
          <label style={{ color: '#333', fontSize: '14px' }}>
            Verify Signature:
          </label>
          <input
            type="text"
            name="verifySignature"
            value={securityData.verifySignature.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <FormControl size="small">
            <Select
              name="verifySignature"
              value={securityData.verifySignature.value}
              onChange={handleSecurityChange}
              style={{ fontSize: '14px' }}
            >
              <MenuItem value="Not Required">Not Required</MenuItem>
              <MenuItem value="Trusted Certificate">Trusted Certificate</MenuItem>
              <MenuItem value="Trusted Root Certificate">Trusted Root Certificate</MenuItem>
            </Select>
          </FormControl>
        </div>
      </div>

      {/* Bottom Action Bar */}
      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '10px',
        padding: '15px 20px',
        borderTop: '1px solid #dee2e6',
        backgroundColor: '#f8f9fa',
        marginTop: '20px'
      }}>
        <button
          onClick={handleSubmit}
          disabled={loading}
          style={{
            padding: '8px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer',
            fontWeight: 'bold',
            opacity: loading ? 0.7 : 1
          }}
        >
          {loading ? 'Updating...' : 'OK'}
        </button>
        <button
          onClick={handleCancel}
          style={{
            padding: '8px 20px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default SecurityTab;