import React from "react";

const ScenarioSelector = ({ selection, setSelection, showTitle }) => {
  const getButtonStyle = (buttonType) => {
    const isActive = selection === buttonType;

    return {
      backgroundColor: "transparent",
      color: isActive ? "#3b82f6" : "#6b7280",
      borderRadius: "0",
      width: "auto",
      fontWeight: isActive ? "600" : "400",
      fontSize: "14px",
      border: "none",
      borderBottom: isActive ? "2px solid #3b82f6" : "2px solid transparent",
      padding: "12px 16px",
      cursor: "pointer",
      transition: "all 0.3s ease",
      boxShadow: "none",
      textAlign: "center",
      marginRight: "0",
      marginBottom: "0",
      whiteSpace: "nowrap",
      outline: "none",
    };
  };

  const containerStyle = {
    padding: "20px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: "20px",
  };

  const titleStyle = {
    color: "#1f2937",
    fontSize: "18px",
    fontWeight: "600",
    margin: "0",
    textAlign: "center",
  };

  const buttonGroupStyle = {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "center",
    gap: "0",
    borderBottom: "1px solid #e5e7eb",
  };

  return (
    <div style={containerStyle}>
      {showTitle && !selection && (
        <h4 style={titleStyle}>Please select the testing scenario</h4>
      )}
      <div style={buttonGroupStyle}>
        <button
          onClick={() => setSelection("single")}
          style={getButtonStyle("single")}
          onMouseEnter={(e) => {
            if (selection !== "single") {
              e.target.style.color = "#374151";
            }
          }}
          onMouseLeave={(e) => {
            if (selection !== "single") {
              e.target.style.color = "#6b7280";
            }
          }}
        >
          Single test case With Single Interface
        </button>
        <button
          onClick={() => setSelection("multiple")}
          style={getButtonStyle("multiple")}
          onMouseEnter={(e) => {
            if (selection !== "multiple") {
              e.target.style.color = "#374151";
            }
          }}
          onMouseLeave={(e) => {
            if (selection !== "multiple") {
              e.target.style.color = "#6b7280";
            }
          }}
        >
          Multiple test cases With Single Interface
        </button>
        <button
          onClick={() => setSelection("multiple-interfaces")}
          style={getButtonStyle("multiple-interfaces")}
          onMouseEnter={(e) => {
            if (selection !== "multiple-interfaces") {
              e.target.style.color = "#374151";
            }
          }}
          onMouseLeave={(e) => {
            if (selection !== "multiple-interfaces") {
              e.target.style.color = "#6b7280";
            }
          }}
        >
          Multiple Interfaces With Multiple Test Case
        </button>
      </div>
    </div>
  );
};

export default ScenarioSelector;