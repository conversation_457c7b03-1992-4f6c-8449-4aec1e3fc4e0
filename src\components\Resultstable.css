/* Enhanced <PERSON>rror <PERSON> */
.enhanced-error-modal {
  margin-top: 36px;
  min-width: 600px;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.error-modal-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 10px 0;
}

.error-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  background-color: #fafafa;
}

.error-section h4 {
  margin: 0 0 10px 0;
  color: #d32f2f;
  font-size: 16px;
  font-weight: 600;
}

.error-content {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
}

.error-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #d32f2f;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  line-height: 1.4;
}

/* Solutions Accordion Styles */
.solutions-accordion {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.accordion-header {
  width: 100%;
  padding: 15px 20px;
  background-color: #f5f5f5;
  border: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  transition: background-color 0.2s ease;
}

.accordion-header:hover {
  background-color: #eeeeee;
}

.accordion-header:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.accordion-icon {
  transition: transform 0.2s ease;
  color: #666;
}

.accordion-icon.expanded {
  transform: rotate(180deg);
}

.accordion-content {
  border-top: 1px solid #e0e0e0;
  background-color: white;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 500px;
    opacity: 1;
  }
}

.solutions-content {
  padding: 20px;
}

/* Solutions Table Styles */
.solutions-table-container {
  overflow-x: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.solutions-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background-color: white;
}

.solutions-table thead {
  background-color: #f8f9fa;
}

.solutions-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e0e0e0;
  position: sticky;
  top: 0;
  background-color: #f8f9fa;
}

.solutions-table th:first-child {
  width: 50px;
  text-align: center;
}

.solutions-table th:nth-child(2) {
  width: 45%;
}

.solutions-table th:nth-child(3) {
  width: 50%;
}

.solutions-table tbody tr {
  border-bottom: 1px solid #e8e8e8;
  transition: background-color 0.2s ease;
}

.solutions-table tbody tr:hover {
  background-color: #f8fdf8;
}

.solutions-table tbody tr:last-child {
  border-bottom: none;
}

.solutions-table td {
  padding: 16px;
  vertical-align: top;
  line-height: 1.5;
}

.index-cell {
  text-align: center;
  font-weight: 600;
  color: #666;
  background-color: #fafafa;
  width: 50px;
}

.cause-cell {
  color: #e65100;
  background-color: #fff8f0;
  border-left: 4px solid #ff9800;
  font-weight: 500;
}

.solution-cell {
  color: #2e7d32;
  background-color: #f0f8f0;
  border-left: 4px solid #4caf50;
  font-weight: 500;
}

.no-solutions,
.solutions-error {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.solutions-error {
  color: #d32f2f;
  background-color: #ffebee;
  border-radius: 4px;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .enhanced-error-modal {
    margin-top: 36px;
    min-width: 90vw;
    max-width: 90vw;
    margin: 36px auto 0;
  }
  
  .accordion-header {
    padding: 12px 15px;
    font-size: 14px;
  }
  
  .solutions-content {
    padding: 15px;
  }
  
  .solution-item {
    padding: 12px;
  }
  
  .cause-text,
  .solution-text {
    font-size: 13px;
    padding: 6px;
  }
}

/* ResultsTable.css */
.results-table-container {
  width: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 20px;
  margin: 20px 0;
  overflow-x: auto;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.comparison-table th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  padding: 12px 8px;
  border-bottom: 2px solid #dee2e6;
  text-align: left;
  font-size: 13px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.comparison-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #e9ecef;
  font-size: 12px;
  vertical-align: middle;
}

.comparison-table tbody tr:hover {
  background-color: #f8f9fa;
}

.comparison-table tbody tr.Passed {
  border-left: 3px solid #28a745;
}

.comparison-table tbody tr.Failed {
  border-left: 3px solid #dc3545;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-match {
  color: #4CAF50;
  background-color: #fefefe;
}

.status-difference {
  color: rgb(237, 16, 16);
  background-color: #fefefe;
}

.status-cannot-process {
  color: #FF9800;
  background-color: #fefefe;
}

.status-unknown {
  color: #383d41;
}

/* View payload buttons */
.view-payload-button {
  color: black;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  background: white;
  box-shadow: 0 2px 4px #007bff4d;
}

.view-payload-button:hover {
  color: blue;
  transform: translateY(-1px);
}

.no-payload {
  color: #dc3545;
  font-size: 12px;
}

/* Details buttons */
.details-button {
  color: black;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  background: white;
  box-shadow: 0 2px 4px #007bff4d;
}

.details-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #6f789c 0%, #6eb3db 100%);
  color: white;
  transform: translateY(-1px);
}

.details-button.disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Error view buttons */
.error-view-button {
  background: linear-gradient(135deg, #415edf 0%, #9e4b6f 100%);
  color: white;
  border: none;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.error-view-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #a71e2a 0%, #721c24 100%);
  transform: translateY(-1px);
}

.error-view-button.disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Summary stats styling */
.summary-stats {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;

  margin-bottom: 25px;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-item {
  text-align: center;
  padding: 10px;
  min-width: 120px;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 28px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.stat-value.match {
  color: #28a745;
}

.stat-value.no-match {
  color: #dc3545;
}

.stat-value.cannot-process {
  color: #FF9800;
}

.stat-value.success-rate {
  color: #007bff;
}
/* Chatbot styles */
.chatbot-toggle-button {
  position: fixed;
  bottom: 4rem;
  right: 20px;
  z-index: 1000;
  background-color: #1565c0;
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chatbot-container {
  border: 1px solid #dee2e6;
  position: fixed;
  bottom: 8rem;
  right: 20px;
  z-index: 999;
  width: 25%;
  height: 500px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
  overflow: hidden;
}

/* Responsive design */
@media (max-width: 1200px) {
  .results-table-container {
    overflow-x: auto;
  }
  
  .comparison-table {
    min-width: 1200px;
  }
}

@media (max-width: 768px) {
  .summary-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .stat-item {
    min-width: auto;
  }
  
  .error-modal {
    width: 95%;
  }
  
  .chatbot-container {
    width: 80%;
    right: 10%;
  }
}