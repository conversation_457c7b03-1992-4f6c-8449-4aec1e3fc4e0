import React, { useState } from "react";
import CircularProgress from "@mui/material/CircularProgress";
import ResultsTable from "../ResultsTable";
import ComparisonDetailsModal from "../ComparisonDetailsModal";
import PayloadModal from "../PayloadModal";
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  pdf,
} from "@react-pdf/renderer";
import { saveAs } from "file-saver";
import "../../App.css";
import APIEndpoint from "../../config";
import formatXml from "xml-formatter";
import { height } from "@mui/system";
import SingleTestCaseResultsTable from "../SingleTestCaseResultsTable";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Snackbar,
  Alert,
} from "@mui/material";
import NeoResultsTable from "./NeoResultstable";

// Define PDF document styles
const styles = StyleSheet.create({
  statusPass: {
    color: "#008800", // Green for matches
  },
  statusFail: {
    color: "#CC0000", // Red for differences
  },
  statusWarning: {
    color: "#FFA500", // Orange for cannot be processed
  },
  page: {
    padding: 30,
    backgroundColor: "#FFFFFF",
  },
  col4: { width: "25%" },
  section: {
    marginBottom: 15,
    padding: 10,
    backgroundColor: "#FAFAFA",
    borderRadius: 5,
    borderLeftWidth: 3,
    borderLeftColor: "#003366",
  },
  pageBreak: {
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
    marginVertical: 20,
  },
  boldText: {
    fontWeight: "bold",
  },
  header: {
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#CCCCCC",
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#003366",
  },
  subtitle: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 5,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
    marginTop: 15,
    color: "#003366",
  },
  comparisonHeader: {
    backgroundColor: "#EEF5FF",
    padding: 10,
    marginBottom: 10,
    borderRadius: 5,
  },
  fileHeader: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 5,
  },
  fileStatus: {
    fontSize: 12,
    marginBottom: 5,
  },
  tableHeader: {
    backgroundColor: "#EEEEEE",
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#CCCCCC",
    padding: 5,
  },
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
    padding: 5,
  },
  col1: { width: "30%" },
  col2: { width: "35%" },
  col3: { width: "35%" },
  text: {
    fontSize: 10,
    paddingRight: 5,
  },
  footer: {
    position: "absolute",
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: "center",
    color: "#666666",
    fontSize: 10,
    borderTopWidth: 1,
    borderTopColor: "#CCCCCC",
    paddingTop: 10,
  },
  infoRow: {
    flexDirection: "row",
    marginBottom: 5,
  },
  infoLabel: {
    width: "40%",
    fontSize: 10,
    fontWeight: "bold",
  },
  infoValue: {
    width: "60%",
    fontSize: 10,
  },
  summary: {
    marginTop: 15,
    marginBottom: 15,
    padding: 10,
    backgroundColor: "#F9F9F9",
    borderRadius: 5,
  },
  summaryText: {
    fontSize: 12,
    marginBottom: 5,
  },
});

// PDF Report Document Component for Neo Comparison
const NeoComparisonReport = ({ reportData, formdata }) => {
  if (!reportData || !Array.isArray(reportData)) {
    return (
      <Document>
        <Page size="A4" style={styles.page}>
          <Text>No data available</Text>
        </Page>
      </Document>
    );
  }

  // Calculate statistics
  const total = reportData.length;
  const matches = reportData.filter(item => item.status === "Match").length;
  const differences = reportData.filter(item => item.status === "Difference").length;
  const cannotProcess = reportData.filter(item => item.status === "Cannot be processed").length;
  const matchRate = total > 0 ? Math.round((matches / total) * 100) : 0;

  // Status style mapping
  const getStatusStyle = (status) => {
    switch(status) {
      case "Match":
        return styles.statusPass;
      case "Difference":
        return styles.statusFail;
      case "Cannot be processed":
        return styles.statusWarning;
      default:
        return {};
    }
  };

  return (
    <Document>
      <Page size="A2" style={styles.page}>
        <View style={styles.header}>
          <Text style={styles.title}>IntSwitch Neo Comparison Report</Text>
          <Text style={styles.subtitle}>
            Generated on: {new Date().toLocaleString()}
          </Text>
        </View>

        <View style={styles.summary}>
          <Text style={[styles.summaryText, styles.boldText]}>
            Summary Statistics
          </Text>
          <Text style={styles.summaryText}>
            Total Comparisons: {total}
          </Text>
          <Text style={styles.summaryText}>
            Matches: {matches} | Differences: {differences} | Cannot be processed: {cannotProcess}
          </Text>
          <Text style={styles.summaryText}>
            Match Rate: {matchRate}%
          </Text>
        </View>

        {/* Add summary table here */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Comparison Results Overview</Text>
          <View style={styles.tableHeader}>
            <Text style={[styles.boldText, { width: "20%" }]}>Key</Text>
            <Text style={[styles.boldText, { width: "20%" }]}>Interface Name</Text>
            <Text style={[styles.boldText, { width: "15%" }]}>Flow Name</Text>
            <Text style={[styles.boldText, { width: "15%" }]}>Status</Text>
            <Text style={[styles.boldText, { width: "10%" }]}>Differences</Text>
            <Text style={[styles.boldText, { width: "10%" }]}>CPI O/P</Text>
            <Text style={[styles.boldText, { width: "10%" }]}>Neo O/P</Text>
          </View>
          {reportData.map((comparison, index) => (
            <View key={`summary-${index}`} style={styles.tableRow}>
              <Text style={[styles.text, { width: "20%", fontSize: 8 }]}>
                {comparison.key || "N/A"}
              </Text>
              <Text style={[styles.text, { width: "20%" }]}>
                {comparison.interfaceName || "N/A"}
              </Text>
              <Text style={[styles.text, { width: "15%" }]}>
                {formdata.iFlowName || "N/A"}
              </Text>
              <Text style={[
                styles.text, 
                { width: "15%" },
                getStatusStyle(comparison.status)
              ]}>
                {comparison.status || "N/A"}
              </Text>
              <Text style={[styles.text, { width: "10%" }]}>
                {comparison.differences ? comparison.differences.length : "N/A"}
              </Text>
              <Text style={[styles.text, { width: "10%" }]}>
                {comparison.cpiOutput || "N/A"}
              </Text>
              <Text style={[styles.text, { width: "10%" }]}>
                {comparison.neoOutput || "N/A"}
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.footer}>
          <Text>
            IntSwitch Testing Tool - SAP Neo Integration Migration Comparison Report
          </Text>
          <Text>© {new Date().getFullYear()} IntSwitch</Text>
        </View>
      </Page>
    </Document>
  );
};

const NeoComparisonResults = ({ comparisonResult, onBack, formdata, isSingleTestCase }) => {
  const [selectedComparison, setSelectedComparison] = useState(null);
  const [payloadContent, setPayloadContent] = useState(null);
  const [payloadType, setPayloadType] = useState("");
  const [isPayloadModalOpen, setIsPayloadModalOpen] = useState(false);
  const [isLoadingPayload, setIsLoadingPayload] = useState(false);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  
  // New state for email dialog
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);
  const [emailAddress, setEmailAddress] = useState("");
  const [emailSending, setEmailSending] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");
  const [generatedPdfBlob, setGeneratedPdfBlob] = useState(null);

  const handleViewPayload = async (payloadUrl, type) => {
    setIsLoadingPayload(true);
    setIsPayloadModalOpen(true);
    setPayloadType(type);

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${APIEndpoint}${payloadUrl}`, {
        headers: {
          "Authorization": `Basic ${localStorage.getItem("basicAuth")}`
        },
      });

      if (!response.ok)
        throw new Error(`Failed to fetch payload: ${response.status}`);
      const content = await response.text();
      setPayloadContent(content);
    } catch (error) {
      console.error("Error fetching payload:", error);
      setPayloadContent(`Error loading payload: ${error.message}`);
    } finally {
      setIsLoadingPayload(false);
    }
  };

  const handleDownloadReport = async () => {
    try {
      setIsGeneratingReport(true);

      // Make sure we have valid comparison data
      if (
        !comparisonResult ||
        !comparisonResult.comparisons &&
        !Array.isArray(comparisonResult)
      ) {
        throw new Error("Invalid comparison data");
      }

      // Handle both array and object with comparisons property
      const comparisons = Array.isArray(comparisonResult) 
        ? comparisonResult 
        : comparisonResult.comparisons || [];

      // Generate the PDF blob
      const pdfBlob = await pdf(
        <NeoComparisonReport
          reportData={comparisons}
          formdata={formdata}
        />
      ).toBlob();

      // Save the blob for potential emailing
      setGeneratedPdfBlob(pdfBlob);

      // Create a download for the PDF
      saveAs(
        pdfBlob,
        `intswitch-neo-comparison-report-${new Date()
          .toISOString()
          .slice(0, 10)}.pdf`
      );

      // Show the email dialog after download
      setEmailDialogOpen(true);
      
    } catch (error) {
      console.error("Error generating PDF report:", error);
      setSnackbarMessage("Failed to generate the report. Please try again.");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const handleSendEmail = async () => {
    if (!emailAddress || !generatedPdfBlob) return;
    
    try {
      setEmailSending(true);
      
      // Create FormData to send the file and email address
      const formData = new FormData();
      formData.append("to", emailAddress);
      formData.append("file", new File([generatedPdfBlob], "neo_comparison_report.pdf", { type: "application/pdf" }));
      
      const token = localStorage.getItem("token");
      const response = await fetch(`${APIEndpoint}/api/email/send`, {
        method: "POST",
        headers: {
          "Authorization": `Basic ${localStorage.getItem("basicAuth")}`
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to send email: ${response.status}`);
      }

      setSnackbarMessage("Report sent successfully!");
      setSnackbarSeverity("success");
      setEmailDialogOpen(false);
    } catch (error) {
      console.error("Error sending email:", error);
      setSnackbarMessage("Failed to send email. Please try again.");
      setSnackbarSeverity("error");
    } finally {
      setEmailSending(false);
      setSnackbarOpen(true);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  // Handle both array and object with comparisons property
  const comparisons = Array.isArray(comparisonResult) 
    ? comparisonResult 
    : comparisonResult?.comparisons || [];

  return (
    <div className="comparison-results">
      {comparisonResult ? (
        <div className="results-container">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <h2>Neo Comparison Results</h2>
            <button
              onClick={handleDownloadReport}
              className="download-button"
              disabled={isGeneratingReport}
            >
              {isGeneratingReport ? "Generating Report..." : "Download Report"}
            </button>
          </div>
         
          {isSingleTestCase ? (
            <NeoResultsTable
              formdata={formdata}
              comparisons={comparisons}
              onRowClick={setSelectedComparison}
              onViewPayload={handleViewPayload}
              hoveredRow={hoveredRow}
              setHoveredRow={setHoveredRow}
              isNeoComparison={true}
            />
          ) : (
            <ResultsTable
              formdata={formdata}
              comparisons={comparisons}
              onRowClick={setSelectedComparison}
              onViewPayload={handleViewPayload}
              hoveredRow={hoveredRow}
              setHoveredRow={setHoveredRow}
              isNeoComparison={true}
            />
          )}
          <ComparisonDetailsModal
            formdata={formdata}
            selectedComparison={selectedComparison}
            onClose={() => setSelectedComparison(null)}
            isNeoComparison={true}
          />
          <PayloadModal
            isOpen={isPayloadModalOpen}
            payloadType={payloadType}
            payloadContent={payloadContent}
            isLoading={isLoadingPayload}
            onClose={() => {
              setIsPayloadModalOpen(false);
              setPayloadContent(null);
            }}
          />
        </div>
      ) : (
        <div className="loading-message">
          <CircularProgress />
          <p>Loading neo comparison results...</p>
        </div>
      )}

      {/* Email Dialog */}
      <Dialog open={emailDialogOpen} onClose={() => setEmailDialogOpen(false)}>
        <DialogTitle>Email Report</DialogTitle>
        <DialogContent>
          <p>Would you like to email this report to someone?</p>
          <TextField
            autoFocus
            margin="dense"
            label="Email Address"
            type="email"
            fullWidth
            variant="standard"
            value={emailAddress}
            onChange={(e) => setEmailAddress(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmailDialogOpen(false)}>No Thanks</Button>
          <Button 
            onClick={handleSendEmail}
            disabled={!emailAddress || emailSending}
          >
            {emailSending ? "Sending..." : "Send Email"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>

      <button onClick={onBack} className="back-button">
        ← Back to Form
      </button>
    </div>
  );
};

export default NeoComparisonResults;