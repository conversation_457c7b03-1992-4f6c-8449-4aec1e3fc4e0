import React, { useState } from "react";
import DateTime from "react-datetime";
import moment from "moment";
import "react-datetime/css/react-datetime.css";
import APIEndpoint from "../config";  
import "../App.css";

// Helper to safely parse ISO strings
const parseISOToMoment = (isoString) => {
  return isoString ? moment(isoString) : null;
};

const IntermediatePage = ({ onBack, onProceed }) => {
  const [timeRange, setTimeRange] = useState({
    startTime: "",
    endTime: ""
  });
  const [isLoading, setIsLoading] = useState(false);

const handleChange = (name, value) => {
  if (moment.isMoment(value) && value.isValid()) {
    setTimeRange(prev => ({ ...prev, [name]: value.toISOString() }));
  } else if (typeof value === "string") {
    setTimeRange(prev => ({ ...prev, [name]: value }));
  }
};



  const handleSubmit = async () => {
    if (!timeRange.startTime || !timeRange.endTime) {
      alert("Please select both start and end times");
      return;
    }

    setIsLoading(true);

    try {
      const token = localStorage.getItem("token");

      const url = `${APIEndpoint}/api/extractDetails?startTime=${encodeURIComponent(
        timeRange.startTime
      )}&endTime=${encodeURIComponent(timeRange.endTime)}`;
      
      const response = await fetch(url, {
        method: "GET",
        headers: {
           "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API Error ${response.status}`);
      }

      const result = await response.json();
      onProceed(result);
    } catch (error) {
      console.error("Failed to fetch interfaces:", error);
      alert(`Failed to fetch data: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="intermediate-page">
      <h3>Select Time Range</h3>
      <p style={{fontSize:"1rem"}}>Please Specify the Time Range from SAP PI/PO Monitoring to fetch available service interfaces</p>
      
      <table className="time-range-table">
        <thead>
          <tr style={{ backgroundColor: "#272D4F", color: "white" }}>
            <th style={{ padding: "10px", border: "1px solid #ccc", textAlign: "left" }}>
              Time Range
            </th>
            <th style={{ padding: "10px", border: "1px solid #ccc", textAlign: "left" }}>
              Enter details
            </th>
          </tr>
        </thead>
        <tbody>
          {["startTime", "endTime"].map((type) => (
            <tr key={type}>
              <td>{type === "startTime" ? "Start Time" : "End Time"}</td>
              <td>
                <div className="custom-datetime">
                <DateTime
                  size="xsmall"
                  value={parseISOToMoment(timeRange[type])}
                  onChange={(value) => handleChange(type, value)}
                  dateFormat="YYYY-MM-DD"
                  timeFormat="HH:mm:ss.SSS"
                  inputProps={{
                    placeholder: "YYYY-MM-DD HH:mm:ss.SSS",
                    style: { width: "250px" },
                  }}
                /></div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <div className="button-group">
        <button onClick={onBack} className="back-button">
          ← Back to Scenario Selection
        </button>
        <button 
          onClick={handleSubmit} 
          className="back-button"
          disabled={isLoading}
        >
          {isLoading ? "Fetching Data..." : "Fetch Interfaces →"}
        </button>
      </div>
    </div>
  );
};

export default IntermediatePage;




