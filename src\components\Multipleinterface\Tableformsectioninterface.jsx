import React, { useState, useEffect } from 'react';
import { Table, TableHead, TableBody, TableRow, TableCell, Checkbox, Select, MenuItem, FormControl, Paper, Button } from '@mui/material';
import APIEndpoint from "../../config";
import axios from "axios";

const Tableformsectioninterface = ({ 
  formData, 
  setFormData, 
  serviceInterfaces = [], 
  selectedInterfaces = [],
  onConfigure,
  onDeploy
}) => {
  const [interfaceData, setInterfaceData] = useState([]);
  const [packages, setPackages] = useState({});
  const [iFlows, setIFlows] = useState({});
  const [loadingPackages, setLoadingPackages] = useState({});
  const [loadingIFlows, setLoadingIFlows] = useState({});
  const [deploying, setDeploying] = useState({});
  const [deployMessages, setDeployMessages] = useState({});

  const getApiTenantName = (tenant) => {
    if (!tenant) return tenant;
    return tenant.charAt(0).toUpperCase() + tenant.slice(1).toLowerCase();
  };

  useEffect(() => {
    if (selectedInterfaces && selectedInterfaces.length > 0) {
      const initialData = selectedInterfaces.map((iface, index) => ({
        id: index + 1,
        interfaceName: iface.interface?.name || `Interface ${index + 1}`,
        selected: true,
        tenant: iface.tenant || '',
        package: iface.package || '',
        iFlow: iface.iFlow || '',
        adapter: iface.adapter || ''
      }));
      setInterfaceData(initialData);
    } else if (serviceInterfaces && serviceInterfaces.length > 0) {
      const initialData = serviceInterfaces.map((iface, index) => ({
        id: index + 1,
        interfaceName: iface.interface?.name || `Interface ${index + 1}`,
        selected: false,
        tenant: '',
        package: '',
        iFlow: '',
        adapter: ''
      }));
      setInterfaceData(initialData);
    }
  }, [selectedInterfaces, serviceInterfaces]);

  const fetchPackagesForInterface = async (interfaceId, tenant) => {
    if (!tenant) return;
    setLoadingPackages(prev => ({ ...prev, [interfaceId]: true }));
    try {
      const apiTenantName = getApiTenantName(tenant);
      const response = await axios.get(
        `${APIEndpoint}/packages`,
        { 
          params: { configName: apiTenantName },
          headers: { 'Content-Type': 'application/json' }
        }
      );
      
      const packageKeys = Object.keys(response.data || {});
      const packagesArray = packageKeys.map(key => ({
        id: key,
        name: response.data[key].packageId
      }));
      
      setPackages(prev => ({ ...prev, [interfaceId]: packagesArray }));
    } catch (error) {
      console.error(`Error fetching packages for interface ${interfaceId}:`, error);
    } finally {
      setLoadingPackages(prev => ({ ...prev, [interfaceId]: false }));
    }
  };

  const fetchIFlowsForInterface = async (interfaceId, packageName, tenant) => {
    if (!packageName || !tenant) return;
    setLoadingIFlows(prev => ({ ...prev, [interfaceId]: true }));
    try {
      const apiTenantName = getApiTenantName(tenant);
      const response = await axios.get(
        `${APIEndpoint}/api/integration/artifacts`,
        { 
          params: { 
            packageId: packageName,
            configName: apiTenantName 
          },
          headers: { 'Content-Type': 'application/json' }
        }
      );
      
      const iFlowData = response.data.body || {};
      const iFlowNames = Object.keys(iFlowData).map(key => ({
        id: key,
        name: iFlowData[key].artifactName
      }));
      
      setIFlows(prev => ({ ...prev, [interfaceId]: iFlowNames }));
    } catch (error) {
      console.error(`Error fetching IFlows for interface ${interfaceId}:`, error);
    } finally {
      setLoadingIFlows(prev => ({ ...prev, [interfaceId]: false }));
    }
  };

  const updateInterfaceData = (id, field, value) => {
    setInterfaceData(prev => {
      const updated = prev.map(item => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };
          
          if (field === 'tenant') {
            updatedItem.package = '';
            updatedItem.iFlow = '';
            if (value) fetchPackagesForInterface(id, value);
          } else if (field === 'package') {
            updatedItem.iFlow = '';
            if (value && updatedItem.tenant) {
              fetchIFlowsForInterface(id, value, updatedItem.tenant);
            }
          }
          
          return updatedItem;
        }
        return item;
      });
      
      const selectedData = updated.filter(item => item.selected);
      setFormData(prev => ({
        ...prev,
        selectedInterfaces: selectedData.map(item => item.interfaceName),
        multipleInterfaceData: selectedData
      }));
      
      return updated;
    });
  };

  const handleRowConfigure = (rowData) => {
    if (!rowData.iFlow) {
      alert("Please select an I-Flow Name before configuring");
      return;
    }
    onConfigure(rowData);
  };

  const handleRowDeploy = async (rowData) => {
    if (!rowData.iFlow || !rowData.tenant) {
      alert("Please select both I-Flow Name and Tenant before deploying");
      return;
    }

    setDeploying(prev => ({ ...prev, [rowData.id]: true }));
    setDeployMessages(prev => ({ ...prev, [rowData.id]: null }));

    try {
      const apiTenantName = getApiTenantName(rowData.tenant);
      const response = await axios.post(
        `${APIEndpoint}/api/deploy`,
        null,
        {
          params: {
            artifactId: rowData.iFlow,
            configName: apiTenantName
          },
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      setDeployMessages(prev => ({
        ...prev,
        [rowData.id]: "Deployment started successfully! Your artifact is being processed."
      }));
    } catch (error) {
      console.error("Deployment error:", error);
      setDeployMessages(prev => ({
        ...prev,
        [rowData.id]: "Deployment failed. Please try again."
      }));
    } finally {
      setDeploying(prev => ({ ...prev, [rowData.id]: false }));
    }
  };

  const isConfigureEnabled = (rowData) => {
    return rowData.adapter && 
      (rowData.adapter === "SFTP" ||
       rowData.adapter === "IDOC" ||
       rowData.adapter === "HTTPS" ||
       rowData.adapter === "AS2" ||
       rowData.adapter === "XI" ||
       rowData.adapter === "SOAP");
  };

  return (
    <div style={{ width: '100%', marginBottom: '20px' }}>
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>Select</TableCell>
              <TableCell>No.</TableCell>
              <TableCell>Interface Name</TableCell>
              <TableCell>Tenant Name</TableCell>
              <TableCell>Package Name</TableCell>
              <TableCell>I-Flow Name</TableCell>
              <TableCell>Adapter Name</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {interfaceData.map((row) => (
              <React.Fragment key={row.id}>
                <TableRow>
                  <TableCell>
                    <Checkbox
                      checked={row.selected}
                      onChange={(e) => updateInterfaceData(row.id, 'selected', e.target.checked)}
                    />
                  </TableCell>
                  <TableCell>{row.id}</TableCell>
                  <TableCell>{row.interfaceName}</TableCell>
                  <TableCell>
                    <FormControl size="small" sx={{ minWidth: 120 }}>
                      <Select
                        value={row.tenant}
                        onChange={(e) => updateInterfaceData(row.id, 'tenant', e.target.value)}
                        displayEmpty
                      >
                        <MenuItem value="">Select Tenant</MenuItem>
                        <MenuItem value="Dev">DEV</MenuItem>
                        <MenuItem value="QA">QA</MenuItem>
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <FormControl size="small" sx={{ minWidth: 180 }}>
                      <Select
                        value={row.package}
                        onChange={(e) => updateInterfaceData(row.id, 'package', e.target.value)}
                        displayEmpty
                        disabled={!row.tenant || loadingPackages[row.id]}
                      >
                        <MenuItem value="">
                          {loadingPackages[row.id] ? 'Loading...' : 'Select Package'}
                        </MenuItem>
                        {(packages[row.id] || []).map((pkg) => (
                          <MenuItem key={pkg.id} value={pkg.name}>
                            {pkg.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <FormControl size="small" sx={{ minWidth: 180 }}>
                      <Select
                        value={row.iFlow}
                        onChange={(e) => updateInterfaceData(row.id, 'iFlow', e.target.value)}
                        displayEmpty
                        disabled={!row.package || loadingIFlows[row.id]}
                      >
                        <MenuItem value="">
                          {loadingIFlows[row.id] ? 'Loading...' : 'Select I-Flow'}
                        </MenuItem>
                        {(iFlows[row.id] || []).map((flow) => (
                          <MenuItem key={flow.id} value={flow.name}>
                            {flow.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <FormControl size="small" sx={{ minWidth: 130 }}>
                      <Select
                        value={row.adapter}
                        onChange={(e) => updateInterfaceData(row.id, 'adapter', e.target.value)}
                        displayEmpty
                      >
                        <MenuItem value="">Select Adapter</MenuItem>
                        <MenuItem value="AS2">AS2</MenuItem>
                        <MenuItem value="HTTPS">HTTPS</MenuItem>
                        <MenuItem value="IDOC">IDOC</MenuItem>
                        <MenuItem value="SFTP">SFTP</MenuItem>
                        <MenuItem value="SOAP">SOAP</MenuItem>
                        <MenuItem value="JMS">JMS</MenuItem>
                        <MenuItem value="Mail">MAIL</MenuItem>
                        <MenuItem value="PROCESS_DIRECT">PROCESS DIRECT</MenuItem>
                        <MenuItem value="XI">XI</MenuItem>
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <Button
                        variant="outlined"
                        size="small"
                        disabled={!isConfigureEnabled(row)}
                        onClick={() => handleRowConfigure(row)}
                      >
                        Configure
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        disabled={!row.iFlow || !row.tenant || deploying[row.id]}
                        onClick={() => handleRowDeploy(row)}
                      >
                        {deploying[row.id] ? 'Deploying...' : 'Deploy'}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
                {deployMessages[row.id] && (
                  <TableRow>
                    <TableCell colSpan={8} style={{
                      backgroundColor: deployMessages[row.id].includes("failed")
                        ? "#ffebee"
                        : "#e8f5e9",
                      border: deployMessages[row.id].includes("failed")
                        ? "1px solid #ef9a9a"
                        : "1px solid #a5d6a7",
                      color: deployMessages[row.id].includes("failed") ? "#c62828" : "#2e7d32",
                      padding: '8px 16px',
                      fontWeight: '500'
                    }}>
                      {deployMessages[row.id]}
                    </TableCell>
                  </TableRow>
                )}
              </React.Fragment>
            ))}
          </TableBody>
        </Table>
      </Paper>
    </div>
  );
};

export default Tableformsectioninterface;