import React, { useState } from "react";
import {
  FaUserShield,
  FaKey,
  FaBuilding,
  FaLock,
  FaSave,
  FaGlobe,
  FaLink,
  FaMagic,
} from "react-icons/fa";
import { FaExclamationCircle, FaCheckCircle } from "react-icons/fa";
import { FaUser } from "react-icons/fa";
import APIEndpoint from "../config";
import { useNavigate } from "react-router-dom";
import axios from "axios";

const RegisterUser = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    // API Credentials
    apiUsername: "",
    apiPassword: "",
    apiTenantUrl: "",
    apiTokenUrl: "",

    // Integration Flow Credentials
    flowUsername: "",
    flowPassword: "",
    flowTenantUrl: "",
    flowTokenUrl: "",

    // Common Configuration
    configName: "Dev",
  });

  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleAutoFill = () => {
    setFormData({
      // API Credentials (from the "api" section)
      apiUsername: "sb-98451377-8a73-4691-9c91-bd759f08e4bc!b63626|it!b16077",
      apiPassword:
        "08489eca-ed83-4348-85d7-a99b29501055$P9LgE19v_fxnxpSy4jhFRiF4PVaIyrOU3ek4twQNJf8=",
      apiTenantUrl: "https://inccpidev.it-cpi001.cfapps.eu10.hana.ondemand.com",
      apiTokenUrl:
        "https://inccpidev.authentication.eu10.hana.ondemand.com/oauth/token",

      // Integration Flow Credentials (from the "cpi" section)
      flowUsername:
        "sb-418a8dc7-6d80-4d95-bae6-f8b2d2d018cc!b63626|it-rt-inccpidev!b16077",
      flowPassword:
        "aa40c953-86c5-4f9c-80f2-961113fb8164$eXbpr6Mn6pGRUwdRsUO34jzUeB3d8K-gbIb3Xtr7DVo=",
      flowTenantUrl:
        "https://inccpidev.it-cpi001-rt.cfapps.eu10.hana.ondemand.com",
      flowTokenUrl:
        "https://inccpidev.authentication.eu10.hana.ondemand.com/oauth/token",

      configName: "Dev",
    });
    setSuccessMessage("Form autofilled with data. Please verify and submit!");
    setErrorMessage("");
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleRegister();
    }
  };

  const handleRegister = async () => {
    // Validate all fields
    if (
      !formData.apiUsername ||
      !formData.apiPassword ||
      !formData.apiTenantUrl ||
      !formData.apiTokenUrl ||
      !formData.flowUsername ||
      !formData.flowPassword ||
      !formData.flowTenantUrl ||
      !formData.flowTokenUrl
    ) {
      setErrorMessage("Please fill in all fields");
      setSuccessMessage("");
      return;
    }

    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    try {
      // Prepare data for CPI Credentials (saveTenatDetailsCI endpoint)
      const cpiCredentialsData = {
        clientId: formData.apiUsername,
        clientSecret: formData.apiPassword,
        tokenUrl: formData.apiTokenUrl,
        url: formData.apiTenantUrl,
        env: formData.configName,
      };

      // Prepare data for Integration Config (saveToDb endpoint)
      const integrationConfigData = {
        id: 1,
        configName: formData.configName,
        username: formData.flowUsername,
        password: formData.flowPassword,
        tokenUrl: formData.flowTokenUrl,
        baseUrl: formData.flowTenantUrl,
      };

      // Prepare data for new API endpoints
      const apiCredentialsData = {
        baseUrl: formData.flowTenantUrl,
        configName: formData.configName,
        password: formData.flowPassword,
        tokenUrl: formData.flowTokenUrl,
        username: formData.flowUsername,
      };

      const cpiCredentialsDataForNewEndpoint = {
        clientId: formData.apiUsername,
        clientSecret: formData.apiPassword,
        env: formData.configName,
        tokenUrl: formData.apiTokenUrl,
        url: formData.apiTenantUrl,
      };

      // Make parallel API calls to all endpoints
      // const [
      //   cpiResponse,
      //   configResponse,
      //   apiCredentialsResponse,
      //   cpiNewEndpointResponse,
      // ] = await Promise.all([
      // fetch(`${APIEndpoint}/api/saveTenatDetailsCI`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(cpiCredentialsData)
      // }),
      // fetch(`${APIEndpoint}/api/saveToDb`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(integrationConfigData)
      // }),
      //   fetch(`${APIEndpoint}/api/setapicredentials`, {
      //     method: "POST", // Note: This should probably be POST based on the data being sent
      //     headers: {
      //       "Content-Type": "application/json",
      //     },
      //     body: JSON.stringify(apiCredentialsData),
      //   }),
      //   fetch(`${APIEndpoint}/api/setcpidata`, {
      //     method: "POST", // Note: This should probably be POST based on the data being sent
      //     headers: {
      //       "Content-Type": "application/json",
      //     },
      //     body: JSON.stringify(cpiCredentialsDataForNewEndpoint),
      //   }),
      // ]);

      // Check all responses
      // const cpiResult = await cpiResponse.json().catch(() => null);
      // const configResult = await configResponse.json().catch(() => null);
      // const apiCredentialsResult = await apiCredentialsResponse.json().catch(() => null);
      // const cpiNewEndpointResult = await cpiNewEndpointResponse.json().catch(() => null);

      // // Handle case where configuration already exists
      // if (configResult?.status === 'exists') {
      //   setErrorMessage(configResult.message || 'Configuration with these credentials already exists');
      //   return;
      // }

      // if (!cpiResponse.ok || !configResponse.ok || !apiCredentialsResponse.ok || !cpiNewEndpointResponse.ok) {
      //   throw new Error(
      //     (cpiResult?.message || 'Failed to save CPI credentials') +
      //     (configResult?.message ? ' | ' + configResult.message : '') +
      //     (apiCredentialsResult?.message ? ' | ' + apiCredentialsResult.message : '') +
      //     (cpiNewEndpointResult?.message ? ' | ' + cpiNewEndpointResult.message : '')
      //   );
      // }

      // All calls succeeded
      const [apiCredentialsResponse, cpiNewEndpointResponse] =
        await Promise.all([
          axios.post(
            `${APIEndpoint}/api/setapicredentials`,
            apiCredentialsData,
            {
              headers: {
                "Content-Type": "application/json",
              }
            }
          ),
          axios.post(
            `${APIEndpoint}/api/setcpidata`,
            cpiCredentialsDataForNewEndpoint,
            {
              headers: {
                "Content-Type": "application/json",
              }
            }
          ),
        ]);

      // Check responses
      if (
        apiCredentialsResponse.status === 200 &&
        cpiNewEndpointResponse.status === 200
      ) {
        setSuccessMessage(
          `Credentials successfully registered for ${formData.configName} environment`
        );

        // Reset form
        setFormData({
          apiUsername: "",
          apiPassword: "",
          apiTenantUrl: "",
          apiTokenUrl: "",
          flowUsername: "",
          flowPassword: "",
          flowTenantUrl: "",
          flowTokenUrl: "",
          configName: "Dev",
        });

        navigate("/dashboard/extract");
      } else {
        throw new Error(
          (apiCredentialsResponse.data?.message ||
            "Failed to save API credentials") +
            (cpiNewEndpointResponse.data?.message
              ? " | " + cpiNewEndpointResponse.data.message
              : "")
        );
      }
    } catch (error) {
      console.error("Registration error:", error);
      setErrorMessage(
        error.response?.data?.message ||
          error.message ||
          "Failed to register credentials"
      );
    } finally {
      setIsLoading(false);
    }
  };

  axios.interceptors.request.use(
  (config) => {
    console.log('Request headers:', config.headers);
    console.log('Authorization header:', config.headers.Authorization);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <div style={styles.header}>
          <div style={styles.logo}>
            <FaUserShield size={36} color="#084a94" />
          </div>
          <h2 style={styles.title}>SAP CI User Registration</h2>
          <p style={styles.subtitle}>
            Register credentials for Dev or QA environment
          </p>
        </div>

        {errorMessage && (
          <div style={styles.errorAlert}>
            <FaExclamationCircle style={styles.errorIcon} />
            <span>{errorMessage}</span>
          </div>
        )}

        {successMessage && (
          <div style={styles.successAlert}>
            <FaCheckCircle style={styles.successIcon} />
            <span>{successMessage}</span>
          </div>
        )}

        <label style={styles.sectionLabel}>
          <span>User Credentials</span>
        </label>

        {/* API Credentials Section */}
        <label style={styles.sectionLabel}>
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <span>Plan: integration-flow</span>
            <span>Service: Process Integration Runtime</span>
          </div>
        </label>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaUser style={styles.inputIcon} />
            <span>Client ID</span>
          </label>
          <input
            type="text"
            name="apiUsername"
            value={formData.apiUsername}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter API Client ID"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaKey style={styles.inputIcon} />
            <span>Client Secret</span>
          </label>
          <input
            type="password"
            name="apiPassword"
            value={formData.apiPassword}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter API Client Secret"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaGlobe style={styles.inputIcon} />
            <span>Tenant URL</span>
          </label>
          <input
            type="url"
            name="apiTenantUrl"
            value={formData.apiTenantUrl}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter API Tenant URL"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaLink style={styles.inputIcon} />
            <span>Token URL</span>
          </label>
          <input
            type="url"
            name="apiTokenUrl"
            value={formData.apiTokenUrl}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter API Token URL"
            style={styles.input}
          />
        </div>

        {/* Integration Flow Credentials Section */}
        <label style={styles.sectionLabel}>
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <span>Plan: api</span>
            <span>Service: Process Integration Runtime</span>
          </div>
        </label>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaUser style={styles.inputIcon} />
            <span>Client ID</span>
          </label>
          <input
            type="text"
            name="flowUsername"
            value={formData.flowUsername}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter Flow Client ID"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaKey style={styles.inputIcon} />
            <span>Client Secret</span>
          </label>
          <input
            type="password"
            name="flowPassword"
            value={formData.flowPassword}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter Flow Client Secret"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaGlobe style={styles.inputIcon} />
            <span>Tenant URL</span>
          </label>
          <input
            type="url"
            name="flowTenantUrl"
            value={formData.flowTenantUrl}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter Flow Tenant URL"
            style={styles.input}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaLink style={styles.inputIcon} />
            <span>Token URL</span>
          </label>
          <input
            type="url"
            name="flowTokenUrl"
            value={formData.flowTokenUrl}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter Flow Token URL"
            style={styles.input}
          />
        </div>

        {/* Common Configuration Section */}
        <label style={styles.sectionLabel}>
          <span>Environment Configuration</span>
        </label>
        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaBuilding style={styles.inputIcon} />
            <span>Tenant</span>
          </label>
          <select
            name="configName"
            value={formData.configName}
            onChange={handleChange}
            style={styles.select}
          >
            <option value="Dev">Development</option>
            <option value="QA">Quality Assurance</option>
          </select>
        </div>

        <div style={styles.buttonGroup}>
          <button
            onClick={handleAutoFill}
            style={styles.secondaryButton}
            disabled={isLoading}
          >
            <FaMagic style={styles.buttonIcon} />
            Autofill
          </button>
          <button
            onClick={handleRegister}
            style={styles.primaryButton}
            disabled={isLoading}
          >
            <FaSave style={styles.buttonIcon} />
            {isLoading ? "Registering..." : "Register Credentials"}
          </button>
        </div>

        <div style={styles.footer}>
          <p style={styles.helpText}>
            Note: All credentials will be encrypted and stored securely
          </p>
        </div>
      </div>
    </div>
  );
};

const styles = {
  errorAlert: {
    display: "flex",
    alignItems: "flex-start", // Changed from 'center' to 'flex-start'
    padding: "12px 16px",
    backgroundColor: "#fee2e2",
    color: "#b91c1c",
    borderRadius: "8px",
    fontSize: "14px",
    marginBottom: "20px",
    wordBreak: "break-word", // Added to handle long messages
  },
  container: {
    marginTop: "20px",
    display: "flex",
    justifyContent: "center",
    fontFamily: "'Segoe UI', 'Roboto', 'Oxygen', sans-serif",
  },
  card: {
    width: "46%",
    backgroundColor: "white",
    borderRadius: "12px",
    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
    padding: "20px",
  },
  header: {
    textAlign: "center",
    marginBottom: "32px",
  },
  logo: {
    marginBottom: "16px",
    display: "flex",
    justifyContent: "center",
  },
  title: {
    fontSize: "24px",
    fontWeight: "600",
    color: "#1e293b",
    marginBottom: "8px",
  },
  subtitle: {
    fontSize: "14px",
    color: "#64748b",
    margin: 0,
  },
  formContainer: {
    marginBottom: "20px",
  },
  formGroup: {
    marginBottom: "20px",
  },
  sectionLabel: {
    display: "block",
    fontSize: "14px",
    fontWeight: "600",
    color: "#475569",
    marginBottom: "16px",
    paddingBottom: "8px",
    borderBottom: "1px solid #e2e8f0",
  },
  label: {
    display: "flex",
    alignItems: "center",
    fontSize: "14px",
    fontWeight: "500",
    color: "#475569",
    marginBottom: "8px",
  },
  inputIcon: {
    width: "16px",
    height: "16px",
    marginRight: "8px",
    color: "#64748b",
  },
  input: {
    width: "100%",
    padding: "12px 16px 12px 40px",
    fontSize: "14px",
    border: "1px solid #e2e8f0",
    borderRadius: "8px",
    backgroundColor: "#f8fafc",
    transition: "all 0.2s",
    boxSizing: "border-box",
    outline: "none",
    color: "#1e293b",
  },
  select: {
    width: "100%",
    padding: "12px 16px 12px 40px",
    fontSize: "14px",
    border: "1px solid #e2e8f0",
    borderRadius: "8px",
    backgroundColor: "#f8fafc",
    color: "#1e293b",
    appearance: "none",
    backgroundImage:
      "url(\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\")",
    backgroundRepeat: "no-repeat",
    backgroundPosition: "right 1rem center",
    backgroundSize: "1em",
  },
  buttonGroup: {
    display: "flex",
    gap: "12px",
    marginTop: "16px",
  },
  primaryButton: {
    flex: 1,
    padding: "14px 16px",
    fontSize: "15px",
    fontWeight: "500",
    backgroundColor: "#084a94",
    color: "white",
    border: "none",
    borderRadius: "8px",
    cursor: "pointer",
    transition: "all 0.2s",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    boxShadow: "0 2px 4px rgba(8, 74, 148, 0.2)",
  },
  secondaryButton: {
    flex: 1,
    padding: "14px 16px",
    fontSize: "15px",
    fontWeight: "500",
    backgroundColor: "#e2e8f0",
    color: "#475569",
    border: "none",
    borderRadius: "8px",
    cursor: "pointer",
    transition: "all 0.2s",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  buttonIcon: {
    marginRight: "8px",
  },
  errorAlert: {
    display: "flex",
    alignItems: "center",
    padding: "12px 16px",
    backgroundColor: "#fee2e2",
    color: "#b91c1c",
    borderRadius: "8px",
    fontSize: "14px",
    marginBottom: "20px",
  },
  successAlert: {
    display: "flex",
    alignItems: "center",
    padding: "12px 16px",
    backgroundColor: "#dcfce7",
    color: "#166534",
    borderRadius: "8px",
    fontSize: "14px",
    marginBottom: "20px",
  },
  errorIcon: {
    width: "16px",
    height: "16px",
    marginRight: "8px",
    flexShrink: 0,
  },
  successIcon: {
    width: "16px",
    height: "16px",
    marginRight: "8px",
    flexShrink: 0,
  },
  footer: {
    marginTop: "24px",
    textAlign: "center",
    borderTop: "1px solid #f1f5f9",
    paddingTop: "16px",
  },
  helpText: {
    fontSize: "13px",
    color: "#64748b",
    margin: 0,
  },
};

export default RegisterUser;
