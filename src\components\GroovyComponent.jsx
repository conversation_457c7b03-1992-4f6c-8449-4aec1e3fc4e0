import React, { useState, useEffect } from "react";
import {
  FaCode,
  FaDownload,
  FaInfoCircle,
  FaSearch,
  FaTimes,
  FaUpload,
} from "react-icons/fa";
import APIEndpoint from "../config";
import { Button } from "@mui/material";

const GroovyComponent = () => {
  const [groovyData, setGroovyData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedScript, setSelectedScript] = useState(null);
  const [scriptContent, setScriptContent] = useState("");
  const [contentLoading, setContentLoading] = useState(false);
  const [contentError, setContentError] = useState(null);

  // Upload dialog states
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [uploadData, setUploadData] = useState({
    scriptTag: "",
    scriptUseCases: "",
    scriptDescription: "",
    scriptFile: null,
  });
  const [uploadLoading, setUploadLoading] = useState(false);
  const [uploadError, setUploadError] = useState(null);

  useEffect(() => {
    fetchGroovyData();
  }, []);

  useEffect(() => {
    if (searchTerm === "") {
      setFilteredData(groovyData);
    } else {
      const filtered = groovyData.filter((item) =>
        item.scriptTag.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredData(filtered);
    }
  }, [searchTerm, groovyData]);

  const fetchGroovyData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${APIEndpoint}/api/groovy/fetchAllData`, {
        method: "GET",
        headers: { "Content-Type": "application/json" ,
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setGroovyData(data);
      setFilteredData(data);
    } catch (err) {
      setError(err.message);
      console.error("Error fetching groovy data:", err);
    } finally {
      setLoading(false);
    }
  };
  const [showCpiUploadModal, setShowCpiUploadModal] = useState(false);
  const [cpiUploadData, setCpiUploadData] = useState({
    artifactId: "",
    scriptTag: "",
  });
  const [cpiUploadLoading, setCpiUploadLoading] = useState(false);
  const [cpiUploadError, setCpiUploadError] = useState(null);

  const openCpiUploadModal = () => {
    setShowCpiUploadModal(true);
    setCpiUploadData({
      artifactId: "",
      scriptTag: selectedScript.scriptTag, // Auto-fill the scriptTag
    });
    setCpiUploadError(null);
  };

  const closeCpiUploadModal = () => {
    setShowCpiUploadModal(false);
    setCpiUploadError(null);
  };

  const handleCpiInputChange = (e) => {
    const { name, value } = e.target;
    setCpiUploadData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCpiUploadSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!cpiUploadData.artifactId.trim()) {
      setCpiUploadError("Artifact ID is required");
      return;
    }
    if (!cpiUploadData.scriptTag.trim()) {
      setCpiUploadError("Script Tag is required");
      return;
    }

    try {
      setCpiUploadLoading(true);
      setCpiUploadError(null);

      const formData = new FormData();
      formData.append("artifactId", cpiUploadData.artifactId);
      formData.append("scriptTag", cpiUploadData.scriptTag);

      const response = await fetch(`${APIEndpoint}/api/groovy/addResources`, {
        method: "POST",
        body: formData,
        headers: {
          "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Success - close dialog and show success message
      alert("Script successfully uploaded to CPI!");
      closeCpiUploadModal();
    } catch (err) {
      setCpiUploadError(err.message);
      console.error("Error uploading to CPI:", err);
    } finally {
      setCpiUploadLoading(false);
    }
  };

  const copyModalContent = () => {
    navigator.clipboard
      .writeText(scriptContent)
      .then(() => {
        alert("Script copied to clipboard!");
      })
      .catch((err) => {
        console.error("Failed to copy: ", err);
        alert("Failed to copy script");
      });
  };

  const downloadScript = () => {
    const element = document.createElement("a");
    const file = new Blob([scriptContent], { type: "text/plain" });
    element.href = URL.createObjectURL(file);
    element.download = selectedScript.scriptFileName || "script.groovy";
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const fetchScriptContent = async (scriptUrl) => {
    try {
      setContentLoading(true);
      setContentError(null);
      const response = await fetch(`${APIEndpoint}${scriptUrl}`, {
        method: "GET",
        headers: { "Content-Type": "text/plain",
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const text = await response.text();
      setScriptContent(text || "No content available");
    } catch (err) {
      setContentError(err.message);
      console.error("Error fetching script content:", err);
    } finally {
      setContentLoading(false);
    }
  };

  const handleViewScript = (script) => {
    setSelectedScript(script);
    fetchScriptContent(script.viewGroovyScript);
  };

  const closeModal = () => {
    setSelectedScript(null);
    setScriptContent("");
    setContentError(null);
  };

  // Upload dialog functions
  const openUploadDialog = () => {
    setShowUploadDialog(true);
    setUploadError(null);
    setUploadData({
      scriptTag: "",
      scriptUseCases: "",
      scriptDescription: "",
      scriptFile: null,
    });
  };

  const closeUploadDialog = () => {
    setShowUploadDialog(false);
    setUploadError(null);
    setUploadData({
      scriptTag: "",
      scriptUseCases: "",
      scriptDescription: "",
      scriptFile: null,
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUploadData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const fileName = file.name;
      const fileExt = fileName.split(".").pop().toLowerCase();

      if (fileExt !== "groovy") {
        setUploadError("Only .groovy files are allowed");
        return;
      }

      setUploadData((prev) => ({
        ...prev,
        scriptFile: file,
      }));
      setUploadError(null); // Clear any previous errors
    }
  };

  const handleUploadSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!uploadData.scriptTag.trim()) {
      setUploadError("Script Tag is required");
      return;
    }
    if (!uploadData.scriptUseCases.trim()) {
      setUploadError("Script Use Cases is required");
      return;
    }
    if (!uploadData.scriptDescription.trim()) {
      setUploadError("Script Description is required");
      return;
    }
    if (!uploadData.scriptFile) {
      setUploadError("Script File is required");
      return;
    }

    const fileName = uploadData.scriptFile.name;
    const fileExt = fileName.split(".").pop().toLowerCase();
    if (fileExt !== "groovy") {
      setUploadError("Only .groovy files are allowed");
      return;
    }

    try {
      setUploadLoading(true);
      setUploadError(null);

      const formData = new FormData();
      formData.append("scriptTag", uploadData.scriptTag);
      formData.append("scriptUseCases", uploadData.scriptUseCases);
      formData.append("scriptDescription", uploadData.scriptDescription);
      formData.append("scriptFile", uploadData.scriptFile);

      const response = await fetch(`${APIEndpoint}/api/groovy/add`, {
        method: "POST",
        body: formData,
        headers: {
          "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Success - refresh data and close dialog
      alert("Script uploaded successfully!");
      closeUploadDialog();
      fetchGroovyData(); // Refresh the list
    } catch (err) {
      setUploadError(err.message);
      console.error("Error uploading script:", err);
    } finally {
      setUploadLoading(false);
    }
  };

  if (loading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
          fontSize: "16px",
          color: "#666",
        }}
      >
        Loading Groovy Components...
      </div>
    );
  }

  if (error) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
          fontSize: "16px",
          color: "#dc2626",
          flexDirection: "column",
          gap: "10px",
        }}
      >
        <FaInfoCircle size={24} />
        Error loading data: {error}
        <button
          onClick={fetchGroovyData}
          style={{
            padding: "8px 16px",
            backgroundColor: "#3b82f6",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div
      style={{
        padding: "20px",
        minHeight: "100vh",
        padding: "2rem",
        backgroundColor: "white",
        position: "relative",
      }}
    >
      <div
        style={{
          marginBottom: "30px",
          borderBottom: "2px solid #e5e7eb",
          paddingBottom: "15px",
        }}
      >
        <h1
          style={{
            fontSize: "28px",
            fontWeight: "bold",
            color: "#1f2937",
            margin: "0 0 8px 0",
          }}
        >
          Reusable Groovy Components
        </h1>
        <p
          style={{
            color: "#6b7280",
            fontSize: "16px",
            margin: "0",
          }}
        >
          Browse,Upload and Download pre-built Groovy scripts for your
          integration flows
        </p>
      </div>

      {/* Search Bar and Upload Button */}
      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          marginBottom: "20px",
          gap: "20px", // Add gap between search and upload
        }}
      >
        <div
          style={{
            position: "relative",
            flex: "1", // Take remaining space
            maxWidth: "300px",
          }}
        >
          <FaSearch
            style={{
              position: "absolute",
              left: "12px",
              top: "50%",
              transform: "translateY(-50%)",
              color: "#9ca3af",
            }}
          />
          <input
            type="text"
            placeholder="Search"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: "100%",
              padding: "10px 16px 10px 40px",
              borderRadius: "8px",
              border: "1px solid #e5e7eb",
              fontSize: "14px",
              outline: "none",
              transition: "all 0.2s ease",
              boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
            }}
            onFocus={(e) => {
              e.target.style.borderColor = "#3b82f6";
              e.target.style.boxShadow = "0 0 0 3px rgba(59, 130, 246, 0.2)";
            }}
            onBlur={(e) => {
              e.target.style.borderColor = "#e5e7eb";
              e.target.style.boxShadow = "0 1px 2px rgba(0, 0, 0, 0.05)";
            }}
          />
        </div>

        {/* Updated Upload Button to match search styling */}
        <div
          style={{
            position: "relative",
            flex: "1",
            maxWidth: "150px",
          }}
        >
          <FaUpload
            style={{
              position: "absolute",
              left: "12px",
              top: "50%",
              transform: "translateY(-50%)",
              color: "#9ca3af",
            }}
          />
          <button
            onClick={openUploadDialog}
            style={{
              width: "100%",
              padding: "10px 16px 10px 40px",
              borderRadius: "8px",
              border: "1px solid #e5e7eb",
              fontSize: "14px",
              outline: "none",
              transition: "all 0.2s ease",
              boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
              backgroundColor: "white",
              color: "#4b5563",
              textAlign: "left",
              cursor: "pointer",
            }}
            onFocus={(e) => {
              e.currentTarget.style.borderColor = "#3b82f6";
              e.currentTarget.style.boxShadow =
                "0 0 0 3px rgba(59, 130, 246, 0.2)";
            }}
            onBlur={(e) => {
              e.currentTarget.style.borderColor = "#e5e7eb";
              e.currentTarget.style.boxShadow = "0 1px 2px rgba(0, 0, 0, 0.05)";
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.borderColor = "#d1d5db";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.borderColor = "#e5e7eb";
            }}
          >
            Upload Script
          </button>
        </div>
      </div>

      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fill, minmax(400px, 1fr))",
          gap: "20px",
        }}
      >
        {filteredData.map((item, index) => (
          <div
            key={index}
            style={{
              backgroundColor: "white",
              border: "1px solid #e5e7eb",
              borderRadius: "12px",
              padding: "24px",
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.08)",
              transition: "all 0.3s ease",
              cursor: "pointer",
              position: "relative",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.boxShadow =
                "0 4px 16px rgba(0, 0, 0, 0.12)";
              e.currentTarget.style.transform = "translateY(-2px)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.boxShadow = "0 2px 8px rgba(0, 0, 0, 0.08)";
              e.currentTarget.style.transform = "translateY(0)";
            }}
          >
            {/* Header with icon and tag */}
            <div
              style={{
                display: "flex",
                textAlign: "left",
                alignItems: "center",
                gap: "12px",
                marginBottom: "16px",
              }}
            >
              <div
                style={{
                  backgroundColor: "#dbeafe",
                  borderRadius: "8px",
                  padding: "8px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <FaCode size={20} color="#3b82f6" />
              </div>
              <h3
                style={{
                  display: "flex",
                  fontSize: "18px",
                  fontWeight: "600",
                  color: "#1f2937",
                  margin: "0",
                  flex: "1",
                }}
              >
                {item.scriptTag}
              </h3>
            </div>

            {/* Description */}
            <div style={{ marginBottom: "20px" }}>
              <p
                style={{
                  textAlign: "left",
                  fontSize: "14px",
                  color: "#6b7280",
                  lineHeight: "1.5",
                  margin: "0",
                }}
              >
                {item.scriptDescription}
              </p>
            </div>

            {/* Footer with filename and download button */}
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                paddingTop: "16px",
                borderTop: "1px solid #f3f4f6",
              }}
            >
              <div
                style={{
                  fontSize: "12px",
                  color: "#9ca3af",
                  fontFamily: "monospace",
                  backgroundColor: "#f9fafb",
                  padding: "4px 8px",
                  borderRadius: "4px",
                }}
              >
                {item.scriptFileName}
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleViewScript(item);
                }}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "6px",
                  backgroundColor: "#3b82f6",
                  color: "white",
                  border: "none",
                  borderRadius: "6px",
                  padding: "8px 12px",
                  fontSize: "14px",
                  fontWeight: "500",
                  cursor: "pointer",
                  transition: "background-color 0.2s ease",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "#2563eb";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "#3b82f6";
                }}
              >
                View
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredData.length === 0 && !loading && (
        <div
          style={{
            textAlign: "center",
            color: "#6b7280",
            fontSize: "16px",
            padding: "40px",
          }}
        >
          {searchTerm
            ? `No Groovy components found for "${searchTerm}"`
            : "No Groovy components available at the moment."}
        </div>
      )}

      {/* Upload Dialog */}
      {showUploadDialog && (
        <div
          style={{
            position: "fixed",
            top: "0",
            left: "0",
            right: "0",
            bottom: "0",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: "1000",
            padding: "20px",
          }}
        >
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "12px",
              width: "90%",
              maxWidth: "600px",
              maxHeight: "80vh",
              display: "flex",
              flexDirection: "column",
              boxShadow: "0 4px 24px rgba(0, 0, 0, 0.15)",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "16px 24px",
                borderBottom: "1px solid #e5e7eb",
              }}
            >
              <h3
                style={{
                  fontSize: "20px",
                  fontWeight: "600",
                  margin: "0",
                  color: "#1f2937",
                }}
              >
                Upload Groovy Script
              </h3>
              <button
                onClick={closeUploadDialog}
                style={{
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                  fontSize: "20px",
                  color: "#6b7280",
                  padding: "4px",
                }}
              >
                <FaTimes />
              </button>
            </div>

            <form onSubmit={handleUploadSubmit}>
              <div
                style={{
                  padding: "24px",
                  flex: "1",
                  overflowY: "auto",
                }}
              >
                {uploadError && (
                  <div
                    style={{
                      backgroundColor: "#fef2f2",
                      border: "1px solid #fecaca",
                      color: "#dc2626",
                      padding: "12px",
                      borderRadius: "8px",
                      marginBottom: "16px",
                      fontSize: "14px",
                    }}
                  >
                    {uploadError}
                  </div>
                )}

                <div style={{ marginBottom: "16px" }}>
                  <label
                    style={{
                      display: "flex",
                      fontSize: "14px",
                      fontWeight: "500",
                      color: "#374151",
                      marginBottom: "6px",
                    }}
                  >
                    Script Tag *
                  </label>
                  <input
                    type="text"
                    name="scriptTag"
                    value={uploadData.scriptTag}
                    onChange={handleInputChange}
                    placeholder="Enter script tag"
                    style={{
                      width: "100%",
                      padding: "10px 12px",
                      border: "1px solid #d1d5db",
                      borderRadius: "6px",
                      fontSize: "14px",
                      outline: "none",
                      transition: "border-color 0.2s ease",
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = "#3b82f6";
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = "#d1d5db";
                    }}
                  />
                </div>

                <div style={{ marginBottom: "16px" }}>
                  <label
                    style={{
                      display: "flex",
                      fontSize: "14px",
                      fontWeight: "500",
                      color: "#374151",
                      marginBottom: "6px",
                    }}
                  >
                    Script Use Cases *
                  </label>
                  <textarea
                    name="scriptUseCases"
                    value={uploadData.scriptUseCases}
                    onChange={handleInputChange}
                    placeholder="Enter script use cases"
                    rows="3"
                    style={{
                      width: "100%",
                      padding: "10px 12px",
                      border: "1px solid #d1d5db",
                      borderRadius: "6px",
                      fontSize: "14px",
                      outline: "none",
                      transition: "border-color 0.2s ease",
                      resize: "vertical",
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = "#3b82f6";
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = "#d1d5db";
                    }}
                  />
                </div>

                <div style={{ marginBottom: "16px" }}>
                  <label
                    style={{
                      display: "flex",
                      fontSize: "14px",
                      fontWeight: "500",
                      color: "#374151",
                      marginBottom: "6px",
                    }}
                  >
                    Script Description *
                  </label>
                  <textarea
                    name="scriptDescription"
                    value={uploadData.scriptDescription}
                    onChange={handleInputChange}
                    placeholder="Enter script description"
                    rows="4"
                    style={{
                      width: "100%",
                      padding: "10px 12px",
                      border: "1px solid #d1d5db",
                      borderRadius: "6px",
                      fontSize: "14px",
                      outline: "none",
                      transition: "border-color 0.2s ease",
                      resize: "vertical",
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = "#3b82f6";
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = "#d1d5db";
                    }}
                  />
                </div>

                <div style={{ marginBottom: "16px" }}>
                  <label
                    style={{
                      display: "flex",
                      fontSize: "14px",
                      fontWeight: "500",
                      color: "#080808ff",
                      marginBottom: "6px",
                    }}
                  >
                    Script File *
                  </label>
                  <input
                    type="file"
                    onChange={handleFileChange}
                    accept=".groovy"
                    style={{
                      width: "100%",
                      padding: "10px 12px",
                      border: "1px solid #d1d5db",
                      borderRadius: "6px",
                      fontSize: "14px",
                      outline: "none",
                      transition: "border-color 0.2s ease",
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = "#3b82f6";
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = "#d1d5db";
                    }}
                  />
                  {uploadData.scriptFile && (
                    <p
                      style={{
                        fontSize: "12px",
                        color: "#6b7280",
                        marginTop: "4px",
                        margin: "4px 0 0 0",
                      }}
                    >
                      Selected: {uploadData.scriptFile.name}
                    </p>
                  )}
                </div>
              </div>

              <div
                style={{
                  padding: "16px 24px",
                  borderTop: "1px solid #e5e7eb",
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: "12px",
                }}
              >
                <button
                  type="button"
                  onClick={closeUploadDialog}
                  style={{
                    padding: "10px 16px",
                    backgroundColor: "#f3f4f6",
                    color: "#374151",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontWeight: "500",
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={uploadLoading}
                  style={{
                    padding: "10px 16px",
                    backgroundColor: uploadLoading ? "#9ca3af" : "#3b82f6",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: uploadLoading ? "not-allowed" : "pointer",
                    fontSize: "14px",
                    fontWeight: "500",
                    display: "flex",
                    alignItems: "center",
                    gap: "6px",
                  }}
                >
                  {uploadLoading ? "Uploading..." : "Upload Script"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal Dialog for Script Content */}
      {selectedScript && (
        <div
          style={{
            marginTop: "8px",
            position: "fixed",
            top: "0",
            left: "0",
            right: "0",
            bottom: "0",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: "1000",
            padding: "20px",
          }}
        >
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "12px",
              width: "80%",
              maxWidth: "900px",
              maxHeight: "80vh",
              display: "flex",
              flexDirection: "column",
              boxShadow: "0 4px 24px rgba(0, 0, 0, 0.15)",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "16px 24px",
                borderBottom: "1px solid #e5e7eb",
              }}
            >
              <h3
                style={{
                  fontSize: "20px",
                  fontWeight: "600",
                  margin: "0",
                  color: "#1f2937",
                }}
              >
                {selectedScript.scriptTag}
              </h3>
              <button
                onClick={closeModal}
                style={{
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                  fontSize: "20px",
                  color: "#6b7280",
                  padding: "4px",
                }}
              >
                <FaTimes />
              </button>
            </div>

            <div
              style={{
                padding: "16px 24px",
                flex: "1",
                overflowY: "auto",
                position: "relative",
              }}
            >
              {contentLoading ? (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "200px",
                    color: "#666",
                  }}
                >
                  Loading script content...
                </div>
              ) : contentError ? (
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "200px",
                    color: "#dc2626",
                    gap: "10px",
                  }}
                >
                  <FaInfoCircle size={24} />
                  <div>Error loading script: {contentError}</div>
                  <button
                    onClick={() =>
                      fetchScriptContent(selectedScript.viewGroovyScript)
                    }
                    style={{
                      padding: "8px 16px",
                      backgroundColor: "#3b82f6",
                      color: "white",
                      border: "none",
                      borderRadius: "4px",
                      cursor: "pointer",
                    }}
                  >
                    Retry
                  </button>
                </div>
              ) : (
                <pre
                  style={{
                    textAlign: "left",
                    backgroundColor: "#f8fafc",
                    padding: "16px",
                    borderRadius: "8px",
                    overflowX: "auto",
                    fontFamily: "monospace",
                    fontSize: "14px",
                    lineHeight: "1.5",
                    color: "#334155",
                    margin: "0",
                    whiteSpace: "pre-wrap",
                  }}
                >
                  {scriptContent}
                </pre>
              )}
            </div>

            <div
              style={{
                padding: "16px 24px",
                borderTop: "1px solid #e5e7eb",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <div style={{ display: "flex", gap: "10px" }}>
                <button
                  onClick={copyModalContent}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: "#3b82f6",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontWeight: "500",
                    display: "flex",
                    alignItems: "center",
                    gap: "6px",
                  }}
                >
                  <FaDownload size={14} />
                  Copy
                </button>
                <button
                  onClick={downloadScript}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: "#10b981",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontWeight: "500",
                    display: "flex",
                    alignItems: "center",
                    gap: "6px",
                  }}
                >
                  <FaDownload size={14} />
                  Download
                </button>
                <button
                  onClick={openCpiUploadModal}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: "gray",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontWeight: "500",
                    display: "flex",
                    alignItems: "center",
                    gap: "6px",
                  }}
                >
                  <FaUpload size={14} />
                  Upload to CPI
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* CPI Upload Modal */}
      {showCpiUploadModal && (
        <div
          style={{
            position: "fixed",
            top: "0",
            left: "0",
            right: "0",
            bottom: "0",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: "1000",
            padding: "20px",
          }}
        >
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "12px",
              width: "90%",
              maxWidth: "500px",
              maxHeight: "80vh",
              display: "flex",
              flexDirection: "column",
              boxShadow: "0 4px 24px rgba(0, 0, 0, 0.15)",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "16px 24px",
                borderBottom: "1px solid #e5e7eb",
              }}
            >
              <h3
                style={{
                  fontSize: "20px",
                  fontWeight: "600",
                  margin: "0",
                  color: "#1f2937",
                }}
              >
                Upload to CPI
              </h3>
              <button
                onClick={closeCpiUploadModal}
                style={{
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                  fontSize: "20px",
                  color: "#6b7280",
                  padding: "4px",
                }}
              >
                <FaTimes />
              </button>
            </div>

            <form onSubmit={handleCpiUploadSubmit}>
              <div
                style={{
                  padding: "24px",
                  flex: "1",
                  overflowY: "auto",
                }}
              >
                {cpiUploadError && (
                  <div
                    style={{
                      backgroundColor: "#fef2f2",
                      border: "1px solid #fecaca",
                      color: "#dc2626",
                      padding: "12px",
                      borderRadius: "8px",
                      marginBottom: "16px",
                      fontSize: "14px",
                    }}
                  >
                    {cpiUploadError}
                  </div>
                )}

                <div style={{ marginBottom: "16px" }}>
                  <label
                    style={{
                      display: "flex",
                      fontSize: "14px",
                      fontWeight: "500",
                      color: "#374151",
                      marginBottom: "6px",
                    }}
                  >
                    Artifact ID *
                  </label>
                  <input
                    type="text"
                    name="artifactId"
                    value={cpiUploadData.artifactId}
                    onChange={handleCpiInputChange}
                    placeholder="Enter artifact ID"
                    style={{
                      width: "100%",
                      padding: "10px 12px",
                      border: "1px solid #d1d5db",
                      borderRadius: "6px",
                      fontSize: "14px",
                      outline: "none",
                      transition: "border-color 0.2s ease",
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = "#3b82f6";
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = "#d1d5db";
                    }}
                  />
                </div>

                <div style={{ marginBottom: "16px" }}>
                  <label
                    style={{
                      display: "flex",
                      fontSize: "14px",
                      fontWeight: "500",
                      color: "#374151",
                      marginBottom: "6px",
                    }}
                  >
                    Script Tag *
                  </label>
                  <input
                    type="text"
                    name="scriptTag"
                    value={cpiUploadData.scriptTag}
                    onChange={handleCpiInputChange}
                    style={{
                      width: "100%",
                      padding: "10px 12px",
                      border: "1px solid #d1d5db",
                      borderRadius: "6px",
                      fontSize: "14px",
                      outline: "none",
                      transition: "border-color 0.2s ease",
                      backgroundColor: "#f3f4f6",
                      cursor: "not-allowed",
                    }}
                    readOnly
                  />
                </div>
              </div>

              <div
                style={{
                  padding: "16px 24px",
                  borderTop: "1px solid #e5e7eb",
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: "12px",
                }}
              >
                <button
                  type="button"
                  onClick={closeCpiUploadModal}
                  style={{
                    padding: "10px 16px",
                    backgroundColor: "#f3f4f6",
                    color: "#374151",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontWeight: "500",
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={cpiUploadLoading}
                  style={{
                    padding: "10px 16px",
                    backgroundColor: cpiUploadLoading ? "#9ca3af" : "#3b82f6",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: cpiUploadLoading ? "not-allowed" : "pointer",
                    fontSize: "14px",
                    fontWeight: "500",
                    display: "flex",
                    alignItems: "center",
                    gap: "6px",
                  }}
                >
                  {cpiUploadLoading ? "Uploading..." : "Upload to CPI"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default GroovyComponent;
