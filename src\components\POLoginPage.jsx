

import axios from "axios";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>aS<PERSON>r, 
  <PERSON>a<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>lug, 
  FaExclamation<PERSON>ircle, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaS<PERSON>eldAlt,
  FaMagic
} from "react-icons/fa";
import APIEndpoint from "../config";
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';

const POLoginPage = () => {
  const [host, setHost] = useState("");
  const [port, setPort] = useState("");
  const [username, setUsername] = useState("");
  const [servernode,setServerNode]=useState("");
  const [messageid,setMessageId]=useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showDuplicateCredentialDialog, setShowDuplicateCredentialDialog] = useState(false);
  const navigate = useNavigate();

   const handleLogin = async () => {
  if (!host || !port || !username || !password) {
    setError("Please fill in all fields");
    return;
  }

  setIsLoading(true);
  setError("");

  try {
    const poCredentials = { 
      host, 
      port, 
      username, 
      password 
    };
    
    const [checkResponse1, checkResponse] = await Promise.all([
      axios.post(`${APIEndpoint}/api/setPoData`, poCredentials, {
        headers: {
          'Content-Type': 'application/json',
        }
      }),
      
      axios.get(`${APIEndpoint}/api/check-po-connection`, {
        params: {
          host,
          port,
          username,
          password,
          messageId: messageid,
          serverNode: servernode
        }
      })
    ]);

    if (checkResponse.data.status === "Connection failed with HTTP status code: 401") {
      setError("Invalid credentials");
      return;
    }

    try {
      const saveResponse = await axios.post(`${APIEndpoint}/api/po-credentials/save`, {
        poHost: host,
        poPort: port,
        poUsername: username,
        poPassword: password,
      }, {
        headers: {
          'Content-Type': 'application/json',
        }
      });

      // Show confirmation dialog if save was successful
      setShowConfirmation(true);
    } catch (saveError) {
      // Specifically check for duplicate credential error
      if (saveError.response?.data?.message === "Credential already exists for given host and port") {
        setShowDuplicateCredentialDialog(true);
      } else {
        throw saveError;
      }
    }
  } catch (err) {
    console.error("Login error:", err);
    setError(err.response?.data?.message || err.message || "Failed to connect to PO system");
  } finally {
    setIsLoading(false);
  }
};

  const handleConfirmation = (confirmed) => {
    setShowConfirmation(false);
    if (confirmed) {
      navigate("/dashboard/timestamp");
    }
  };

  const handleDuplicateCredentialResponse = (confirmed) => {
    setShowDuplicateCredentialDialog(false);
    if (confirmed) {
      navigate("/dashboard/timestamp");
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleLogin();
    }
  };

  const handleAutofill = () => {
    setHost("*************");
    setPort("50000");
    setUsername("INC02762");
    setPassword("1312%Sinha");
    setServerNode("9310150");
    setMessageId("8d3d64de-8d3d-11f0-8f67-0000008e0fc6");
    setError(""); // Clear any previous errors
  };

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <div style={styles.header}>
          <div style={styles.logo}>
            <FaShieldAlt size={36} color="#084a94" />
          </div>
          <h2 style={styles.title}>SAP PO 7.5 LOGIN PAGE</h2> 
          <p style={styles.subtitle}>Login to access SAP PO 7.5 system</p>  
        </div>
        
        {error && (
          <div style={styles.errorAlert}>
            <FaExclamationCircle style={styles.errorIcon} />
            <span>{error}</span>
          </div>
        )}
        
        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaServer style={styles.inputIcon} />
            <span>Server Host(whitelisted IP)</span>
          </label>
          <input
            type="text"
            value={host}
            onChange={(e) => setHost(e.target.value)}
            placeholder="server.example.com"
            style={styles.input}
            onKeyPress={handleKeyPress}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaPlug style={styles.inputIcon} />
            <span>Server Port</span>
          </label>
          <input
            type="text"
            value={port}
            onChange={(e) => setPort(e.target.value)}
            placeholder="8080"
            style={styles.input}
            onKeyPress={handleKeyPress}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaUser style={styles.inputIcon} />
            <span>Username</span>
          </label>
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Enter your username"
            style={styles.input}
            onKeyPress={handleKeyPress}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaLock style={styles.inputIcon} />
            <span>Password</span>
          </label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter your password"
            style={styles.input}
            onKeyPress={handleKeyPress}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaLock style={styles.inputIcon} />
            <span>Server Node</span>
          </label>
          <input
            type="text"
            value={servernode}
            onChange={(e) => setServerNode(e.target.value)}
            placeholder="Enter your server node"
            style={styles.input}
            onKeyPress={handleKeyPress}
          />
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>
            <FaLock style={styles.inputIcon} />
            <span>Message Id</span>
          </label>
          <input
            type="text"
            value={messageid}
            onChange={(e) => setMessageId(e.target.value)}
            placeholder="Enter your message id"
            style={styles.input}
            onKeyPress={handleKeyPress}
          />
        </div>

        <div style={styles.buttonGroup}>
          <button
            onClick={handleLogin}
            disabled={isLoading}
            style={isLoading ? styles.buttonLoading : styles.button}
          >
            {isLoading ? (
              <>
                <FaSpinner style={styles.spinner} />
                Connecting...
              </>
            ) : "Connect to PO 7.5 System"}
          </button>
          
          <button
            onClick={handleAutofill}
            style={styles.autofillButton}
            title="Fill with demo credentials"
          >
            <FaMagic style={{ marginRight: "8px" }} />
            Autofill
          </button>
        </div>
      </div>
      
      {/* Success confirmation dialog */}
      <Dialog
        open={showConfirmation}
        onClose={() => handleConfirmation(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title" style={{display:"flex", justifyContent:"center"}}>
          {"Configurations Successfully Completed"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            You've successfully connected to the PO 7.5 system. Would you like to begin testing now?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => handleConfirmation(false)} color="primary">
            No
          </Button>
          <Button onClick={() => handleConfirmation(true)} color="primary" autoFocus>
            Yes
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Duplicate credential dialog */}
      <Dialog
        open={showDuplicateCredentialDialog}
        onClose={() => handleDuplicateCredentialResponse(false)}
        aria-labelledby="duplicate-credential-dialog-title"
        aria-describedby="duplicate-credential-dialog-description"
      >
        <DialogTitle id="duplicate-credential-dialog-title" style={{display:"flex", justifyContent:"center"}}>
          {"Credential Already Exists"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="duplicate-credential-dialog-description">
            Credential already exists for given host and port. Press OK to continue or Cancel to abort.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => handleDuplicateCredentialResponse(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={() => handleDuplicateCredentialResponse(true)} color="primary" autoFocus>
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};
const styles = {
  container: {
    margintop:"20px",
    display: "flex",
    justifyContent: "center",
    fontFamily: "'Segoe UI', 'Roboto', 'Oxygen', sans-serif"
  },
  card: {
    
    width:"46%",
    backgroundColor: "white",
    borderRadius: "12px",
    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
    padding: "20px",
  },
  header: {
    textAlign: "center",
    marginBottom: "32px"
  },
  logo: {
    marginBottom: "16px",
    display: "flex",
    justifyContent: "center"
  },
  title: {
    fontSize: "24px",
    fontWeight: "600",
    color: "#1e293b",
    marginBottom: "8px"
  },
  subtitle: {
    fontSize: "14px",
    color: "#64748b",
    margin: 0
  },
  formGroup: {
    marginBottom: "20px",
    position: "relative"
  },
  label: {
    display: "flex",
    alignItems: "center",
    fontSize: "14px",
    fontWeight: "500",
    color: "#475569",
    marginBottom: "8px"
  },
  inputIcon: {
    width: "16px",
    height: "16px",
    marginRight: "8px",
    color: "#64748b"
  },
  input: {
    width: "100%",
    padding: "12px 16px 12px 40px",
    fontSize: "14px",
    border: "1px solid #e2e8f0",
    borderRadius: "8px",
    backgroundColor: "#f8fafc",
    transition: "all 0.2s",
    boxSizing: "border-box",
    outline: "none",
    color: "#1e293b"
  },
  buttonGroup: {
    display: "flex",
    gap: "12px",
    marginTop: "16px"
  },
  button: {
    flex: 1,
    padding: "14px 16px",
    fontSize: "15px",
    fontWeight: "500",
    backgroundColor: "#084a94",
    color: "white",
    border: "none",
    borderRadius: "8px",
    cursor: "pointer",
    transition: "all 0.2s",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    boxShadow: "0 2px 4px rgba(8, 74, 148, 0.2)"
  },
  buttonLoading: {
    flex: 1,
    padding: "14px 16px",
    fontSize: "15px",
    fontWeight: "500",
    backgroundColor: "#084a94",
    opacity: "0.8",
    color: "white",
    border: "none",
    borderRadius: "8px",
    cursor: "not-allowed",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },
  autofillButton: {
    flex: "0 0 auto",
    padding: "14px 16px",
    fontSize: "15px",
    fontWeight: "500",
    backgroundColor: "#f1f5f9",
    color: "#475569",
    border: "1px solid #e2e8f0",
    borderRadius: "8px",
    cursor: "pointer",
    transition: "all 0.2s",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)"
  },
  spinner: {
    animation: "spin 1s linear infinite",
    marginRight: "8px"
  },
  errorAlert: {
    display: "flex",
    alignItems: "center",
    padding: "12px 16px",
    backgroundColor: "#fee2e2",
    color: "#b91c1c",
    borderRadius: "8px",
    fontSize: "14px",
    marginBottom: "24px"
  },
  errorIcon: {
    width: "16px",
    height: "16px",
    marginRight: "8px",
    flexShrink: 0
  }
};

export default POLoginPage;












