import { AppBar, Too<PERSON><PERSON>, But<PERSON> } from "@mui/material";
import LogoutIcon from "@mui/icons-material/Logout";
import LogoOfficial from "../assets/LogoOfficial.png";
import logo from "../assets/logo.png";
// import logoutImg from "../assets/logout.png"; 
const Navbar = ({ onLogout }) => {
  return (
    <AppBar position="fixed" sx={{ backgroundColor: "white"}}>
      <Toolbar style={{ justifyContent: "space-between" }}>
        <img src={logo} alt="Company Logo" style={{ height: 30 }} />
        <div style={{ display: "flex", alignItems: "center", gap: "1rem" }}>
          <img src={LogoOfficial} alt="Small Logo" style={{ height: 30 }} />
          {/* <Button variant="contained" color="primary" onClick={onLogout}> */}
            {/* <LogoutIcon /> */}
          {/* </Button> */}
        </div>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
