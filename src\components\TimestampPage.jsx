import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import DateTime from "react-datetime";
import moment from "moment";
import "react-datetime/css/react-datetime.css";
import axios from "axios";
import APIEndpoint from "../config";
import "../App.css";

const TimestampPage = () => {
  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState({
    startTime: "",
    endTime: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (name, value) => {
    if (moment.isMoment(value) && value.isValid()) {
      setTimeRange((prev) => ({ ...prev, [name]: value.toISOString() }));
    } else if (typeof value === "string") {
      setTimeRange((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async () => {
    if (!timeRange.startTime || !timeRange.endTime) {
      alert("Please select both start and end times");
      return;
    }

    setIsLoading(true);

    try {
      
      // 1. DELETE request (no body, no params)
      // await axios.delete(`${APIEndpoint}/api/interface-keys/delete-all`, {
      //   headers:{
      //     'Content-Type': 'application/json',
      //   }
      // });

      // // 2. POST request with query parameters (exactly like original fetch)
      // const saveResponse = await axios.post(
      //   `${APIEndpoint}/api/interface-keys/extract-and-save?startTime=${encodeURIComponent(
      //     timeRange.startTime
      //   )}&endTime=${encodeURIComponent(timeRange.endTime)}`,
      //   null, // empty body
      //   {
      //     headers: {
      //     'Content-Type': 'application/json',
      //   }
      //   }
      // );

      // 3. GET request with query parameters (exactly like original fetch)
      const fetchResponse = await axios.get(
        `${APIEndpoint}/api/extractDetails?startTime=${encodeURIComponent(
          timeRange.startTime
        )}&endTime=${encodeURIComponent(timeRange.endTime)}`,
        {
          headers:{
          'Content-Type': 'application/json',
        }
        }
      );

      navigate("/dashboard/interface", { state: { interfaceData: fetchResponse.data } });
    } catch (error) {
      console.error("Operation failed:", error);
      alert(`Failed to complete operation: ${error.response?.data?.message || error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    navigate("/dashboard/po-login");
  };

  return (
    <div style={{
      padding: "20px",
      maxWidth: "900px",
      margin: "0 auto",
      minHeight: "calc(100vh - 200px)",
      display: "flex",
      flexDirection: "column",
      border: "1px solid #e5e7eb",
      borderRadius: "12px",
      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)",
      backgroundColor: "#fff",
      justifyContent: "space-between"
    }}>
      <div className="intermediate-page">
        <h3>Select Time Range</h3>
        <p style={{ fontSize: "1rem" }}>
          Please Specify the Time Range from SAP PO 7.5 Monitoring to fetch
          available service interfaces
        </p>

        <table className="time-range-table">
          <thead>
            <tr>
              <th>Time Range</th>
              <th>Enter details</th>
            </tr>
          </thead>
          <tbody>
            {["startTime", "endTime"].map((type) => (
              <tr key={type}>
                <td>{type === "startTime" ? "Start Time" : "End Time"}</td>
                <td>
                  <div className="custom-datetime">
                    <DateTime
                      value={timeRange[type] ? moment(timeRange[type]) : null}
                      onChange={(value) => handleChange(type, value)}
                      dateFormat="YYYY-MM-DD"
                      timeFormat="HH:mm:ss.SSS"
                      inputProps={{
                        placeholder: "YYYY-MM-DD HH:mm:ss.SSS",
                      }}
                      utc={true}
                    />
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        <div className="button-group">
          <button onClick={handleBack} className="back-button">
            ← Back to PO Login
          </button>
          <button
            onClick={handleSubmit}
            className="back-button"
            disabled={isLoading}
          >
            {isLoading ? "Processing..." : "Fetch Interfaces →"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TimestampPage;