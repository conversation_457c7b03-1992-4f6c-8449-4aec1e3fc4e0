import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import APIEndpoint from "../../config";
import {
  Card,
  Typography,
  Button,
  Box,
  Grid,
  Paper,
  LinearProgress,
  Alert,
  Snackbar,
} from "@mui/material";
import ActionButtons from "./ActionButtons";
import ConfigureSchedulerModal from "../ConfigureSchedulerModal";
import ConfigureIDOC from "../ConfigureIDOC";
import ConfigureHTTP from "../ConfigureHTTP";
import ConfigureSOAP from "../ConfigureSOAP";
import ConfigureAS2 from "../ConfigureAS2";

const CPINeoFormSection = ({ formData, setFormData }) => {
  const navigate = useNavigate();
  const [packages, setPackages] = useState([]);
  const [iFlows, setIFlows] = useState([]);
  const [loadingPackages, setLoadingPackages] = useState(false);
  const [loadingIFlows, setLoadingIFlows] = useState(false);
  const [isDeploying, setIsDeploying] = useState(false);
  
  // Modal states - Fixed: Added missing showAS2Modal state
  const [showHTTPModal, setShowHTTPModal] = useState(false);
  const [showSOAPModal, setShowSOAPModal] = useState(false);
  const [showIDOCModal, setShowIDOCModal] = useState(false);
  const [showAS2Modal, setShowAS2Modal] = useState(false); // This was missing!
  const [showConfigureModal, setShowConfigureModal] = useState(false);
  
  // Message states
  const [showMessage, setShowMessage] = useState(false);
  const [deployMessage, setDeployMessage] = useState(null);

  const isConfigureEnabled =
    formData.adapterName &&
    (formData.adapterName === "SFTP" ||
      formData.adapterName === "IDOC" ||
      formData.adapterName === "HTTPS" ||
      formData.adapterName === "SOAP" ||
      formData.adapterName === "AS2"); // Added AS2 to the list

  const handleDeploy = async () => {
    if (!formData.iFlowName || !formData.tenant) {
      setDeployMessage({
        text: "Please select both I-Flow Name and Tenant before deploying",
        severity: "error",
      });
      setShowMessage(true);
      return;
    }

    setIsDeploying(true);
    setDeployMessage(null);

    try {
      const apiTenantName =
        formData.tenant.charAt(0).toUpperCase() +
        formData.tenant.slice(1).toLowerCase();
      const response = await fetch(
        `${APIEndpoint}/api/deploy?artifactId=${encodeURIComponent(
          formData.iFlowName
        )}&configName=${encodeURIComponent(apiTenantName)}`,
        {
          method: "POST",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to initiate deployment");
      }

      const data = await response.json();
      setDeployMessage({
        text: "Deployment started successfully! Your artifact is being processed.",
        severity: "success",
      });
      setShowMessage(true);
    } catch (error) {
      console.error("Deployment error:", error);
      setDeployMessage({
        text: "Deployment failed. Please try again.",
        severity: "error",
      });
      setShowMessage(true);
    } finally {
      setIsDeploying(false);
    }
  };

  // Fixed: Added debugging and better error handling
  const onConfigure = () => {
    console.log("Configure clicked with adapter:", formData.adapterName);
    console.log("iFlowName:", formData.iFlowName);
    
    if (!formData.iFlowName) {
      setDeployMessage({
        text: "Please select an I-Flow Name before configuring",
        severity: "error",
      });
      setShowMessage(true);
      return;
    }

    // Fixed: Added proper modal state management
    switch (formData.adapterName) {
      case "SFTP":
        console.log("Opening SFTP modal");
        setShowConfigureModal(true);
        break;
      case "IDOC":
        console.log("Opening IDOC modal");
        setShowIDOCModal(true);
        break;
      case "HTTPS":
        console.log("Opening HTTPS modal");
        setShowHTTPModal(true);
        break;
      case "SOAP":
        console.log("Opening SOAP modal");
        setShowSOAPModal(true);
        break;
      case "AS2":
        console.log("Opening AS2 modal");
        setShowAS2Modal(true);
        break;
      default:
        console.log("No modal configured for adapter:", formData.adapterName);
        break;
    }
  };

  // Search functionality states
  const [packageSearchTerm, setPackageSearchTerm] = useState("");
  const [showPackageDropdown, setShowPackageDropdown] = useState(false);
  const [filteredPackages, setFilteredPackages] = useState([]);
  const packageDropdownRef = useRef(null);

  // Function to convert tenant value for API (DEV -> Dev, QA -> Qa)
  const getApiTenantName = (tenant) => {
    if (!tenant) return tenant;
    return tenant.charAt(0).toUpperCase() + tenant.slice(1).toLowerCase();
  };

  // Filter packages based on search term
  useEffect(() => {
    if (packageSearchTerm) {
      const filtered = packages.filter((pkg) =>
        pkg.name.toLowerCase().includes(packageSearchTerm.toLowerCase())
      );
      setFilteredPackages(filtered);
    } else {
      setFilteredPackages(packages);
    }
  }, [packageSearchTerm, packages]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        packageDropdownRef.current &&
        !packageDropdownRef.current.contains(event.target)
      ) {
        setShowPackageDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const fetchPackages = async () => {
      if (!formData.tenant) return;

      setLoadingPackages(true);
      try {
        const apiTenantName = getApiTenantName(formData.tenant);
        const response = await fetch(
          `${APIEndpoint}/packages?configName=${apiTenantName}`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch packages");
        }

        const data = await response.json();
        console.log("Raw API response:", data);

        // Extract package data from the response structure
        const packageKeys = Object.keys(data || {});

        // Create packages array with packageId for both display and value
        const packagesArray = packageKeys.map((key) => ({
          id: key,
          packageId: data[key].packageId, // Use packageId for both display and API calls
          name: data[key].packageId, // Display packageId in dropdown
        }));

        console.log("Processed packages:", packagesArray);
        setPackages(packagesArray);

        // Reset package and IFlow selections when tenant changes
        setFormData((prev) => ({
          ...prev,
          packageName: "", // This will store the packageId
          iFlowName: "",
        }));
        setIFlows([]);
        setPackageSearchTerm("");
      } catch (error) {
        console.error("Error fetching packages:", error);
      } finally {
        setLoadingPackages(false);
      }
    };

    fetchPackages();
  }, [formData.tenant, setFormData]);

  // Update the IFlow fetch to use the correct packageId
  useEffect(() => {
    const fetchIFlows = async () => {
      if (!formData.packageName || !formData.tenant) return;

      setLoadingIFlows(true);
      try {
        const apiTenantName = getApiTenantName(formData.tenant);
        // Use packageName which now contains the packageId
        const response = await fetch(
          `${APIEndpoint}/api/integration/artifacts?packageId=${formData.packageName}&configName=${apiTenantName}`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch IFlows");
        }

        const data = await response.json();
        console.log("IFlow API response:", data);

        // Extract IFlow names from the response structure
        const iFlowData = data.body || {};
        const iFlowNames = Object.keys(iFlowData).map((key) => ({
          id: key,
          name: iFlowData[key].artifactName,
        }));
        setIFlows(iFlowNames);

        // Reset IFlow selection when package changes
        setFormData((prev) => ({
          ...prev,
          iFlowName: "",
        }));
      } catch (error) {
        console.error("Error fetching IFlows:", error);
      } finally {
        setLoadingIFlows(false);
      }
    };

    fetchIFlows();
  }, [formData.packageName, formData.tenant, setFormData]);

  const handlePackageSelect = (packageName) => {
    setFormData({
      ...formData,
      packageName: packageName,
    });
    setPackageSearchTerm(packageName);
    setShowPackageDropdown(false);
  };

  const handlePackageInputChange = (e) => {
    const value = e.target.value;
    setPackageSearchTerm(value);
    setShowPackageDropdown(true);

    // If the input is cleared, also clear the selected package
    if (!value) {
      setFormData({
        ...formData,
        packageName: "",
        iFlowName: "",
      });
    }
  };

  // Fixed: Added function to close message
  const handleCloseMessage = () => {
    setShowMessage(false);
  };

  console.log("formData", formData);

  return (
    <div className="testcase-section">
      <h5>Please mention the required details from SAP Integration suite</h5>
      <table>
        <thead>
          <tr style={{ backgroundColor: "#272D4F", color: "white" }}>
            <th
              style={{
                backgroundColor: "#272D4F",
                padding: "10px",
                border: "1px solid #ccc",
                textAlign: "left",
              }}
            >
              SAP Integration suite
            </th>
            <th
              style={{
                backgroundColor: "#272D4F",
                padding: "10px",
                border: "1px solid #ccc",
                textAlign: "left",
              }}
            >
              Enter details
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Tenant</td>
            <td>
              <select
                value={formData.tenant}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    tenant: e.target.value,
                  })
                }
                required
              >
                <option value="">Select Tenant</option>
                <option value="Dev">DEV</option>
                <option value="QA">QA</option>
              </select>
            </td>
          </tr>
          <tr>
            <td>Package Name</td>
            <td>
              <div style={{ position: "relative" }} ref={packageDropdownRef}>
                <input
                  type="text"
                  value={packageSearchTerm}
                  onChange={handlePackageInputChange}
                  onFocus={() => setShowPackageDropdown(true)}
                  placeholder={
                    loadingPackages
                      ? "Loading packages..."
                      : "Search and select package"
                  }
                  disabled={loadingPackages || !formData.tenant}
                  required
                  style={{
                    width: "100%",
                    padding: "8px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                    fontSize: "14px",
                  }}
                />
                {showPackageDropdown &&
                  !loadingPackages &&
                  filteredPackages.length > 0 && (
                    <div
                      style={{
                        position: "absolute",
                        top: "100%",
                        left: 0,
                        right: 0,
                        backgroundColor: "white",
                        border: "1px solid #ccc",
                        borderTop: "none",
                        maxHeight: "200px",
                        overflowY: "auto",
                        zIndex: 1000,
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                      }}
                    >
                      {filteredPackages.map((pkg) => (
                        <div
                          key={pkg.id}
                          onClick={() => handlePackageSelect(pkg.name)}
                          style={{
                            padding: "8px 12px",
                            cursor: "pointer",
                            borderBottom: "1px solid #eee",
                            backgroundColor: "white",
                            fontSize: "14px",
                          }}
                          onMouseEnter={(e) =>
                            (e.target.style.backgroundColor = "#f5f5f5")
                          }
                          onMouseLeave={(e) =>
                            (e.target.style.backgroundColor = "white")
                          }
                        >
                          {pkg.name}
                        </div>
                      ))}
                    </div>
                  )}
                {showPackageDropdown &&
                  !loadingPackages &&
                  packageSearchTerm &&
                  filteredPackages.length === 0 && (
                    <div
                      style={{
                        position: "absolute",
                        top: "100%",
                        left: 0,
                        right: 0,
                        backgroundColor: "white",
                        border: "1px solid #ccc",
                        borderTop: "none",
                        padding: "8px 12px",
                        color: "#666",
                        fontSize: "14px",
                        zIndex: 1000,
                      }}
                    >
                      No packages found
                    </div>
                  )}
              </div>
            </td>
          </tr>
          <tr>
            <td>I-Flow Name</td>
            <td>
              <select
                value={formData.iFlowName}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    iFlowName: e.target.value,
                  })
                }
                required
                disabled={loadingIFlows || !formData.packageName}
              >
                <option value="">
                  {loadingIFlows ? "Loading IFlows..." : "Select IFlow"}
                </option>
                {iFlows.map((flow) => (
                  <option key={flow.id} value={flow.name}>
                    {flow.name}
                  </option>
                ))}
              </select>
            </td>
          </tr>
          <tr>
            <td>Sender Adapter</td>
            <td>
              <select
                value={formData.adapterName}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    adapterName: e.target.value,
                  })
                }
                required
              >
                <option value="">Select Adapter</option>
                <option value="AS2">AS2</option>
                <option value="HTTPS">HTTPS</option>
                <option value="IDOC">IDOC</option>
                <option value="SFTP">SFTP</option>
                <option value="SOAP">SOAP</option>
                <option value="JMS">JMS</option>
                <option value="Mail">MAIL</option>
                <option value="PROCESS_DIRECT">PROCESS DIRECT</option>
              </select>
            </td>
          </tr>
          <tr>
            <td colSpan="2">
              <label>
                If you are not a registered user please register here{" "}
                <a
                  href="#register"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate("/dashboard/register");
                  }}
                  style={{ color: "blue", cursor: "pointer" }}
                >
                  Register
                </a>
              </label>
            </td>
          </tr>
        </tbody>
      </table>
      
      <Box sx={{ display: "flex", gap: 2 }}>
        <ActionButtons
          formData={formData}
          onConfigure={onConfigure}
          onDeploy={handleDeploy}
          isConfigureEnabled={isConfigureEnabled}
          isDeploying={isDeploying}
        />
      </Box>

      {/* Fixed: Added proper conditional rendering for all modals */}
      {formData.adapterName === "SFTP" && (
        <ConfigureSchedulerModal
          isOpen={showConfigureModal}
          onClose={() => setShowConfigureModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "IDOC" && (
        <ConfigureIDOC
          isOpen={showIDOCModal}
          onClose={() => setShowIDOCModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "HTTPS" && (
        <ConfigureHTTP
          isOpen={showHTTPModal}
          onClose={() => setShowHTTPModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "SOAP" && (
        <ConfigureSOAP
          isOpen={showSOAPModal}
          onClose={() => setShowSOAPModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "AS2" && (
        <ConfigureAS2
          isOpen={showAS2Modal}
          onClose={() => setShowAS2Modal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {/* Fixed: Added missing Snackbar component */}
      <Snackbar
        open={showMessage}
        autoHideDuration={6000}
        onClose={handleCloseMessage}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseMessage}
          severity={deployMessage?.severity || 'info'}
          sx={{ width: '100%' }}
        >
          {deployMessage?.text}
        </Alert>
      </Snackbar>

      <style jsx>{`
        .spinner {
          animation: rotate 2s linear infinite;
        }
        .spinner .path {
          stroke: #374151;
          stroke-linecap: round;
          animation: dash 1.5s ease-in-out infinite;
        }
        @keyframes rotate {
          100% {
            transform: rotate(360deg);
          }
        }
        @keyframes dash {
          0% {
            stroke-dasharray: 1, 150;
            stroke-dashoffset: 0;
          }
          50% {
            stroke-dasharray: 90, 150;
            stroke-dashoffset: -35;
          }
          100% {
            stroke-dasharray: 90, 150;
            stroke-dashoffset: -124;
          }
        }
      `}</style>
    </div>
  );
};

export default CPINeoFormSection;