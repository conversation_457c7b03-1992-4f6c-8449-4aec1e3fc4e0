import React, { useState } from "react";
import "./ComparePayloads.css";

const MultiplePayloads = ({ selection, onBack, onReset }) => {
  const [inputFiles, setInputFiles] = useState([]);
  const [sapPOFiles, setSapPOFiles] = useState([]);
  const [sapCPIFiles, setSapCPIFiles] = useState([]);

  const handleFileChange = (e, setter) => {
    const files = Array.from(e.target.files);
    const validFiles = files.filter(file => file.type === "application/zip");
    
    if (validFiles.length !== files.length) {
      alert("Please upload only .zip files.");
    }
    
    if (validFiles.length > 0) {
      setter(validFiles);
    }
  };

  const handleRun = async () => {
    if (sapPOFiles.length === 0 || sapCPIFiles.length === 0) {
      alert("Please upload both SAP PO and SAP IS files before running.");
      return;
    }

    const formData = new FormData();
    inputFiles.forEach(file => formData.append("inputFiles", file));
    sapPOFiles.forEach(file => formData.append("sapPOFiles", file));
    sapCPIFiles.forEach(file => formData.append("sapCPIFiles", file));

    try {
      const response = await fetch("/api/run-batch-tests", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Batch test execution started for ${result.count} payloads`);
      } else {
        alert("Error starting batch tests.");
      }
    } catch (error) {
      console.error("Run Error:", error);
      alert("Something went wrong!");
    }
  };

  return (
    <>
      <h3 className="upload-heading">Multiple Payloads Test - {selection}</h3>
      
      <div className="navigation-buttons">
        <button className="back-button" onClick={onBack}>
          Back to Test Type
        </button>
        <button className="reset-button" onClick={onReset}>
          Start Over
        </button>
      </div>

      <table className="upload-table">
        <thead>
          <tr>
            <th>Upload Input Payloads (Multiple Zips)</th>
            <th>Upload SAP PO Payloads (Multiple Zips)</th>
            <th>Upload SAP IS Payloads (Multiple Zips)</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              <label className="upload-button">
                Upload Multiple ZIPs
                <input
                  type="file"
                  accept=".zip"
                  multiple
                  hidden
                  onChange={(e) => handleFileChange(e, setInputFiles)}
                />
              </label>
              <div className="file-names">
                {inputFiles.map((file, index) => (
                  <div key={index}>{file.name}</div>
                ))}
              </div>
            </td>
            <td>
              <label className="upload-button">
                Upload Multiple ZIPs
                <input
                  type="file"
                  accept=".zip"
                  multiple
                  hidden
                  onChange={(e) => handleFileChange(e, setSapPOFiles)}
                />
              </label>
              <div className="file-names">
                {sapPOFiles.map((file, index) => (
                  <div key={index}>{file.name}</div>
                ))}
              </div>
            </td>
            <td>
              <label className="upload-button">
                Upload Multiple ZIPs
                <input
                  type="file"
                  accept=".zip"
                  multiple
                  hidden
                  onChange={(e) => handleFileChange(e, setSapCPIFiles)}
                />
              </label>
              <div className="file-names">
                {sapCPIFiles.map((file, index) => (
                  <div key={index}>{file.name}</div>
                ))}
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <div className="run-section">
        <button className="run-button" onClick={handleRun}>
          Run Batch Tests ({Math.min(
            inputFiles.length || Infinity,
            sapPOFiles.length || Infinity,
            sapCPIFiles.length || Infinity
          )} payloads)
        </button>
      </div>

      {/* <footer className="footer">
        <p>
          <a href="#">Welcome to IntSwitch Testing Tool and copyright Information</a>
        </p>
        <p>
          <a href="#" className="intswitch">IntSwitch</a> Testing tool for testing data migrated from Non-SAP and SAP based on Premise / On cloud middleware to SAP Integration Suite's Cloud Integration Capability
        </p>
      </footer> */}
    </>
  );
};

export default MultiplePayloads;