import React from "react";
import CircularProgress from "@mui/material/CircularProgress";

const PayloadModal = ({ 
  isOpen, 
  payloadType, 
  payloadContent, 
  isLoading, 
  onClose 
}) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content" style={{ maxWidth: "800px" }}>
        <h3>{payloadType} Payload Content</h3>
        <button className="close-modal" onClick={onClose}>
          ×
        </button>

        <div className="payload-content-container">
          {isLoading ? (
            <div className="loading-message">
              <CircularProgress />
              <p>Loading payload content...</p>
            </div>
          ) : (
            <pre className="payload-content">{payloadContent}</pre>
          )}
        </div>
      </div>
    </div>
  );
};

export default PayloadModal;