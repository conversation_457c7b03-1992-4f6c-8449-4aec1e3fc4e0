import React, { useState, useEffect } from 'react';

const Scheduler4 = ({ onCronExpressionUpdate }) => {
  const [recurrenceType, setRecurrenceType] = useState('daily'); // For 'recur'
  const [selectedWeekdays, setSelectedWeekdays] = useState([]);
  const [startTimeInternal, setStartTimeInternal] = useState('12:34'); // Default time
  const [cronExpression, setCronExpression] = useState('');
 
  const handleTimeChange = (event) => {
    setStartTimeInternal(event.target.value);
  };

  const handleRecurrenceTypeChange = (e) => {
    setRecurrenceType(e.target.value);
    if (e.target.value === 'daily') {
      setSelectedWeekdays([]);
    } else {
      setSelectedWeekdays(['Monday']); // Default to Monday for weekly
    }
  };

  const handleWeekdayChange = (e) => {
    const { value, checked } = e.target;
    if (checked) {
      setSelectedWeekdays([...selectedWeekdays, value]);
    } else {
      setSelectedWeekdays(selectedWeekdays.filter((day) => day !== value));
    }
  };

  const generateCronExpression = () => {
    const [hour, minute] = startTimeInternal.split(':').map((str) => parseInt(str, 10));

    if (recurrenceType === 'daily') {
      // Daily recurrence format: 0 MINUTE HOUR ? * * * --tz=IST
      return `0 ${minute} ${hour} ? * * * --tz=IST`;
    } else if (recurrenceType === 'weekly') {
      // Weekly recurrence format: 0 MINUTE HOUR ? * DAY1,DAY2,... * --tz=IST
      const daysOfWeek = selectedWeekdays.map((day) => day.slice(0, 3).toUpperCase()).join(',');
      return `0 ${minute} ${hour} ? * ${daysOfWeek} * --tz=IST`;
    }

    return ''; // Default fallback
  };

  useEffect(() => {
    const generatedCronExpression = generateCronExpression();
    if (generatedCronExpression !== cronExpression) { // Only update if there's a change
      setCronExpression(generatedCronExpression);
    }
  }, [startTimeInternal, recurrenceType, selectedWeekdays]); 
   // Update `cronExpression` based on states
  
   useEffect(() => {
    if (cronExpression) {
      onCronExpressionUpdate(cronExpression);
    }
  }, [cronExpression]); // Reduced dependencies to only the critical one
   // Only runs when `cronExpression` is updated
  

  return (
    <div className="container">
      <div className="form-group">
        <label htmlFor="recurrenceType">Schedule to Recur</label>
        <select
          id="recurrenceType"
          value={recurrenceType}
          onChange={handleRecurrenceTypeChange}
          className="form-control"
        >
          <option value="daily">Daily</option>
          <option value="weekly">Weekly</option>
        </select>
      </div>

      {recurrenceType === 'weekly' && (
        <div className="form-group">
          <label>Choose Weekdays</label>
          {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(
            (day) => (
              <div className="form-check" key={day}>
                <input
                  type="checkbox"
                  id={`weekday-${day}`}
                  value={day}
                  checked={selectedWeekdays.includes(day)}
                  onChange={handleWeekdayChange}
                />
                <label htmlFor={`weekday-${day}`}>{day}</label>
              </div>
            ),
          )}
        </div>
      )}

      <div className='container mx-5'>
        <label htmlFor="startTimeInput">Select Time:</label>
        <input
          type="time"
          value={startTimeInternal}
          onChange={handleTimeChange}
          id="startTimeInput"
          className="form-control"
        />
      </div>

      {/* <div>
        <strong>Cron Expression:</strong> {cronExpression}
      </div> */}
    </div>
  );
};

export default Scheduler4;
