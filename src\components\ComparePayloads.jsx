import React, { useState } from "react";
import "./ComparePayloads.css";
import APIEndpoint from "../config";
import ComparisonDataForCompare from "./ComparisonDataForCompare";

const ComparePayloads = () => {
  const [selection, setSelection] = useState("");
  const [inputFile, setInputFile] = useState(null);
  const [sapPOFile, setSapPOFile] = useState(null);
  const [sapCPIFile, setSapCPIFile] = useState(null);
  const [inputXMLFile, setInputXMLFile] = useState(null);
  const [sapPOXMLFile, setSapPOXMLFile] = useState(null);
  const [sapCPIXMLFile, setSapCPIXMLFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [comparisonResult, setComparisonResult] = useState(null);
  const [viewResults, setViewResults] = useState(false);

  const handleFileChange = (e, setter) => {
    const file = e.target.files[0];
    if (file) {
      const isZip = file.name.endsWith(".zip");
      const isXml = file.name.endsWith(".xml");

      if (
        (selection === "single" && isXml) ||
        (selection === "multiple" && isZip)
      ) {
        setter(file);
      } else {
        alert(
          `Please upload a valid ${
            selection === "single" ? ".xml" : ".zip"
          } file.`
        );
        e.target.value = "";
      }
    }
  };

  const handleRun = async () => {
    setIsLoading(true);
    const formData = new FormData();

    if (selection === "single") {
      if (!inputXMLFile || !sapPOXMLFile || !sapCPIXMLFile) {
        alert("Please upload all XML files before running.");
        setIsLoading(false);
        return;
      }
      formData.append("input", inputXMLFile);
      formData.append("pooutput", sapPOXMLFile);
      formData.append("cpioutput", sapCPIXMLFile);
    } 
    else {
      if (inputFile) 
      formData.append("inputZip", inputFile);
      formData.append("poOutputZip", sapPOFile);
      formData.append("cpiOutputZip", sapCPIFile);
    }

    try {
      const endpoint =
        selection === "single"
          ? `${APIEndpoint}/api/xml/unitcompare`
          : `${APIEndpoint}/api/Comparezip`;

      const response = await fetch(endpoint, {
        method: "POST",
        body: formData,
        headers: {
          "Authorization": `Basic ${localStorage.getItem("basicAuth")}`
        },
      });

      if (response.ok) {
        const result = await response.json();
        setComparisonResult(result);
        setViewResults(true);
      } else {
        const error = await response.text();
        alert(`Upload failed: ${error}`);
      }
    } catch (error) {
      console.error("Upload Error:", error);
      alert("Upload failed. Please check console for details.");
    } finally {
      setIsLoading(false);
    }
  };

  const FileUploadCard = ({ 
    title, 
    file, 
    onFileChange, 
    accept, 
    fileType,
    icon 
  }) => (
    <div className="file-upload-card">
      <div className="file-upload-header">
        <div className="file-upload-icon">
          {icon}
        </div>
        <h4 className="file-upload-title">{title}</h4>
      </div>
      
      <div className="file-upload-content">
        <label className="file-upload-area">
          <input
            type="file"
            accept={accept}
            hidden
            onChange={onFileChange}
          />
          <div className="file-upload-placeholder">
            <svg className="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            <span className="upload-text">
              {file ? "Change File" : `Upload ${fileType}`}
            </span>
            <span className="upload-subtext">
              Drag and drop or click to browse
            </span>
          </div>
        </label>
        
        {file && (
          <div className="file-selected">
            <div className="file-info">
              <svg className="file-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span className="file-name">{file.name}</span>
            </div>
            <div className="file-size">
              {(file.size / 1024).toFixed(1)} KB
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // Integrated scenario selector
  const ScenarioSelector = () => {
    const getButtonStyle = (buttonType) => {
      const isActive = selection === buttonType;

      return {
        backgroundColor: isActive ? "#e5e7eb" : "white",
        color: "#374151",
        borderRadius: "8px",
        width: "200px",
        fontWeight: "500",
        fontSize: "14px",
        border: "1px solid #e5e7eb",
        padding: "12px 16px",
        cursor: "pointer",
        transition: "all 0.3s ease",
        boxShadow: isActive
          ? "0 2px 4px rgba(0, 0, 0, 0.1)"
          : "0 1px 3px rgba(0, 0, 0, 0.1)",
        textAlign: "center",
        marginRight: "10px",
        marginBottom: "10px",
      };
    };

    const containerStyle = {
      padding: "20px",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "20px",
    };

    const titleStyle = {
      color: "#1f2937",
      fontSize: "18px",
      fontWeight: "600",
      margin: "0",
      textAlign: "center",
    };

    const buttonGroupStyle = {
      display: "flex",
      flexWrap: "wrap",
      justifyContent: "center",
      gap: "10px",
    };

    return (
      <div style={containerStyle}>
        {!selection && (
          <h4 style={titleStyle}>Please select the testing scenario</h4>
        )}
        <div style={buttonGroupStyle}>
          <button
            onClick={() => setSelection("single")}
            style={getButtonStyle("single")}
            onMouseEnter={(e) => {
              if (selection !== "single") {
                e.target.style.backgroundColor = "#f9fafb";
              }
            }}
            onMouseLeave={(e) => {
              if (selection !== "single") {
                e.target.style.backgroundColor = "white";
              }
            }}
          >
            Single Test Case
          </button>
          <button
            onClick={() => setSelection("multiple")}
            style={getButtonStyle("multiple")}
            onMouseEnter={(e) => {
              if (selection !== "multiple") {
                e.target.style.backgroundColor = "#f9fafb";
              }
            }}
            onMouseLeave={(e) => {
              if (selection !== "multiple") {
                e.target.style.backgroundColor = "white";
              }
            }}
          >
            Multiple Test Cases
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="compare-payloads-container">
      {!viewResults ? (
        <div className="compare-payloads-content">
          <ScenarioSelector />

          {selection && (
            <div className="upload-section">
              <div className="section-header">
                <h2 className="section-title">Upload Test Files</h2>
                <p className="section-description">
                  Upload your {selection === "single" ? "XML" : "ZIP"} files to begin the comparison process
                </p>
              </div>

              <div className="upload-grid">
                {selection === "single" ? (
                  <>
                    <FileUploadCard
                      title="Input Payload"
                      file={inputXMLFile}
                      onFileChange={(e) => handleFileChange(e, setInputXMLFile)}
                      accept=".xml,text/xml,application/xml"
                      fileType="XML"
                      icon={
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      }
                    />
                    <FileUploadCard
                      title="SAP PO Output"
                      file={sapPOXMLFile}
                      onFileChange={(e) => handleFileChange(e, setSapPOXMLFile)}
                      accept=".xml,text/xml,application/xml"
                      fileType="XML"
                      icon={
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>
                      }
                    />
                    <FileUploadCard
                      title="SAP CPI Output"
                      file={sapCPIXMLFile}
                      onFileChange={(e) => handleFileChange(e, setSapCPIXMLFile)}
                      accept=".xml,text/xml,application/xml"
                      fileType="XML"
                      icon={
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                      }
                    />
                  </>
                ) : (
                  <>
                    <FileUploadCard
                      title="Input Payload"
                      file={inputFile}
                      onFileChange={(e) => handleFileChange(e, setInputFile)}
                      accept=".zip,application/zip,application/x-zip-compressed"
                      fileType="ZIP"
                      icon={
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      }
                    />
                    <FileUploadCard
                      title="SAP PO Output"
                      file={sapPOFile}
                      onFileChange={(e) => handleFileChange(e, setSapPOFile)}
                      accept=".zip,application/zip,application/x-zip-compressed"
                      fileType="ZIP"
                      icon={
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>
                      }
                    />
                    <FileUploadCard
                      title="SAP CPI Output"
                      file={sapCPIFile}
                      onFileChange={(e) => handleFileChange(e, setSapCPIFile)}
                      accept=".zip,application/zip,application/x-zip-compressed"
                      fileType="ZIP"
                      icon={
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                      }
                    />
                  </>
                )}
              </div>

              <div className="action-section">
                <button
                  className="run-button"
                  onClick={handleRun}
                  disabled={isLoading}
                  style={{
                    padding: "12px 24px",
                    fontWeight: "bold",
                    width: "50%",
                    alignSelf: "center",
                    backgroundColor: "#272D4F",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: isLoading ? "not-allowed" : "pointer",
                    opacity: isLoading ? 0.7 : 1
                  }}
                >
                  {isLoading ? (
                    <div className="button-loading">
                      <div className="spinner"></div>
                      Processing...
                    </div>
                  ) : (
                    <div className="button-content">
                      <svg className="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      Run Comparison
                    </div>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      ) : (
        <ComparisonDataForCompare
          comparisonResult={comparisonResult}
          onBack={() => {
            setViewResults(false);
            setComparisonResult(null);
          }}
          isSingleTestCase={selection === "single"}
        />
      )}
    </div>
  );
};

export default ComparePayloads;