import React, { useState } from 'react';
import { MenuItem, Select, FormControl } from '@mui/material';
import API_ENDPOINT from '../../config';

const MDNTab = ({ onClose, setSnackbar, artifactId }) => {
  const [formData, setFormData] = useState({
    privateKeyAlias: { param: '', value: '' },
    signatureEncoding: { param: '', value: 'binary' },
    propagateMDNDetails: { param: '', value: 'Not Required' },
    negativeMDNProcessing: { param: '', value: 'Not Required' },
    proxyType: { param: '', value: 'Internet' },
    authentication: { param: '', value: 'None' },
    credentialName: { param: '', value: '' },
    privateKeyAliasAuth: { param: '', value: '' },
    timeout: { param: '', value: '300000' }
  });
  const [loading, setLoading] = useState(false);

  
  const handleCancel = () => {
    if (onClose) onClose();
  };
  
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: { ...prev[field], value }
    }));
  };

  const handleParamChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: { ...prev[field], param: value }
    }));
  };

  const renderAuthenticationFields = () => {
    switch (formData.authentication.value) {
      case 'Basic Authentication':
        return (
          <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center', marginBottom: '15px' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>
              Credential Name: <span style={{ color: 'red' }}>*</span>
            </label>
            <input
              type="text"
              placeholder="Define Parameter"
              value={formData.credentialName.param}
              onChange={(e) => handleParamChange('credentialName', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <input
              type="text"
              placeholder="Define Value"
              value={formData.credentialName.value}
              onChange={(e) => handleInputChange('credentialName', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
          </div>
        );
      
      case 'Client Certificate':
        return (
          <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center', marginBottom: '15px' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>
              Private Key Alias: <span style={{ color: 'red' }}>*</span>
            </label>
            <input
              type="text"
              placeholder="Define Parameter"
              value={formData.privateKeyAliasAuth.param}
              onChange={(e) => handleParamChange('privateKeyAliasAuth', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <input
              type="text"
              placeholder="Define Value"
              value={formData.privateKeyAliasAuth.value}
              onChange={(e) => handleInputChange('privateKeyAliasAuth', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
          </div>
        );
      
      case 'Dynamic':
        return (
          <>
            <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center', marginBottom: '15px' }}>
              <label style={{ color: '#333', fontSize: '14px' }}>
                Credential Name: <span style={{ color: 'red' }}>*</span>
              </label>
              <input
                type="text"
                placeholder="Define Parameter"
                value={formData.credentialName.param}
                onChange={(e) => handleParamChange('credentialName', e.target.value)}
                style={{
                  padding: '8px 12px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              />
              <input
                type="text"
                placeholder="Define Value"
                value={formData.credentialName.value}
                onChange={(e) => handleInputChange('credentialName', e.target.value)}
                style={{
                  padding: '8px 12px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              />
            </div>
            <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center', marginBottom: '15px' }}>
              <label style={{ color: '#333', fontSize: '14px' }}>
                Private Key Alias: <span style={{ color: 'red' }}>*</span>
              </label>
              <input
                type="text"
                placeholder="Define Parameter"
                value={formData.privateKeyAliasAuth.param}
                onChange={(e) => handleParamChange('privateKeyAliasAuth', e.target.value)}
                style={{
                  padding: '8px 12px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              />
              <input
                type="text"
                placeholder="Define Value"
                value={formData.privateKeyAliasAuth.value}
                onChange={(e) => handleInputChange('privateKeyAliasAuth', e.target.value)}
                style={{
                  padding: '8px 12px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              />
            </div>
          </>
        );
      
      default:
        return null;
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    
    try {
      // Create an array of all parameter updates
      const updates = [
        { name: formData.privateKeyAlias.param, value: formData.privateKeyAlias.value },
        { name: formData.signatureEncoding.param, value: formData.signatureEncoding.value },
        { name: formData.propagateMDNDetails.param, value: formData.propagateMDNDetails.value },
        { name: formData.negativeMDNProcessing.param, value: formData.negativeMDNProcessing.value },
        { name: formData.proxyType.param, value: formData.proxyType.value },
        { name: formData.authentication.param, value: formData.authentication.value },
        { name: formData.timeout.param, value: formData.timeout.value },
        ...(formData.credentialName.param ? 
          [{ name: formData.credentialName.param, value: formData.credentialName.value }] : []),
        ...(formData.privateKeyAliasAuth.param ? 
          [{ name: formData.privateKeyAliasAuth.param, value: formData.privateKeyAliasAuth.value }] : [])
      ].filter(update => update.name); // Only include parameters with names

      if (updates.length === 0) {
        setSnackbar({
          open: true,
          message: "Please enter at least one parameter name",
          severity: "error"
        });
        setLoading(false);
        return;
      }

      // Execute all updates sequentially
      let lastResponse = null;
      for (const update of updates) {
        const endpoint = `${API_ENDPOINT}/api/updateParam/${artifactId}/${update.name}`;
        
        const response = await fetch(endpoint, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            parameterValue: update.value,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update parameter ${update.name}`);
        }
        
        // Store the last successful response
        lastResponse = await response.json();
      }

      // Show the success message from the API response
      if (lastResponse && lastResponse.status) {
        setSnackbar({
          open: true,
          message: lastResponse.status,
          severity: "success"
        });
      } else {
        setSnackbar({
          open: true,
          message: "MDN parameters updated successfully",
          severity: "success"
        });
      }
    } catch (error) {
      console.error("Error updating MDN parameters:", error);
      setSnackbar({
        open: true,
        message: error.message || "Failed to update some MDN parameters",
        severity: "error"
      });
    } finally {
      setLoading(false);
      // if (onClose) onClose();
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', paddingBottom: '80px' }}>
      {/* Response MDN Section */}
      <div style={{ marginBottom: '32px' }}>
        <h3 style={{ 
          color: '#6c757d', 
          fontSize: '12px', 
          fontWeight: 'bold',
          marginBottom: '16px',
          textTransform: 'uppercase',
          letterSpacing: '0.5px'
        }}>
          RESPONSE MDN
        </h3>
        
        <div style={{ display: 'grid', gap: '16px' }}>
          {/* Private Key Alias for Signature */}
          <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>
              Private Key Alias for Signature:
            </label>
            <input
              type="text"
              placeholder="Define Parameter"
              value={formData.privateKeyAlias.param}
              onChange={(e) => handleParamChange('privateKeyAlias', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <input
              type="text"
              placeholder="Define Value"
              value={formData.privateKeyAlias.value}
              onChange={(e) => handleInputChange('privateKeyAlias', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
          </div>

          {/* Signature Encoding */}
          <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>
              Signature Encoding: <span style={{ color: 'red' }}>*</span>
            </label>
            <input
              type="text"
              placeholder="Define Parameter"
              value={formData.signatureEncoding.param}
              onChange={(e) => handleParamChange('signatureEncoding', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <FormControl size="small">
              <Select
                value={formData.signatureEncoding.value}
                onChange={(e) => handleInputChange('signatureEncoding', e.target.value)}
                style={{ fontSize: '14px' }}
              >
                <MenuItem value="binary">binary</MenuItem>
                <MenuItem value="base64">base64</MenuItem>
              </Select>
            </FormControl>
          </div>

          {/* Propagate MDN Details to Exchange */}
          <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>
              Propagate MDN Details to Exchange: <span style={{ color: 'red' }}>*</span>
            </label>
            <input
              type="text"
              placeholder="Define Parameter"
              value={formData.propagateMDNDetails.param}
              onChange={(e) => handleParamChange('propagateMDNDetails', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <FormControl size="small">
              <Select
                value={formData.propagateMDNDetails.value}
                onChange={(e) => handleInputChange('propagateMDNDetails', e.target.value)}
                style={{ fontSize: '14px' }}
              >
                <MenuItem value="Not Required">Not Required</MenuItem>
                <MenuItem value="Required">Required</MenuItem>
                <MenuItem value="Dynamic">Dynamic</MenuItem>
              </Select>
            </FormControl>
          </div>

          {/* Negative MDN on Processing Failures */}
          <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>
              Negative MDN on Processing Failures: <span style={{ color: 'red' }}>*</span>
            </label>
            <input
              type="text"
              placeholder="Define Parameter"
              value={formData.negativeMDNProcessing.param}
              onChange={(e) => handleParamChange('negativeMDNProcessing', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <FormControl size="small">
              <Select
                value={formData.negativeMDNProcessing.value}
                onChange={(e) => handleInputChange('negativeMDNProcessing', e.target.value)}
                style={{ fontSize: '14px' }}
              >
                <MenuItem value="Not Required">Not Required</MenuItem>
                <MenuItem value="Required">Required</MenuItem>
                <MenuItem value="Dynamic">Dynamic</MenuItem>
              </Select>
            </FormControl>
          </div>
        </div>
      </div>

      {/* Asynchronous MDN Connection Settings */}
      <div style={{ marginBottom: '32px' }}>
        <h3 style={{ 
          color: '#6c757d', 
          fontSize: '12px', 
          fontWeight: 'bold',
          marginBottom: '16px',
          textTransform: 'uppercase',
          letterSpacing: '0.5px'
        }}>
          ASYNCHRONOUS MDN CONNECTION SETTINGS
        </h3>
        
        <div style={{ display: 'grid', gap: '16px' }}>
          {/* Proxy Type */}
          <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>
              Proxy Type:
            </label>
            <input
              type="text"
              placeholder="Define Parameter"
              value={formData.proxyType.param}
              onChange={(e) => handleParamChange('proxyType', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <FormControl size="small">
              <Select
                value={formData.proxyType.value}
                onChange={(e) => handleInputChange('proxyType', e.target.value)}
                style={{ fontSize: '14px' }}
              >
                <MenuItem value="Internet">Internet</MenuItem>
                <MenuItem value="Dynamic">Dynamic</MenuItem>
                <MenuItem value="On-Premise">On-Premise</MenuItem>
              </Select>
            </FormControl>
          </div>

          {/* Authentication */}
          <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>
              Authentication: <span style={{ color: 'red' }}>*</span>
            </label>
            <input
              type="text"
              placeholder="Define Parameter"
              value={formData.authentication.param}
              onChange={(e) => handleParamChange('authentication', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <FormControl size="small">
              <Select
                value={formData.authentication.value}
                onChange={(e) => handleInputChange('authentication', e.target.value)}
                style={{ fontSize: '14px' }}
              >
                <MenuItem value="None">None</MenuItem>
                <MenuItem value="Basic Authentication">Basic Authentication</MenuItem>
                <MenuItem value="Client Certificate">Client Certificate</MenuItem>
                <MenuItem value="Dynamic">Dynamic</MenuItem>
              </Select>
            </FormControl>
          </div>

          {/* Authentication-specific fields */}
          {renderAuthenticationFields()}

          {/* Timeout */}
          <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>
              Timeout (in ms): <span style={{ color: 'red' }}>*</span>
            </label>
            <input
              type="text"
              placeholder="Define Parameter"
              value={formData.timeout.param}
              onChange={(e) => handleParamChange('timeout', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <input
              type="text"
              placeholder="Define Value"
              value={formData.timeout.value}
              onChange={(e) => handleInputChange('timeout', e.target.value)}
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
          </div>
        </div>
      </div>

     <div style={{
        backgroundColor: '#f8f9fa',
        borderTop: '1px solid #dee2e6',
        padding: '15px 20px',
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '10px',
        zIndex: 1000
      }}>
        <button 
          onClick={handleSubmit}
          disabled={loading}
          style={{
            padding: '8px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'background-color 0.2s',
            opacity: loading ? 0.7 : 1
          }}
          onMouseOver={(e) => !loading && (e.target.style.backgroundColor = '#0056b3')}
          onMouseOut={(e) => !loading && (e.target.style.backgroundColor = '#007bff')}
        >
          {loading ? 'Updating...' : 'OK'}
        </button>
        <button 
          onClick={handleCancel}
          style={{
            padding: '8px 20px',
            backgroundColor: 'transparent',
            color: '#007bff',
            border: '1px solid #007bff',
            borderRadius: '4px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = '#007bff';
            e.target.style.color = 'white';
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = 'transparent';
            e.target.style.color = '#007bff';
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default MDNTab;