// import { useState } from "react";
import "../App.css";
import './ConfigureIDOCModel.css';
import ConfigureSftpParams from "./Configuresftpparams";
import IDOCConfigurationParam from "./IDOCConfigurationParam";

const ConfigureIDOC = ({ isOpen, onClose, iFlowName }) => {
  if (!isOpen) return null;

  return (
    <div className="scheduler-modal-overlay">
      <div className="scheduler-modal-content">
        <div className="scheduler-modal-header">
          <h3>Configure IDOC for {iFlowName}</h3>
        </div>
        <div className="scheduler-modal-body"
        style={{display:'flex',
        flexDirection:'column',
        alignItems:'center',
        justifyContent:'center',
        gap:'20px',
        height:'100%'}}>
          <IDOCConfigurationParam artifactId={iFlowName} onClose={onClose} />
          <ConfigureSftpParams artifactId={iFlowName} onClose={onClose} header="IDOC Parameters Configuration" targetapi="/api/updateParamIdocTarget/" sourceapi="/api/updateParamIdocSource/"/>
        </div>
      </div>
    </div>
  );
};

export default ConfigureIDOC;
