import React, { useState } from "react";
import APIEndpoint from "../config";

const TriggerFlowModal = ({
  isOpen,
  onClose,
  iFlowName,
  startTime,
  endTime,
}) => {
  const [clientId, setClientId] = useState("");
  const [clientSecret, setClientSecret] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!clientId.trim() || !clientSecret.trim()) {
      alert("Please fill in both Client ID and Client Secret");
      return;
    }

    if (!iFlowName) {
      alert("I-Flow Name is required");
      return;
    }

    if (!startTime || !endTime) {
      alert("Start Time and End Time are required");
      return;
    }

    setIsSubmitting(true);

    try {
      const isoStartTime = new Date(startTime).toISOString();
      const isoEndTime = new Date(endTime).toISOString();

      const params = new URLSearchParams({
        cpiClientId: clientId.trim(),
        cpiClientSecret: clientSecret.trim(),
        artifactName: iFlowName,
        startTime: isoStartTime,
        endTime: isoEndTime,
      });

      const response = await fetch(
        `${APIEndpoint}/extractInputPayloads?${params.toString()}`,
        {
          method: "GET",
          headers: {
              "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log("Trigger Flow result:", result);

      if (result.success) {
        alert(`Flow triggered successfully and output fetched from CPI.\n${result.message}`);
      } else {
        alert("Flow triggered, but no output was fetched from CPI.");
      }

      setTimeout(() => {
        onClose();
        setClientId("");
        setClientSecret("");
      }, 2000);
    } catch (error) {
      console.error("Trigger Flow error:", error);
      alert(`Error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setClientId("");
    setClientSecret("");
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          padding: "30px",
          borderRadius: "8px",
          width: "500px",
          maxWidth: "90%",
          boxShadow: "0 4px 20px rgba(0, 0, 0, 0.15)",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "20px",
          }}
        >
          <h3 style={{ margin: 0, color: "#333" }}>Trigger Flow</h3>
          <button
            onClick={handleClose}
            style={{
              background: "none",
              border: "none",
              fontSize: "24px",
              cursor: "pointer",
              color: "#999",
              padding: "0",
              width: "30px",
              height: "30px",
            }}
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: "15px" }}>
            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500", color: "#555" }}>
              Client ID *
            </label>
            <input
              type="text"
              value={clientId}
              onChange={(e) => setClientId(e.target.value)}
              style={{
                width: "100%",
                padding: "10px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "14px",
              }}
              placeholder="Enter Client ID"
              disabled={isSubmitting}
            />
          </div>

          <div style={{ marginBottom: "15px" }}>
            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500", color: "#555" }}>
              Client Secret *
            </label>
            <input
              type="password"
              value={clientSecret}
              onChange={(e) => setClientSecret(e.target.value)}
              style={{
                width: "100%",
                padding: "10px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "14px",
              }}
              placeholder="Enter Client Secret"
              disabled={isSubmitting}
            />
          </div>

          <div style={{ marginBottom: "15px" }}>
            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500", color: "#555" }}>
              Start Time
            </label>
            <input
              type="text"
              value={startTime || ""}
              readOnly
              style={{
                width: "100%",
                padding: "10px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "14px",
                backgroundColor: "#f5f5f5",
                color: "#666",
              }}
            />
          </div>

          <div style={{ marginBottom: "15px" }}>
            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500", color: "#555" }}>
              End Time
            </label>
            <input
              type="text"
              value={endTime || ""}
              readOnly
              style={{
                width: "100%",
                padding: "10px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "14px",
                backgroundColor: "#f5f5f5",
                color: "#666",
              }}
            />
          </div>

          <div style={{ marginBottom: "15px" }}>
            <label style={{ display: "block", marginBottom: "5px", fontWeight: "500", color: "#555" }}>
              I-Flow Name
            </label>
            <input
              type="text"
              value={iFlowName || ""}
              readOnly
              style={{
                width: "100%",
                padding: "10px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "14px",
                backgroundColor: "#f5f5f5",
                color: "#666",
              }}
            />
          </div>

          <div style={{ display: "flex", justifyContent: "flex-end", gap: "10px" }}>
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              style={{
                padding: "10px 20px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                backgroundColor: "white",
                color: "#666",
                cursor: isSubmitting ? "not-allowed" : "pointer",
                fontSize: "14px",
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              style={{
                padding: "10px 20px",
                border: "none",
                borderRadius: "4px",
                backgroundColor: isSubmitting ? "#ccc" : "#007bff",
                color: "white",
                cursor: isSubmitting ? "not-allowed" : "pointer",
                fontSize: "14px",
              }}
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TriggerFlowModal;
