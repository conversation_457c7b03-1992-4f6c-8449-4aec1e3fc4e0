// import React, { useState } from 'react';
// import {
//   Box,
//   Button,
//   FormControl,
//   InputLabel,
//   Select,
//   MenuItem,
//   Typography,
//   Container,
//   Alert,
//   CircularProgress,
//   Link
// } from '@mui/material';
// import { Download } from 'lucide-react';
// import ChatIcon from '@mui/icons-material/Chat';
// import ChatbotMain from "./Chatbot/ChatbotMain";
// const GroovyConvertor = () => {
//   const [language, setLanguage] = useState("java");
//   const [sourceCode, setSourceCode] = useState("// Write your Java code here...");
//   const [convertedCode, setConvertedCode] = useState("// Converted Groovy code will appear here...");
//   const [loading, setLoading] = useState(false);
//  const [chatbotOpen, setChatbotOpen] = useState(false);
//   const languages = [
//     { value: "java", label: "JAVA", enabled: true },
//     { value: "javascript", label: "JavaScript", enabled: true },
//     { value: "dwl", label: "DWL", enabled: false }
//   ];
//   const handleConvert = async () => {
//     if (language !== "java" && language !== "javascript") return;

//     setLoading(true);
//     try {
//       const response = await fetch(
//         "https://test-automation-tool.cfapps.us10-001.hana.ondemand.com/api/groovy/convertScriptsToGroovy",
//         {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//             Authorization: `Basic ${btoa("intswitchjavaX92:aK7wPq93TdYxR14FbE")}`,
//           },
//           body: JSON.stringify({
//             type: language,
//             code: sourceCode,
//           }),
//         }
//       );

//       const data = await response.json();
//       setConvertedCode(data.groovyScript || "// No output from API");
//     } catch (error) {
//       console.error(error);
//       setConvertedCode("// Error while converting");
//     }
//     setLoading(false);
//   };

//   const handleDownload = () => {
//     const blob = new Blob([convertedCode], { type: 'text/plain' });
//     const url = URL.createObjectURL(blob);
//     const a = document.createElement('a');
//     a.href = url;
//     a.download = 'converted-script.groovy';
//     document.body.appendChild(a);
//     a.click();
//     document.body.removeChild(a);
//     URL.revokeObjectURL(url);
//   };

//   const handleLanguageChange = (event) => {
//     const newLanguage = event.target.value;
//     setLanguage(newLanguage);
//     const langObj = languages.find(lang => lang.value === newLanguage);
//     setSourceCode(`// Write your ${langObj.label} code here...`);
//     setConvertedCode("// Converted Groovy code will appear here...");
//   };
//   const selectedLanguage = languages.find(lang => lang.value === language);
//   const canDownload = convertedCode !== "// Converted Groovy code will appear here..." && 
//                      convertedCode !== "// Error while converting" &&
//                      convertedCode !== "// No output from API";

//   return (
//     <div
//       style={{
//         padding: "20px",
//         minHeight: "100vh",
//         backgroundColor: "white",
//         position: "relative",
//       }}
//     >
//       <div
//         style={{
//           // marginBottom: "30px",
//           borderBottom: "2px solid #e5e7eb",
//           // paddingBottom: "15px",
//         }}
//       >
//         <h1
//           style={{
//             fontSize: "28px",
//             fontWeight: "bold",
//             color: "#1f2937",
//             margin: "6px",
//           }}
//         >
//           Groovy Convertor
//         </h1>
//         <p
//           style={{
//             color: "#6b7280",
//             fontSize: "16px",
//             margin: "0",
//           }}
//         >
//           Convert JS , JAVA , DWL Files to groovy
//         </p>
//       </div>


//     <Box sx={{ 
//       minHeight: '100vh', 
//       // backgroundColor: '#f5f5f5',
//       p: 3
//     }}>
//       <Container maxWidth="xl">
//         {/* Top Section with Dropdowns */}
//         <Box sx={{ 
//           display: 'flex', 
//           justifyContent: 'space-between', 
//           alignItems: 'flex-end'
//         }}>
//           {/* From Language */}
//           <Box>
//             <Typography variant="body2" sx={{ color: '#666', mb: 1,textAlign:"left"}}>
//               From Language
//             </Typography>
//             <FormControl sx={{ minWidth: 200, width: "20rem"}}>
//               <Select
//                 value={language}
//                 onChange={handleLanguageChange}
//                 displayEmpty
//                 sx={{ 
//                   backgroundColor: 'white',
//                   height:"2rem",
//                   padding:"7.5px 16px",
//                   color: '#333',
//                   '& .MuiOutlinedInput-notchedOutline': {
//                     borderColor: '#ddd'
//                   },
//                   '&:hover .MuiOutlinedInput-notchedOutline': {
//                     borderColor: '#999'
//                   },
//                   '& .MuiSvgIcon-root': {
//                     color: '#333'
//                   }
//                 }}
//               >
//                 {languages.map((lang) => (
//                   <MenuItem 
//                     key={lang.value} 
//                     value={lang.value}
//                     disabled={!lang.enabled}
//                     sx={{
//                       backgroundColor: 'white',
//                       color: lang.enabled ? '#333' : '#999',
//                       '&:hover': {
//                         backgroundColor: lang.enabled ? '#f5f5f5' : 'white'
//                       },
//                       '&.Mui-selected': {
//                         backgroundColor: '#e3f2fd',
//                       }
//                     }}
//                   >
//                     {lang.label}
//                     {!lang.enabled && (
//                       <Typography variant="caption" sx={{ ml: 1, color: '#666' }}>
//                         (Available Soon)
//                       </Typography>
//                     )}
//                   </MenuItem>
//                 ))}
//               </Select>
//             </FormControl>
//           </Box>

//           {/* To Language */}
//           <Box>
//             <Typography variant="body2" sx={{ color: '#666', mb: 1,textAlign: "left" }}>
//               To Language
//             </Typography>
//             <FormControl sx={{ minWidth: 200 ,width: "20rem"}}>
//               <Select
//                 value="groovy"
//                 displayEmpty
//                 disabled
//                 sx={{ 
//                   backgroundColor: 'white',
//                   height:"2rem",
//                   color: '#333',
//                   '& .MuiOutlinedInput-notchedOutline': {
//                     borderColor: '#ddd'
//                   },
//                   '& .MuiSvgIcon-root': {
//                     color: '#333'
//                   }
//                 }}
//               >
//                 <MenuItem value="groovy" sx={{ backgroundColor: 'white', color: '#333' }}>
//                   Groovy
//                 </MenuItem>
//               </Select>
//             </FormControl>
//           </Box>
//         </Box>

//         {/* Convert Button */}
//         <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
//           <Button
//             variant="contained"
//             onClick={handleConvert}
//             disabled={loading || !selectedLanguage?.enabled}
//             sx={{
//               backgroundColor: '#1565c0',
//               color: 'white',
//               px: 6,
//               py: 1.5,
//               fontSize: '16px',
//               fontWeight: 'bold',
//               borderRadius: 2,
//               textTransform: 'uppercase',
//               '&:hover': {
//                 backgroundColor: '#6a39d1',
//               },
//               '&:disabled': {
//                 backgroundColor: '#e0e0e0',
//                 color: '#999'
//               }
//             }}
//           >
//             {loading ? (
//               <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
//                 <CircularProgress size={16} sx={{ color: 'white' }} />
//                 Converting...
//               </Box>
//             ) : (
//               'CONVERT CODE!'
//             )}
//           </Button>
//         </Box>



//         {/* Code Editors */}
//         <Box sx={{ 
//           display: 'grid', 
//           gridTemplateColumns: '1fr 1fr', 
//           gap: 3, 
//           mb: 4 
//         }}>
//           {/* Source Code Editor */}
//           <Box sx={{ 
//             backgroundColor: 'white',
//             borderRadius: 2,
//             overflow: 'hidden',
//             border: '1px solid #ddd',
//             boxShadow: 1
//           }}>
//             <textarea
//               value={sourceCode}
//               onChange={(e) => setSourceCode(e.target.value)}
//               disabled={!selectedLanguage?.enabled}
//               style={{
//                 width: '100%',
//                 height: '400px',
//                 padding: '16px',
//                 fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
//                 fontSize: '14px',
//                 border: 'none',
//                 outline: 'none',
//                 resize: 'none',
//                 backgroundColor: selectedLanguage?.enabled ? 'white' : '#f5f5f5',
//                 color: selectedLanguage?.enabled ? '#333' : '#999',
//                 cursor: selectedLanguage?.enabled ? 'text' : 'not-allowed'
//               }}
//             />
//           </Box>

//           {/* Groovy Output Editor */}
//           <Box sx={{ 
//             backgroundColor: 'white',
//             borderRadius: 2,
//             overflow: 'hidden',
//             border: '1px solid #ddd',
//             boxShadow: 1
//           }}>
//             <textarea
//               value={convertedCode}
//               readOnly
//               style={{
//                 width: '100%',
//                 height: '400px',
//                 padding: '16px',
//                 fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
//                 fontSize: '14px',
//                 border: 'none',
//                 outline: 'none',
//                 resize: 'none',
//                 backgroundColor: 'white',
//                 color: '#333',
//                 cursor: 'default'
//               }}
//             />
//           </Box>
//         </Box>

//         {/* Bottom Action Buttons */}
//         <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mb: 3 }}>
//           <Button
//             variant="outlined"
//             onClick={() => {
//               setSourceCode(`// Write your ${selectedLanguage?.label} code here...`);
//               setConvertedCode("// Converted Groovy code will appear here...");
//             }}
//             sx={{ 
//               borderColor: '#1565c0',
//               color: '#1565c0',
//               px: 4,
//               py: 1,
//               '&:hover': {
//                 borderColor: '#6a39d1',
//                 backgroundColor: 'rgba(124, 77, 255, 0.1)'
//               }
//             }}
//           >
//             Clear ✕
//           </Button>

//           <Button
//             variant="contained"
//             onClick={handleDownload}
//             disabled={!canDownload}
//             startIcon={<Download size={20} />}
//             sx={{ 
//               backgroundColor: '#4caf50',
//               px: 4,
//               py: 1,
//               '&:hover': {
//                 backgroundColor: '#45a049'
//               },
//               '&:disabled': {
//                 backgroundColor: '#e0e0e0',
//                 color: '#999'
//               }
//             }}
//           >
//             Download
//           </Button>
//         </Box>

//         <button 
//                   className="chatbot-toggle-button"
//                   onClick={() => setChatbotOpen(!chatbotOpen)}
//                   style={{
//                     position: 'fixed',
//                     bottom: '4rem',
//                     right: '20px',
//                     zIndex: 1000,
//                     backgroundColor: '#1565c0',
//                     color: 'white',
//                     border: 'none',
//                     borderRadius: '50%',
//                     width: '60px',
//                     height: '60px',
//                     cursor: 'pointer',
//                     boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
//                     display: 'flex',
//                     alignItems: 'center',
//                     justifyContent: 'center'
//                   }}
//                 >
//                   <ChatIcon style={{ fontSize: '30px' }} />
//                 </button>

//                 {/* Chatbot component */}
//                 {chatbotOpen && (
//                   <div style={{
//                     border: "1px solid black",
//                     marginTop: "10rem",
//                     position: 'fixed',
//                     bottom: '11rem',
//                     right: '20px',
//                     zIndex: 999,
//                     width: '25%',
//                     height: '500px',
//                     backgroundColor: 'white',
//                     borderRadius: '10px',
//                     boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
//                     overflow: 'hidden'
//                   }}>
//                     <ChatbotMain />
//                   </div>
//                 )}


//         {/* Status Alert */}
//         {!selectedLanguage?.enabled && (
//           <Alert 
//             severity="info" 
//             sx={{ 
//               maxWidth: 600, 
//               mx: 'auto'
//             }}
//           >
//             <Typography variant="body1">
//               <strong>{selectedLanguage?.label}</strong> to Groovy conversion is coming soon! 
//               Currently only Java conversion is available.
//             </Typography>
//           </Alert>
//         )}
//       </Container>
//     </Box></div>
//   );
// };

// export default GroovyConvertor;







// import React, { useState } from 'react';
// import {
//   Box,
//   Button,
//   FormControl,
//   InputLabel,
//   Select,
//   MenuItem,
//   Typography,
//   Container,
//   Alert,
//   CircularProgress,
//   Link
// } from '@mui/material';
// import { Download } from 'lucide-react';
// import ChatIcon from '@mui/icons-material/Chat';
// import ChatbotMain from "./Chatbot/ChatbotMain";
// import {
  
//   FaTimes,
//   FaUpload,
// } from "react-icons/fa";
// const GroovyConvertor = () => {
//   const [language, setLanguage] = useState("java");
//   const [sourceCode, setSourceCode] = useState("// Write your Java code here...");
//   const [convertedCode, setConvertedCode] = useState("// Converted Groovy code will appear here...");
//   const [loading, setLoading] = useState(false);
//   const [chatbotOpen, setChatbotOpen] = useState(false);
//   const languages = [
//     { value: "java", label: "JAVA", enabled: true },
//     { value: "javascript", label: "JavaScript", enabled: true },
//     { value: "dwl", label: "DWL", enabled: false }
//   ];

//   const [showCpiUploadModal, setShowCpiUploadModal] = useState(false);
//   const [cpiUploadData, setCpiUploadData] = useState({
//     artifactId: "",
//     scriptTag: "",
//   });
//   const [cpiUploadLoading, setCpiUploadLoading] = useState(false);
//   const [cpiUploadError, setCpiUploadError] = useState(null);
//   const openCpiUploadModal = () => {
//     setShowCpiUploadModal(true);
//     setCpiUploadData({
//       artifactId: "",
//       scriptTag: "",
//     });
//     setCpiUploadError(null);
//   };

//   const closeCpiUploadModal = () => {
//       setShowCpiUploadModal(false);
//       setCpiUploadError(null);
//     };
  
//     const handleCpiInputChange = (e) => {
//       const { name, value } = e.target;
//       setCpiUploadData((prev) => ({
//         ...prev,
//         [name]: value,
//       }));
//     };
  
//     const handleCpiUploadSubmit = async (e) => {
//       e.preventDefault();
  
//       // Validation
//       if (!cpiUploadData.artifactId.trim()) {
//         setCpiUploadError("Artifact ID is required");
//         return;
//       }
//       if (!cpiUploadData.scriptTag.trim()) {
//         setCpiUploadError("Script Tag is required");
//         return;
//       }
  
//       try {
//         setCpiUploadLoading(true);
//         setCpiUploadError(null);
  
//         const formData = new FormData();
//         formData.append("artifactId", cpiUploadData.artifactId);
//         formData.append("scriptTag", cpiUploadData.scriptTag);
  
//         const response = await fetch(`${APIEndpoint}/api/groovy/addResources`, {
//           method: "POST",
//           body: formData,
//           headers: {
//             "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
//           },
//         });
  
//         if (!response.ok) {
//           throw new Error(`HTTP error! status: ${response.status}`);
//         }
  
//         // Success - close dialog and show success message
//         alert("Script successfully uploaded to CPI!");
//         closeCpiUploadModal();
//       } catch (err) {
//         setCpiUploadError(err.message);
//         console.error("Error uploading to CPI:", err);
//       } finally {
//         setCpiUploadLoading(false);
//       }
//     };
  
//   const handleConvert = async () => {
//     if (language !== "java" && language !== "javascript") return;

//     setLoading(true);
//     try {
//       const response = await fetch(
//         "https://test-automation-tool.cfapps.us10-001.hana.ondemand.com/api/groovy/convertScriptsToGroovy",
//         {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//             Authorization: `Basic ${btoa("intswitchjavaX92:aK7wPq93TdYxR14FbE")}`,
//           },
//           body: JSON.stringify({
//             type: language,
//             code: sourceCode,
//           }),
//         }
//       );

//       const data = await response.json();
//       setConvertedCode(data.groovyScript || "// No output from API");
//     } catch (error) {
//       console.error(error);
//       setConvertedCode("// Error while converting");
//     }
//     setLoading(false);
//   };

//   const handleDownload = () => {
//     const blob = new Blob([convertedCode], { type: 'text/plain' });
//     const url = URL.createObjectURL(blob);
//     const a = document.createElement('a');
//     a.href = url;
//     a.download = 'converted-script.groovy';
//     document.body.appendChild(a);
//     a.click();
//     document.body.removeChild(a);
//     URL.revokeObjectURL(url);
//   };

//   const handleLanguageChange = (event) => {
//     const newLanguage = event.target.value;
//     setLanguage(newLanguage);
//     const langObj = languages.find(lang => lang.value === newLanguage);
//     setSourceCode(`// Write your ${langObj.label} code here...`);
//     setConvertedCode("// Converted Groovy code will appear here...");
//   };
//   const selectedLanguage = languages.find(lang => lang.value === language);
//   const canDownload = convertedCode !== "// Converted Groovy code will appear here..." &&
//     convertedCode !== "// Error while converting" &&
//     convertedCode !== "// No output from API";

//   return (
//     <div
//       style={{
//         padding: "20px",
//         minHeight: "100vh",
//         backgroundColor: "white",
//         position: "relative",
//       }}
//     >
//       <div
//         style={{
//           // marginBottom: "30px",
//           borderBottom: "2px solid #e5e7eb",
//           // paddingBottom: "15px",
//         }}
//       >
//         <h1
//           style={{
//             fontSize: "28px",
//             fontWeight: "bold",
//             color: "#1f2937",
//             margin: "6px",
//           }}
//         >
//           Groovy Convertor
//         </h1>
//         <p
//           style={{
//             color: "#6b7280",
//             fontSize: "16px",
//             margin: "0",
//           }}
//         >
//           Convert JS , JAVA , DWL Files to groovy
//         </p>
//       </div>


//       <Box sx={{
//         minHeight: '100vh',
//         // backgroundColor: '#f5f5f5',
//         p: 3
//       }}>
//         <Container maxWidth="xl">
//           {/* Top Section with Dropdowns */}
//           <Box sx={{
//             display: 'flex',
//             justifyContent: 'space-between',
//             alignItems: 'flex-end'
//           }}>
//             {/* From Language */}
//             <Box>
//               <Typography variant="body2" sx={{ color: '#666', mb: 1, textAlign: "left" }}>
//                 From Language
//               </Typography>
//               <FormControl sx={{ minWidth: 200, width: "20rem" }}>
//                 <Select
//                   value={language}
//                   onChange={handleLanguageChange}
//                   displayEmpty
//                   sx={{
//                     backgroundColor: 'white',
//                     height: "2rem",
//                     padding: "7.5px 16px",
//                     color: '#333',
//                     '& .MuiOutlinedInput-notchedOutline': {
//                       borderColor: '#ddd'
//                     },
//                     '&:hover .MuiOutlinedInput-notchedOutline': {
//                       borderColor: '#999'
//                     },
//                     '& .MuiSvgIcon-root': {
//                       color: '#333'
//                     }
//                   }}
//                 >
//                   {languages.map((lang) => (
//                     <MenuItem
//                       key={lang.value}
//                       value={lang.value}
//                       disabled={!lang.enabled}
//                       sx={{
//                         backgroundColor: 'white',
//                         color: lang.enabled ? '#333' : '#999',
//                         '&:hover': {
//                           backgroundColor: lang.enabled ? '#f5f5f5' : 'white'
//                         },
//                         '&.Mui-selected': {
//                           backgroundColor: '#e3f2fd',
//                         }
//                       }}
//                     >
//                       {lang.label}
//                       {!lang.enabled && (
//                         <Typography variant="caption" sx={{ ml: 1, color: '#666' }}>
//                           (Available Soon)
//                         </Typography>
//                       )}
//                     </MenuItem>
//                   ))}
//                 </Select>
//               </FormControl>
//             </Box>

//             {/* To Language */}
//             <Box>
//               <Typography variant="body2" sx={{ color: '#666', mb: 1, textAlign: "left" }}>
//                 To Language
//               </Typography>
//               <FormControl sx={{ minWidth: 200, width: "20rem" }}>
//                 <Select
//                   value="groovy"
//                   displayEmpty
//                   disabled
//                   sx={{
//                     backgroundColor: 'white',
//                     height: "2rem",
//                     color: '#333',
//                     '& .MuiOutlinedInput-notchedOutline': {
//                       borderColor: '#ddd'
//                     },
//                     '& .MuiSvgIcon-root': {
//                       color: '#333'
//                     }
//                   }}
//                 >
//                   <MenuItem value="groovy" sx={{ backgroundColor: 'white', color: '#333' }}>
//                     Groovy
//                   </MenuItem>
//                 </Select>
//               </FormControl>
//             </Box>
//           </Box>

//           {/* Convert Button */}
//           <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>

//           </Box>



//           {/* Code Editors */}
//           <Box sx={{
//             display: 'grid',
//             gridTemplateColumns: '1fr 1fr',
//             gap: 3,
//             mb: 4
//           }}>
//             {/* Source Code Editor */}
//             <Box sx={{
//               backgroundColor: 'white',
//               borderRadius: 2,
//               overflow: 'hidden',
//               border: '1px solid #ddd',
//               boxShadow: 1
//             }}>
//               <textarea
//                 value={sourceCode}
//                 onChange={(e) => setSourceCode(e.target.value)}
//                 disabled={!selectedLanguage?.enabled}
//                 style={{
//                   width: '100%',
//                   height: '400px',
//                   padding: '16px',
//                   fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
//                   fontSize: '14px',
//                   border: 'none',
//                   outline: 'none',
//                   resize: 'none',
//                   backgroundColor: selectedLanguage?.enabled ? 'white' : '#f5f5f5',
//                   color: selectedLanguage?.enabled ? '#333' : '#999',
//                   cursor: selectedLanguage?.enabled ? 'text' : 'not-allowed'
//                 }}
//               />
//             </Box>

//             {/* Groovy Output Editor */}
//             <Box sx={{
//               backgroundColor: 'white',
//               borderRadius: 2,
//               overflow: 'hidden',
//               border: '1px solid #ddd',
//               boxShadow: 1
//             }}>
//               <textarea
//                 value={convertedCode}
//                 readOnly
//                 style={{
//                   width: '100%',
//                   height: '400px',
//                   padding: '16px',
//                   fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
//                   fontSize: '14px',
//                   border: 'none',
//                   outline: 'none',
//                   resize: 'none',
//                   backgroundColor: 'white',
//                   color: '#333',
//                   cursor: 'default'
//                 }}
//               />
//             </Box>
//           </Box>

//           {/* Bottom Action Buttons */}
//           <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2, mb: 3 }}>
//             <Box style={{ display: "flex", gap: 2 }}>
//               <Button
//                 variant="contained"
//                 onClick={handleConvert}
//                 disabled={loading || !selectedLanguage?.enabled}
//                 sx={{
//                   backgroundColor: '#1565c0',
//                   color: 'white',
//                   px: 6,
//                   py: 1.5,
//                   fontSize: '16px',
//                   fontWeight: 'bold',
//                   borderRadius: 2,
//                   textTransform: 'uppercase',
//                   '&:hover': {
//                     backgroundColor: '#6a39d1',
//                   },
//                   '&:disabled': {
//                     backgroundColor: '#e0e0e0',
//                     color: '#999'
//                   }
//                 }}
//               >
//                 {loading ? (
//                   <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
//                     <CircularProgress size={16} sx={{ color: 'white' }} />
//                     Converting...
//                   </Box>
//                 ) : (
//                   'CONVERT CODE >>>'
//                 )}
//               </Button>

              
//             </Box>
//             <Box style={{ display: "flex", gap: 6 }}>
//               <Button
//                 variant="outlined"
//                 onClick={() => {
//                   setSourceCode(`// Write your ${selectedLanguage?.label} code here...`);
//                   setConvertedCode("// Converted Groovy code will appear here...");
//                 }}
//                 sx={{
//                   borderColor: '#1565c0',
//                   color: '#1565c0',
//                   px: 4,
//                   py: 1,
//                   '&:hover': {
//                     borderColor: '#6a39d1',
//                     backgroundColor: 'rgba(124, 77, 255, 0.1)'
//                   }
//                 }}
//               >
//                 Clear ✕
//               </Button>
//               <Button
//                 variant="contained"
//                 onClick={handleDownload}
//                 disabled={!canDownload}
//                 startIcon={<Download size={20} />}
//                 sx={{
//                   backgroundColor: '#4caf50',
//                   px: 4,
//                   py: 1,
//                   '&:hover': {
//                     backgroundColor: '#45a049'
//                   },
//                   '&:disabled': {
//                     backgroundColor: '#e0e0e0',
//                     color: '#999'
//                   }
//                 }}
//               >
//                 Download
//               </Button>
//               <button
//                 onClick={openCpiUploadModal}
//                 style={{
//                   padding: "8px 16px",
//                   backgroundColor: "gray",
//                   color: "white",
//                   border: "none",
//                   borderRadius: "6px",
//                   cursor: "pointer",
//                   fontSize: "14px",
//                   fontWeight: "500",
//                   display: "flex",
//                   alignItems: "center",
//                   gap: "6px",
//                 }}
//               >
//                 <FaUpload size={14} />
//                 Upload to CPI
//               </button>
//             </Box>
//           </Box>

//           <button
//             className="chatbot-toggle-button"
//             onClick={() => setChatbotOpen(!chatbotOpen)}
//             style={{
//               position: 'fixed',
//               bottom: '4rem',
//               right: '20px',
//               zIndex: 1000,
//               backgroundColor: '#1565c0',
//               color: 'white',
//               border: 'none',
//               borderRadius: '50%',
//               width: '60px',
//               height: '60px',
//               cursor: 'pointer',
//               boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
//               display: 'flex',
//               alignItems: 'center',
//               justifyContent: 'center'
//             }}
//           >
//             <ChatIcon style={{ fontSize: '30px' }} />
//           </button>

//           {/* Chatbot component */}
//           {chatbotOpen && (
//             <div style={{
//               border: "1px solid black",
//               marginTop: "10rem",
//               position: 'fixed',
//               bottom: '11rem',
//               right: '20px',
//               zIndex: 999,
//               width: '25%',
//               height: '500px',
//               backgroundColor: 'white',
//               borderRadius: '10px',
//               boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
//               overflow: 'hidden'
//             }}>
//               <ChatbotMain />
//             </div>
//           )}


//           {/* Status Alert */}
//           {!selectedLanguage?.enabled && (
//             <Alert
//               severity="info"
//               sx={{
//                 maxWidth: 600,
//                 mx: 'auto'
//               }}
//             >
//               <Typography variant="body1">
//                 <strong>{selectedLanguage?.label}</strong> to Groovy conversion is coming soon!
//                 Currently only Java conversion is available.
//               </Typography>
//             </Alert>
//           )}
//         </Container>
//       </Box>
//       {/* CPI Upload Modal */}
//             {showCpiUploadModal && (
//               <div
//                 style={{
//                   position: "fixed",
//                   top: "0",
//                   left: "0",
//                   right: "0",
//                   bottom: "0",
//                   backgroundColor: "rgba(0, 0, 0, 0.5)",
//                   display: "flex",
//                   justifyContent: "center",
//                   alignItems: "center",
//                   zIndex: "1000",
//                   padding: "20px",
//                 }}
//               >
//                 <div
//                   style={{
//                     backgroundColor: "white",
//                     borderRadius: "12px",
//                     width: "90%",
//                     maxWidth: "500px",
//                     maxHeight: "80vh",
//                     display: "flex",
//                     flexDirection: "column",
//                     boxShadow: "0 4px 24px rgba(0, 0, 0, 0.15)",
//                   }}
//                 >
//                   <div
//                     style={{
//                       display: "flex",
//                       justifyContent: "space-between",
//                       alignItems: "center",
//                       padding: "16px 24px",
//                       borderBottom: "1px solid #e5e7eb",
//                     }}
//                   >
//                     <h3
//                       style={{
//                         fontSize: "20px",
//                         fontWeight: "600",
//                         margin: "0",
//                         color: "#1f2937",
//                       }}
//                     >
//                       Upload to CPI
//                     </h3>
//                     <button
//                       onClick={closeCpiUploadModal}
//                       style={{
//                         background: "none",
//                         border: "none",
//                         cursor: "pointer",
//                         fontSize: "20px",
//                         color: "#6b7280",
//                         padding: "4px",
//                       }}
//                     >
//                       <FaTimes />
//                     </button>
//                   </div>
      
//                   <form onSubmit={handleCpiUploadSubmit}>
//                     <div
//                       style={{
//                         padding: "24px",
//                         flex: "1",
//                         overflowY: "auto",
//                       }}
//                     >
//                       {cpiUploadError && (
//                         <div
//                           style={{
//                             backgroundColor: "#fef2f2",
//                             border: "1px solid #fecaca",
//                             color: "#dc2626",
//                             padding: "12px",
//                             borderRadius: "8px",
//                             marginBottom: "16px",
//                             fontSize: "14px",
//                           }}
//                         >
//                           {cpiUploadError}
//                         </div>
//                       )}
      
//                       <div style={{ marginBottom: "16px" }}>
//                         <label
//                           style={{
//                             display: "flex",
//                             fontSize: "14px",
//                             fontWeight: "500",
//                             color: "#374151",
//                             marginBottom: "6px",
//                           }}
//                         >
//                           Artifact ID *
//                         </label>
//                         <input
//                           type="text"
//                           name="artifactId"
//                           value={cpiUploadData.artifactId}
//                           onChange={handleCpiInputChange}
//                           placeholder="Enter artifact ID"
//                           style={{
//                             width: "100%",
//                             padding: "10px 12px",
//                             border: "1px solid #d1d5db",
//                             borderRadius: "6px",
//                             fontSize: "14px",
//                             outline: "none",
//                             transition: "border-color 0.2s ease",
//                           }}
//                           onFocus={(e) => {
//                             e.target.style.borderColor = "#3b82f6";
//                           }}
//                           onBlur={(e) => {
//                             e.target.style.borderColor = "#d1d5db";
//                           }}
//                         />
//                       </div>
      
//                       <div style={{ marginBottom: "16px" }}>
//                         <label
//                           style={{
//                             display: "flex",
//                             fontSize: "14px",
//                             fontWeight: "500",
//                             color: "#374151",
//                             marginBottom: "6px",
//                           }}
//                         >
//                           Script Tag *
//                         </label>
//                         <input
//                           type="text"
//                           name="scriptTag"
//                           value={cpiUploadData.scriptTag}
//                           onChange={handleCpiInputChange}
//                           style={{
//                             width: "100%",
//                             padding: "10px 12px",
//                             border: "1px solid #d1d5db",
//                             borderRadius: "6px",
//                             fontSize: "14px",
//                             outline: "none",
//                             transition: "border-color 0.2s ease",
//                             backgroundColor: "#f3f4f6",
//                             cursor: "not-allowed",
//                           }}
//                           readOnly
//                         />
//                       </div>
//                     </div>
      
//                     <div
//                       style={{
//                         padding: "16px 24px",
//                         borderTop: "1px solid #e5e7eb",
//                         display: "flex",
//                         justifyContent: "flex-end",
//                         gap: "12px",
//                       }}
//                     >
//                       <button
//                         type="button"
//                         onClick={closeCpiUploadModal}
//                         style={{
//                           padding: "10px 16px",
//                           backgroundColor: "#f3f4f6",
//                           color: "#374151",
//                           border: "none",
//                           borderRadius: "6px",
//                           cursor: "pointer",
//                           fontSize: "14px",
//                           fontWeight: "500",
//                         }}
//                       >
//                         Cancel
//                       </button>
//                       <button
//                         type="submit"
//                         disabled={cpiUploadLoading}
//                         style={{
//                           padding: "10px 16px",
//                           backgroundColor: cpiUploadLoading ? "#9ca3af" : "#3b82f6",
//                           color: "white",
//                           border: "none",
//                           borderRadius: "6px",
//                           cursor: cpiUploadLoading ? "not-allowed" : "pointer",
//                           fontSize: "14px",
//                           fontWeight: "500",
//                           display: "flex",
//                           alignItems: "center",
//                           gap: "6px",
//                         }}
//                       >
//                         {cpiUploadLoading ? "Uploading..." : "Upload to CPI"}
//                       </button>
//                     </div>
//                   </form>
//                 </div>
//               </div>
//             )}
//             </div>
//   );
// };

// export default GroovyConvertor;






















import React, { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Container,
  Alert,
  CircularProgress,
  Link
} from '@mui/material';
import { Download } from 'lucide-react';
import ChatIcon from '@mui/icons-material/Chat';
import ChatbotMain from "./Chatbot/ChatbotMain";
import {
  
  FaTimes,
  FaUpload,
} from "react-icons/fa";
const GroovyConvertor = () => {
  const [language, setLanguage] = useState("java");
  const [sourceCode, setSourceCode] = useState("// Write your Java code here...");
  const [convertedCode, setConvertedCode] = useState("// Converted Groovy code will appear here...");
  const [loading, setLoading] = useState(false);
  const [chatbotOpen, setChatbotOpen] = useState(false);
  const languages = [
    { value: "java", label: "JAVA", enabled: true },
    { value: "javascript", label: "JavaScript", enabled: true },
    { value: "dwl", label: "DWL", enabled: false }
  ];

  const [showCpiUploadModal, setShowCpiUploadModal] = useState(false);
  const [cpiUploadData, setCpiUploadData] = useState({
    artifactId: "",
    scriptTag: "",
  });
  const [cpiUploadLoading, setCpiUploadLoading] = useState(false);
  const [cpiUploadError, setCpiUploadError] = useState(null);
  
  const openCpiUploadModal = () => {
    // Check if there's converted code to upload
    if (!convertedCode || 
        convertedCode === "// Converted Groovy code will appear here..." ||
        convertedCode === "// Error while converting" ||
        convertedCode === "// No output from API") {
      alert("Please convert your code first before uploading to CPI!");
      return;
    }
    
    setShowCpiUploadModal(true);
    setCpiUploadData({
      artifactId: "",
      scriptTag: convertedCode, // Automatically populate with converted code
    });
    setCpiUploadError(null);
  };

  const closeCpiUploadModal = () => {
      setShowCpiUploadModal(false);
      setCpiUploadError(null);
    };
  
    const handleCpiInputChange = (e) => {
      const { name, value } = e.target;
      setCpiUploadData((prev) => ({
        ...prev,
        [name]: value,
      }));
    };
  
    const handleCpiUploadSubmit = async (e) => {
      e.preventDefault();
  
      // Validation
      if (!cpiUploadData.artifactId.trim()) {
        setCpiUploadError("Artifact ID is required");
        return;
      }
      if (!cpiUploadData.scriptTag.trim()) {
        setCpiUploadError("Script content is required");
        return;
      }
  
      try {
        setCpiUploadLoading(true);
        setCpiUploadError(null);
  
        // Create a Blob with the Groovy script content
        const groovyFile = new Blob([cpiUploadData.scriptTag], { 
          type: 'text/plain' 
        });
        
        const formData = new FormData();
        formData.append("artifactId", cpiUploadData.artifactId);
        // Append the file with a .groovy extension
        formData.append("groovyFile", groovyFile, "converted-script.groovy");
  
        // Use the API endpoint from the original code
        const APIEndpoint = "https://test-automation-tool.cfapps.us10-001.hana.ondemand.com";
        const response = await fetch(`${APIEndpoint}/api/groovy/addResourcesDirectly`, {
          method: "POST",
          body: formData,
          headers: {
            "Authorization": `Basic ${btoa("intswitchjavaX92:aK7wPq93TdYxR14FbE")}`,
          },
        });
  
        if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
  
        
        // Success - close dialog and show success message
        alert("Groovy script successfully uploaded to CPI!");
        closeCpiUploadModal();
      } catch (err) {
        setCpiUploadError(err.message);
        console.error("Error uploading to CPI:", err);
      } finally {
        setCpiUploadLoading(false);
      }
    };
  
  const handleConvert = async () => {
    if (language !== "java" && language !== "javascript") return;

    setLoading(true);
    try {
      const response = await fetch(
        "https://test-automation-tool.cfapps.us10-001.hana.ondemand.com/api/groovy/convertScriptsToGroovy",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Basic ${btoa("intswitchjavaX92:aK7wPq93TdYxR14FbE")}`,
          },
          body: JSON.stringify({
            type: language,
            code: sourceCode,
          }),
        }
      );

      const data = await response.json();
      setConvertedCode(data.groovyScript || "// No output from API");
    } catch (error) {
      console.error(error);
      setConvertedCode("// Error while converting");
    }
    setLoading(false);
  };

  const handleDownload = () => {
    const blob = new Blob([convertedCode], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'converted-script.groovy';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleLanguageChange = (event) => {
    const newLanguage = event.target.value;
    setLanguage(newLanguage);
    const langObj = languages.find(lang => lang.value === newLanguage);
    setSourceCode(`// Write your ${langObj.label} code here...`);
    setConvertedCode("// Converted Groovy code will appear here...");
  };
  
  const selectedLanguage = languages.find(lang => lang.value === language);
  const canDownload = convertedCode !== "// Converted Groovy code will appear here..." &&
    convertedCode !== "// Error while converting" &&
    convertedCode !== "// No output from API";

  // Check if upload to CPI is available
  const canUploadToCpi = canDownload;

  return (
    <div
      style={{
        padding: "20px",
        minHeight: "100vh",
        backgroundColor: "white",
        position: "relative",
      }}
    >
      <div
        style={{
          // marginBottom: "30px",
          borderBottom: "2px solid #e5e7eb",
          // paddingBottom: "15px",
        }}
      >
        <h1
          style={{
            fontSize: "28px",
            fontWeight: "bold",
            color: "#1f2937",
            margin: "6px",
          }}
        >
          Groovy Convertor
        </h1>
        <p
          style={{
            color: "#6b7280",
            fontSize: "16px",
            margin: "0",
          }}
        >
          Convert JS , JAVA , DWL Files to groovy
        </p>
      </div>


      <Box sx={{
        minHeight: '100vh',
        // backgroundColor: '#f5f5f5',
        p: 3
      }}>
        <Container maxWidth="xl">
          {/* Top Section with Dropdowns */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-end'
          }}>
            {/* From Language */}
            <Box>
              <Typography variant="body2" sx={{ color: '#666', mb: 1, textAlign: "left" }}>
                From Language
              </Typography>
              <FormControl sx={{ minWidth: 200, width: "20rem" }}>
                <Select
                  value={language}
                  onChange={handleLanguageChange}
                  displayEmpty
                  sx={{
                    backgroundColor: 'white',
                    height: "2rem",
                    padding: "7.5px 16px",
                    color: '#333',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#ddd'
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#999'
                    },
                    '& .MuiSvgIcon-root': {
                      color: '#333'
                    }
                  }}
                >
                  {languages.map((lang) => (
                    <MenuItem
                      key={lang.value}
                      value={lang.value}
                      disabled={!lang.enabled}
                      sx={{
                        backgroundColor: 'white',
                        color: lang.enabled ? '#333' : '#999',
                        '&:hover': {
                          backgroundColor: lang.enabled ? '#f5f5f5' : 'white'
                        },
                        '&.Mui-selected': {
                          backgroundColor: '#e3f2fd',
                        }
                      }}
                    >
                      {lang.label}
                      {!lang.enabled && (
                        <Typography variant="caption" sx={{ ml: 1, color: '#666' }}>
                          (Available Soon)
                        </Typography>
                      )}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            {/* To Language */}
            <Box>
              <Typography variant="body2" sx={{ color: '#666', mb: 1, textAlign: "left" }}>
                To Language
              </Typography>
              <FormControl sx={{ minWidth: 200, width: "20rem" }}>
                <Select
                  value="groovy"
                  displayEmpty
                  disabled
                  sx={{
                    backgroundColor: 'white',
                    height: "2rem",
                    color: '#333',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#ddd'
                    },
                    '& .MuiSvgIcon-root': {
                      color: '#333'
                    }
                  }}
                >
                  <MenuItem value="groovy" sx={{ backgroundColor: 'white', color: '#333' }}>
                    Groovy
                  </MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Box>

          {/* Convert Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>

          </Box>



          {/* Code Editors */}
          <Box sx={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: 3,
            mb: 4
          }}>
            {/* Source Code Editor */}
            <Box sx={{
              backgroundColor: 'white',
              borderRadius: 2,
              overflow: 'hidden',
              border: '1px solid #ddd',
              boxShadow: 1
            }}>
              <textarea
                value={sourceCode}
                onChange={(e) => setSourceCode(e.target.value)}
                disabled={!selectedLanguage?.enabled}
                style={{
                  width: '100%',
                  height: '400px',
                  padding: '16px',
                  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                  fontSize: '14px',
                  border: 'none',
                  outline: 'none',
                  resize: 'none',
                  backgroundColor: selectedLanguage?.enabled ? 'white' : '#f5f5f5',
                  color: selectedLanguage?.enabled ? '#333' : '#999',
                  cursor: selectedLanguage?.enabled ? 'text' : 'not-allowed'
                }}
              />
            </Box>

            {/* Groovy Output Editor */}
            <Box sx={{
              backgroundColor: 'white',
              borderRadius: 2,
              overflow: 'hidden',
              border: '1px solid #ddd',
              boxShadow: 1
            }}>
              <textarea
                value={convertedCode}
                readOnly
                style={{
                  width: '100%',
                  height: '400px',
                  padding: '16px',
                  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                  fontSize: '14px',
                  border: 'none',
                  outline: 'none',
                  resize: 'none',
                  backgroundColor: 'white',
                  color: '#333',
                  cursor: 'default'
                }}
              />
            </Box>
          </Box>

          {/* Bottom Action Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2, mb: 3 }}>
            <Box style={{ display: "flex", gap: 2 }}>
              <Button
                variant="contained"
                onClick={handleConvert}
                disabled={loading || !selectedLanguage?.enabled}
                sx={{
                  backgroundColor: '#1565c0',
                  color: 'white',
                  px: 6,
                  py: 1.5,
                  fontSize: '16px',
                  fontWeight: 'bold',
                  borderRadius: 2,
                  textTransform: 'uppercase',
                  '&:hover': {
                    backgroundColor: '#6fa7e7ff',
                  },
                  '&:disabled': {
                    backgroundColor: '#e0e0e0',
                    color: '#999'
                  }
                }}
              >
                {loading ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CircularProgress size={16} sx={{ color: 'white' }} />
                    Converting...
                  </Box>
                ) : (
                  'CONVERT CODE >>>'
                )}
              </Button>

              
            </Box>
            <Box style={{ display: "flex", gap: 6 }}>
              <Button
                variant="outlined"
                onClick={() => {
                  setSourceCode(`// Write your ${selectedLanguage?.label} code here...`);
                  setConvertedCode("// Converted Groovy code will appear here...");
                }}
                sx={{
                  borderColor: '#1565c0',
                  color: '#1565c0',
                  px: 4,
                  py: 1,
                  '&:hover': {
                    borderColor: '#6a39d1',
                    backgroundColor: 'rgba(124, 77, 255, 0.1)'
                  }
                }}
              >
                Clear ✕
              </Button>
              <Button
                variant="contained"
                onClick={handleDownload}
                disabled={!canDownload}
                startIcon={<Download size={20} />}
                sx={{
                  backgroundColor: '#4caf50',
                  px: 4,
                  py: 1,
                  '&:hover': {
                    backgroundColor: '#45a049'
                  },
                  '&:disabled': {
                    backgroundColor: '#e0e0e0',
                    color: '#999'
                  }
                }}
              >
                Download
              </Button>
              <button
                onClick={openCpiUploadModal}
                disabled={!canUploadToCpi}
                style={{
                  padding: "8px 16px",
                  backgroundColor: canUploadToCpi ? "#ff9800" : "#e0e0e0",
                  color: canUploadToCpi ? "white" : "#999",
                  border: "none",
                  borderRadius: "6px",
                  cursor: canUploadToCpi ? "pointer" : "not-allowed",
                  fontSize: "14px",
                  fontWeight: "500",
                  display: "flex",
                  alignItems: "center",
                  gap: "6px",
                  transition: "background-color 0.2s ease",
                }}
                onMouseOver={(e) => {
                  if (canUploadToCpi) {
                    e.target.style.backgroundColor = "#f57c00";
                  }
                }}
                onMouseOut={(e) => {
                  if (canUploadToCpi) {
                    e.target.style.backgroundColor = "#ff9800";
                  }
                }}
              >
                <FaUpload size={14} />
                Upload to CPI
              </button>
            </Box>
          </Box>

          <button
            className="chatbot-toggle-button"
            onClick={() => setChatbotOpen(!chatbotOpen)}
            style={{
              position: 'fixed',
              bottom: '4rem',
              right: '20px',
              zIndex: 1000,
              backgroundColor: '#1565c0',
              color: 'white',
              border: 'none',
              borderRadius: '50%',
              width: '60px',
              height: '60px',
              cursor: 'pointer',
              boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <ChatIcon style={{ fontSize: '30px' }} />
          </button>

          {/* Chatbot component */}
          {chatbotOpen && (
            <div style={{
              border: "1px solid black",
              marginTop: "10rem",
              position: 'fixed',
              bottom: '7rem',
              right: '20px',
              zIndex: 999,
              width: '25%',
              height: '25rem',
              backgroundColor: 'white',
              borderRadius: '10px',
              boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
              overflow: 'hidden'
            }}>
              <ChatbotMain />
            </div>
          )}


          {/* Status Alert */}
          {!selectedLanguage?.enabled && (
            <Alert
              severity="info"
              sx={{
                maxWidth: 600,
                mx: 'auto'
              }}
            >
              <Typography variant="body1">
                <strong>{selectedLanguage?.label}</strong> to Groovy conversion is coming soon!
                Currently only Java conversion is available.
              </Typography>
            </Alert>
          )}
        </Container>
      </Box>
      {/* CPI Upload Modal */}
            {showCpiUploadModal && (
              <div
                style={{
                  position: "fixed",
                  top: "0",
                  left: "0",
                  right: "0",
                  bottom: "0",
                  backgroundColor: "rgba(0, 0, 0, 0.5)",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  zIndex: "1000",
                  padding: "20px",
                }}
              >
                <div
                  style={{
                    backgroundColor: "white",
                    borderRadius: "12px",
                    width: "90%",
                    maxWidth: "600px",
                    maxHeight: "80vh",
                    display: "flex",
                    flexDirection: "column",
                    boxShadow: "0 4px 24px rgba(0, 0, 0, 0.15)",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      padding: "16px 24px",
                      borderBottom: "1px solid #e5e7eb",
                    }}
                  >
                    <h3
                      style={{
                        fontSize: "20px",
                        fontWeight: "600",
                        margin: "0",
                        color: "#1f2937",
                      }}
                    >
                      Upload Groovy Script to CPI
                    </h3>
                    <button
                      onClick={closeCpiUploadModal}
                      style={{
                        background: "none",
                        border: "none",
                        cursor: "pointer",
                        fontSize: "20px",
                        color: "#6b7280",
                        padding: "4px",
                      }}
                    >
                      <FaTimes />
                    </button>
                  </div>
      
                  <form onSubmit={handleCpiUploadSubmit}>
                    <div
                      style={{
                        padding: "24px",
                        flex: "1",
                        overflowY: "auto",
                      }}
                    >
                      {cpiUploadError && (
                        <div
                          style={{
                            backgroundColor: "#fef2f2",
                            border: "1px solid #fecaca",
                            color: "#dc2626",
                            padding: "12px",
                            borderRadius: "8px",
                            marginBottom: "16px",
                            fontSize: "14px",
                          }}
                        >
                          {cpiUploadError}
                        </div>
                      )}
      
                      <div style={{ marginBottom: "16px" }}>
                        <label
                          style={{
                            display: "flex",
                            fontSize: "14px",
                            fontWeight: "500",
                            color: "#374151",
                            marginBottom: "6px",
                          }}
                        >
                          Artifact ID *
                        </label>
                        <input
                          type="text"
                          name="artifactId"
                          value={cpiUploadData.artifactId}
                          onChange={handleCpiInputChange}
                          placeholder="Enter artifact ID"
                          style={{
                            width: "100%",
                            padding: "10px 12px",
                            border: "1px solid #d1d5db",
                            borderRadius: "6px",
                            fontSize: "14px",
                            outline: "none",
                            transition: "border-color 0.2s ease",
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = "#3b82f6";
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = "#d1d5db";
                          }}
                        />
                      </div>
      
                      <div style={{ marginBottom: "16px" }}>
                        <label
                          style={{
                            display: "flex",
                            fontSize: "14px",
                            fontWeight: "500",
                            color: "#374151",
                            marginBottom: "6px",
                          }}
                        >
                          Groovy Script Content *
                        </label>
                        <textarea
                          name="scriptTag"
                          value={cpiUploadData.scriptTag}
                          onChange={handleCpiInputChange}
                          rows={12}
                          style={{
                            width: "100%",
                            padding: "10px 12px",
                            border: "1px solid #d1d5db",
                            borderRadius: "6px",
                            fontSize: "12px",
                            fontFamily: "Monaco, Menlo, 'Ubuntu Mono', monospace",
                            outline: "none",
                            transition: "border-color 0.2s ease",
                            resize: "vertical",
                            minHeight: "200px",
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = "#3b82f6";
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = "#d1d5db";
                          }}
                          placeholder="Converted Groovy script will appear here..."
                        />
                        <div
                          style={{
                            fontSize: "12px",
                            color: "#6b7280",
                            marginTop: "4px",
                          }}
                        >
                          This script will be uploaded as a .groovy file to your CPI artifact
                        </div>
                      </div>
                    </div>
      
                    <div
                      style={{
                        padding: "16px 24px",
                        borderTop: "1px solid #e5e7eb",
                        display: "flex",
                        justifyContent: "flex-end",
                        gap: "12px",
                      }}
                    >
                      <button
                        type="button"
                        onClick={closeCpiUploadModal}
                        style={{
                          padding: "10px 16px",
                          backgroundColor: "#f3f4f6",
                          color: "#374151",
                          border: "none",
                          borderRadius: "6px",
                          cursor: "pointer",
                          fontSize: "14px",
                          fontWeight: "500",
                        }}
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={cpiUploadLoading}
                        style={{
                          padding: "10px 16px",
                          backgroundColor: cpiUploadLoading ? "#9ca3af" : "#ff9800",
                          color: "white",
                          border: "none",
                          borderRadius: "6px",
                          cursor: cpiUploadLoading ? "not-allowed" : "pointer",
                          fontSize: "14px",
                          fontWeight: "500",
                          display: "flex",
                          alignItems: "center",
                          gap: "6px",
                        }}
                      >
                        {cpiUploadLoading ? (
                          <>
                            <div
                              style={{
                                width: "14px",
                                height: "14px",
                                border: "2px solid transparent",
                                borderTop: "2px solid white",
                                borderRadius: "50%",
                                animation: "spin 1s linear infinite",
                              }}
                            />
                            Uploading...
                          </>
                        ) : (
                          <>
                            <FaUpload size={14} />
                            Upload to CPI
                          </>
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}
            </div>
  );
};

export default GroovyConvertor;