/* Additional styles for Multiple Interface Results Table */
.multiple-interface-table-container {
    width: 100%;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    margin: 20px 0;
  }
  
  .interface-section {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 30px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  
  .interface-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    alignItems: center;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.3s ease;
  }
  
  .interface-header:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  }
  
  .interface-header h3 {
    margin: 0 0 8px 0;
    color: #007bff;
    font-size: 18px;
    font-weight: 600;
  }
  
  .interface-summary {
    display: flex;
    gap: 20px;
    font-size: 14px;
    color: #6c757d;
    margin-top: 5px;
  }
  
  .interface-summary span {
    padding: 2px 8px;
    border-radius: 4px;
    background-color: rgba(255,255,255,0.8);
    font-weight: 500;
  }
  
  .interface-results {
    background-color: white;
    overflow-x: auto;
  }
  
  .interface-results .comparison-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
  }
  
  .interface-results .comparison-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 12px 8px;
    border-bottom: 2px solid #dee2e6;
    text-align: left;
    font-size: 13px;
    position: sticky;
    top: 0;
    z-index: 10;
  }
  
  .interface-results .comparison-table td {
    padding: 10px 8px;
    border-bottom: 1px solid #e9ecef;
    font-size: 12px;
    vertical-align: middle;
  }
  
  .interface-results .comparison-table tbody tr:hover {
    background-color: #f8f9fa;
  }
  
  .interface-results .comparison-table tbody tr.Passed {
    border-left: 3px solid #28a745;
  }
  
  .interface-results .comparison-table tbody tr.Failed {
    border-left: 3px solid #dc3545;
  }
  
  /* Status badges for multiple interfaces */
  .status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
 .status-match {
  color: #4CAF50; /* Green */
  background-color: #fefefe;
}

.status-difference {
  color: rgb(237, 16, 16);
  background-color: #fefefe; /* Red */
}

.status-cannot-process {
  color: #FF9800; /* Orange */
  background-color: #fefefe;
}
  
  .status-unknown {
    /* background-color: #e2e3e5; */
    color: #383d41;
    /* border: 1px solid #d6d8db; */
  }
  
  /* View payload buttons for multiple interfaces */
  .interface-results .view-payload-button {
    /* background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white; */
    color:black;
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
  }
  
  .interface-results .view-payload-button:hover {
    /* background: linear-gradient(135deg, #0056b3 0%, #004085 100%); */
    color:blue;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
  }
  
  /* Details buttons for multiple interfaces */
  .interface-results .details-button {
    /* background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); */
    /* color: white; */
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(2, 124, 255, 0.3);
    
  }
  
  .interface-results .details-button:hover:not(.disabled) {
    background: linear-gradient(135deg, #6f789c 0%, #6eb3db 100%);
    color:white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(40,167,69,0.3);
  }
  
  .interface-results .details-button.disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  /* Error view buttons for multiple interfaces */
  .interface-results .error-view-button {
    background: linear-gradient(135deg, #415edf 0%, #9e4b6f 100%);
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  
  .interface-results .error-view-button:hover:not(.disabled) {
    background: linear-gradient(135deg, #a71e2a 0%, #721c24 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220,53,69,0.3);
  }
  
  .interface-results .error-view-button.disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  /* Expand/collapse animation */
  .interface-header div:last-child {
    font-size: 18px;
    transform: rotate(0deg);
    transition: transform 0.3s ease;
    color: #007bff;
    font-weight: bold;
  }
  
  /* Summary stats styling */
  .summary-stats {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .summary-stats > div {
    text-align: center;
    padding: 10px;
    min-width: 120px;
  }
  
  .summary-stats strong {
    font-size: 28px;
    font-weight: 700;
    display: block;
    margin-bottom: 5px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
  }
  
  .summary-stats p {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  /* Responsive design */
  @media (max-width: 1200px) {
    .interface-results {
      overflow-x: auto;
    }
    
    .interface-results .comparison-table {
      min-width: 1200px;
    }
  }
  
  @media (max-width: 768px) {
    .summary-stats {
      flex-direction: column;
      gap: 10px;
    }
    
    .summary-stats > div {
      min-width: auto;
    }
    
    .interface-header {
      padding: 12px 15px;
    }
    
    .interface-header h3 {
      font-size: 16px;
    }
    
    .interface-summary {
      flex-direction: column;
      gap: 8px;
    }
  }