// import React, { useState, useEffect } from "react";
// import { useNavigate } from "react-router-dom";
// import CPIFormSection from "../CPIFormSection";
// import TriggerFlowModal from "../TriggerFlowModal";
// import ConfigureSchedulerModal from "../ConfigureSchedulerModal";
// import "../../App.css";
// import APIEndpoint from "../../config";
// import NEOFormSection from "./NEOFormSection"; // Changed from POMultipleFormSection to NEOFormSection
// import ConfigureIDOC from "../ConfigureIDOC";
// import ConfigureHTTP from "../ConfigureHTTP";
// import ConfigureSOAP from "../ConfigureSOAP";

// const NeoSingleTestCaseForm = ({ formData, setFormData, onBack, onProceed, onGenerateReport, serviceInterfaces }) => {
//   const navigate = useNavigate();
//   const [deployMessage, setDeployMessage] = useState(null);
//   const [isDeploying, setIsDeploying] = useState(false);
//   const [showMessage, setShowMessage] = useState(false);
//   const [showTriggerModal, setShowTriggerModal] = useState(false);
//   const [showConfigureModal, setShowConfigureModal] = useState(false);
//   const [showIDOCModal, setShowIDOCModal] = useState(false);
//   const [showHTTPModal, setShowHTTPModal] = useState(false);
//   const [showSOAPModal, setShowSOAPModal] = useState(false);

//   const [isProceeding, setIsProceeding] = useState(false);
//   const [isGeneratingReport, setIsGeneratingReport] = useState(false);

//   const isConfigureEnabled =
//     formData.adapterName &&
//     (formData.adapterName === "SFTP" ||
//       formData.adapterName === "IDOC" ||
//       formData.adapterName === "HTTPS" ||
//       formData.adapterName === "SOAP");

//   const handleProceedClick = async () => {
//     setIsProceeding(true);
//     try {
//       await onProceed();
//     } finally {
//       setIsProceeding(false);
//     }
//   };

//   const handleGenerateReportClick = async () => {
//     setIsGeneratingReport(true);
//     try {
//       await onGenerateReport();
//     } finally {
//       setIsGeneratingReport(false);
//     }
//   };

//   // Button styles matching Dashboard component
//   const getButtonStyle = (type = "default", disabled = false) => {
//     const baseStyle = {
//       backgroundColor: disabled ? "#f3f4f6" : "white",
//       color: disabled ? "#9ca3af" : "#374151",
//       borderRadius: "8px",
//       fontWeight: "500",
//       fontSize: "14px",
//       border: "1px solid #e5e7eb",
//       padding: "12px 16px",
//       cursor: disabled ? "not-allowed" : "pointer",
//       transition: "all 0.3s ease",
//       boxShadow: disabled ? "0 1px 2px rgba(0, 0, 0, 0.05)" : "0 1px 3px rgba(0, 0, 0, 0.1)",
//       textAlign: "center",
//       minWidth: "120px"
//     };

//     // Specific styles for different button types
//     switch (type) {
//       case "back":
//         return {
//           ...baseStyle,
//           backgroundColor: "#f8fafc",
//           borderColor: "#cbd5e1",
//           color: "#475569"
//         };
//       case "proceed":
//         return {
//           ...baseStyle,
//           backgroundColor: disabled ? "#f3f4f6" : "#e5e7eb",
//           fontWeight: "600"
//         };
//       case "generate":
//         return {
//           ...baseStyle,
//           backgroundColor: disabled ? "#f3f4f6" : "#dcfce7",
//           borderColor: disabled ? "#e5e7eb" : "#16a34a",
//           color: disabled ? "#9ca3af" : "#15803d",
//           fontWeight: "600"
//         };
//       default:
//         return baseStyle;
//     }
//   };

//   const containerStyle = {
//     display: "flex",
//     justifyContent: "space-between",
//     alignItems: "center",
//     marginTop: "20px",
//     padding: "20px 0"
//   };

//   const buttonGroupStyle = {
//     display: "flex",
//     gap: "10px",
//     alignItems: "center"
//   };

//   useEffect(() => {
//     if (deployMessage) {
//       setShowMessage(true);
//       const timer = setTimeout(() => {
//         setShowMessage(false);
//         setDeployMessage(null);
//       }, 3000);
//       return () => clearTimeout(timer);
//     }
//   }, [deployMessage]);

//   const onConfigure = () => {
//     if (!formData.iFlowName) {
//       alert("Please select an I-Flow Name before configuring");
//       return;
//     }
//     if (formData.adapterName === "SFTP") {
//       setShowConfigureModal(true);
//     } else if (formData.adapterName === "IDOC") {
//       setShowIDOCModal(true);
//     } else if (formData.adapterName === "HTTPS") {
//       setShowHTTPModal(true);
//     } else if (formData.adapterName === "SOAP") {
//       setShowSOAPModal(true);
//     }
//   };

//   const onTriggerFlow = () => {
//     if (!formData.iFlowName) {
//       alert("Please select an I-Flow Name before triggering the flow");
//       return;
//     }
//     if (!formData.startTime || !formData.endTime) {
//       alert("Please set both Start Time and End Time before triggering the flow");
//       return;
//     }
//     setShowTriggerModal(true);
//   };

//   const handleDeploy = async () => {
//     if (!formData.iFlowName || !formData.tenant) {
//       alert("Please select both I-Flow Name and Tenant before deploying");
//       return;
//     }

//     setIsDeploying(true);
//     setDeployMessage(null);

//     try {
//       const apiTenantName = formData.tenant.charAt(0).toUpperCase() + formData.tenant.slice(1).toLowerCase();
//       const response = await fetch(
//         `${APIEndpoint}/api/deploy?artifactId=${encodeURIComponent(formData.iFlowName)}&configName=${encodeURIComponent(apiTenantName)}`,
//         {
//           method: 'POST'
//         }
//       );

//       if (!response.ok) {
//         throw new Error("Failed to initiate deployment");
//       }

//       const data = await response.json();
//       setDeployMessage("Deployment started successfully! Your artifact is being processed.");
//     } catch (error) {
//       console.error("Deployment error:", error);
//       setDeployMessage("Deployment failed. Please try again.");
//     } finally {
//       setIsDeploying(false);
//     }
//   };

//   return (
//     <div style={{
//     marginLeft: '20px',
//     marginRight: '20px',
//     padding: '20px',
//     backgroundColor: '#fff',
//     borderRadius: '8px',
//     boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
//   }}>
//     <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
//     <div style={{
//     marginBottom: '20px',
//     padding: '20px',
//     // backgroundColor: '#fff',
//     borderRadius: '8px',
//     boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
//     width: '60rem',
//     maxWidth: '500px',
//     display: 'flex',
//     flexDirection: 'row',
//     alignItems: 'center'
//   }}>
//     <h4 style={{ marginBottom: '15px', color: '#272D4F' ,  width: "30rem"}}>Upload the Input Payload : </h4>
//     <input
//       type="file"
//       accept=".xml"
//       onChange={(event) => {
//         const file = event.target.files[0];
//         if (file) {
//           const reader = new FileReader();
//           reader.onload = (e) => {
//             setFormData(prev => ({
//               ...prev,
//               neoInputPayload: e.target.result
//             }));
//           };
//           reader.readAsText(file);
//         }
//       }}
//       style={{
//         padding: '10px',
//         border: '1px solid #ddd',
//         borderRadius: '4px',
//         width: '100%'
//       }}
//     />
//     {formData.neoInputPayload && (
//       <div style={{
//         marginTop: '10px',
//         padding: '10px',
//         backgroundColor: '#f0f8ff',
//         borderRadius: '4px',
//         fontSize: '14px'
//       }}>
//         File uploaded successfully!
//       </div>
//     )}
//   </div></div>

//       <div className="single-testcase-content">
//         <NEOFormSection formData={formData} setFormData={setFormData} /> {/* Changed to NEOFormSection */}
//         <CPIFormSection
//           formData={formData}
//           setFormData={setFormData}
//         />
//       </div>

//       {showMessage && (
//         <div style={{
//           margin: "10px 0",
//           padding: "15px",
//           backgroundColor: deployMessage.includes("failed") ? "#ffebee" : "#e8f5e9",
//           border: deployMessage.includes("failed") ? "1px solid #ef9a9a" : "1px solid #a5d6a7",
//           borderRadius: "8px",
//           color: deployMessage.includes("failed") ? "#c62828" : "#2e7d32",
//           fontWeight: "500",
//           textAlign: "center"
//         }}>
//           {deployMessage}
//         </div>
//       )}

//       <div style={containerStyle}>
//         <div>
//           <button
//             onClick={onBack}
//             style={getButtonStyle("back")}
//             onMouseEnter={(e) => {
//               e.target.style.backgroundColor = "#e2e8f0";
//             }}
//             onMouseLeave={(e) => {
//               e.target.style.backgroundColor = "#f8fafc";
//             }}
//           >
//             ← Back to System Selection
//           </button>
//         </div>

//         <div style={buttonGroupStyle}>
//           <button
//             style={getButtonStyle("default", !isConfigureEnabled)}
//             onClick={isConfigureEnabled ? onConfigure : undefined}
//             disabled={!isConfigureEnabled}
//             onMouseEnter={(e) => {
//               if (isConfigureEnabled) {
//                 e.target.style.backgroundColor = "#f9fafb";
//               }
//             }}
//             onMouseLeave={(e) => {
//               if (isConfigureEnabled) {
//                 e.target.style.backgroundColor = "white";
//               }
//             }}
//           >
//             Configure
//           </button>

//           <button
//             style={getButtonStyle("default", isDeploying)}
//             onClick={handleDeploy}
//             disabled={isDeploying}
//             onMouseEnter={(e) => {
//               if (!isDeploying) {
//                 e.target.style.backgroundColor = "#f9fafb";
//               }
//             }}
//             onMouseLeave={(e) => {
//               if (!isDeploying) {
//                 e.target.style.backgroundColor = "white";
//               }
//             }}
//           >
//             {isDeploying ? "Deploying..." : "Deploy"}
//           </button>

//           <button
//             style={getButtonStyle("proceed", isProceeding)}
//             onClick={handleProceedClick}
//             disabled={isProceeding}
//             onMouseEnter={(e) => {
//               if (!isProceeding) {
//                 e.target.style.backgroundColor = "#d1d5db";
//               }
//             }}
//             onMouseLeave={(e) => {
//               if (!isProceeding) {
//                 e.target.style.backgroundColor = "#e5e7eb";
//               }
//             }}
//           >
//             {isProceeding ? (
//               <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
//                 <svg className="spinner" viewBox="0 0 50 50" style={{ width: '20px', height: '20px', marginRight: '8px' }}>
//                   <circle className="path" cx="25" cy="25" r="20" fill="none" strokeWidth="5"></circle>
//                 </svg>
//                 Processing...
//               </span>
//             ) : (
//               "Extract Data →"
//             )}
//           </button>

//           <button
//             style={getButtonStyle("generate", isGeneratingReport)}
//             onClick={handleGenerateReportClick}
//             disabled={isGeneratingReport}
//             onMouseEnter={(e) => {
//               if (!isGeneratingReport) {
//                 e.target.style.backgroundColor = "#bbf7d0";
//               }
//             }}
//             onMouseLeave={(e) => {
//               if (!isGeneratingReport) {
//                 e.target.style.backgroundColor = "#dcfce7";
//               }
//             }}
//           >
//             {isGeneratingReport ? (
//               <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
//                 <svg className="spinner" viewBox="0 0 50 50" style={{ width: '20px', height: '20px', marginRight: '8px' }}>
//                   <circle className="path" cx="25" cy="25" r="20" fill="none" strokeWidth="5"></circle>
//                 </svg>
//                 Generating...
//               </span>
//             ) : (
//               "📊 Generate Report"
//             )}
//           </button>
//         </div>
//       </div>

//       <TriggerFlowModal
//         isOpen={showTriggerModal}
//         onClose={() => setShowTriggerModal(false)}
//         iFlowName={formData.iFlowName}
//         startTime={formData.startTime}
//         endTime={formData.endTime}
//       />

//       {formData.adapterName === "SFTP" && (
//         <ConfigureSchedulerModal
//           isOpen={showConfigureModal}
//           onClose={() => setShowConfigureModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}

//       {formData.adapterName === "IDOC" && (
//         <ConfigureIDOC
//           isOpen={showIDOCModal}
//           onClose={() => setShowIDOCModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}

//       {formData.adapterName === "HTTPS" && (
//         <ConfigureHTTP
//           isOpen={showHTTPModal}
//           onClose={() => setShowHTTPModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}

//       {formData.adapterName === "SOAP" && (
//         <ConfigureSOAP
//           isOpen={showSOAPModal}
//           onClose={() => setShowSOAPModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}

//       <style jsx>{`
//         .spinner {
//           animation: rotate 2s linear infinite;
//         }
//         .spinner .path {
//           stroke: #374151;
//           stroke-linecap: round;
//           animation: dash 1.5s ease-in-out infinite;
//         }
//         @keyframes rotate {
//           100% {
//             transform: rotate(360deg);
//           }
//         }
//         @keyframes dash {
//           0% {
//             stroke-dasharray: 1, 150;
//             stroke-dashoffset: 0;
//           }
//           50% {
//             stroke-dasharray: 90, 150;
//             stroke-dashoffset: -35;
//           }
//           100% {
//             stroke-dasharray: 90, 150;
//             stroke-dashoffset: -124;
//           }
//         }
//       `}</style>
//     </div>
//   );
// };

// export default NeoSingleTestCaseForm;

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
// import CPIFormSection from "../CPIFormSection";
import TriggerFlowModal from "../TriggerFlowModal";
import ConfigureSchedulerModal from "../ConfigureSchedulerModal";
import "../../App.css";
import APIEndpoint from "../../config";
import NEOFormSection from "./NEOFormSection";
import ConfigureIDOC from "../ConfigureIDOC";
import ConfigureHTTP from "../ConfigureHTTP";
import ConfigureSOAP from "../ConfigureSOAP";
import {
  Card,
  Typography,
  Button,
  Box,
  Grid,
  Paper,
  LinearProgress,
  Alert,
  Snackbar,
} from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline";
import AssessmentIcon from "@mui/icons-material/Assessment";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CPINeoFormSection from "./CPINeoFormSection";

const NeoSingleTestCaseForm = ({
  formData,
  setFormData,
  onBack,
  onProceed,
  onGenerateReport,
  serviceInterfaces,
}) => {
  const navigate = useNavigate();
  const [deployMessage, setDeployMessage] = useState(null);
  const [isDeploying, setIsDeploying] = useState(false);
  const [showMessage, setShowMessage] = useState(false);
  const [showTriggerModal, setShowTriggerModal] = useState(false);
  const [showConfigureModal, setShowConfigureModal] = useState(false);
  const [showIDOCModal, setShowIDOCModal] = useState(false);
  const [showHTTPModal, setShowHTTPModal] = useState(false);
  const [showSOAPModal, setShowSOAPModal] = useState(false);
  const [isProceeding, setIsProceeding] = useState(false);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  const handleProceedClick = async () => {
    setIsProceeding(true);
    try {
      await onProceed();
    } finally {
      setIsProceeding(false);
    }
  };

  const handleGenerateReportClick = async () => {
    setIsGeneratingReport(true);
    try {
      await onGenerateReport();
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const handleCloseSnackbar = () => {
    setShowMessage(false);
    setDeployMessage(null);
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Store the file object for API call
      setFormData((prev) => ({
        ...prev,
        neoInputPayload: file,
      }));

      // Optionally read content for preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setFormData((prev) => ({
          ...prev,
          neoInputPayloadContent: e.target.result,
        }));
      };
      reader.readAsText(file);
    }
  };

  // const onConfigure = () => {
  //   if (!formData.iFlowName) {
  //     setDeployMessage({ text: "Please select an I-Flow Name before configuring", severity: "error" });
  //     setShowMessage(true);
  //     return;
  //   }
  //   if (formData.adapterName === "SFTP") {
  //     setShowConfigureModal(true);
  //   } else if (formData.adapterName === "IDOC") {
  //     setShowIDOCModal(true);
  //   } else if (formData.adapterName === "HTTPS") {
  //     setShowHTTPModal(true);
  //   } else if (formData.adapterName === "SOAP") {
  //     setShowSOAPModal(true);
  //   }
  // };

  // const onTriggerFlow = () => {
  //   if (!formData.iFlowName) {
  //     setDeployMessage({ text: "Please select an I-Flow Name before triggering the flow", severity: "error" });
  //     setShowMessage(true);
  //     return;
  //   }
  //   if (!formData.startTime || !formData.endTime) {
  //     setDeployMessage({ text: "Please set both Start Time and End Time before triggering the flow", severity: "error" });
  //     setShowMessage(true);
  //     return;
  //   }
  //   setShowTriggerModal(true);
  // };

  return (
    <Box
      sx={{
        p: 3,
        backgroundColor: "#f5f7fa",
        minHeight: "100vh",
      }}
    >
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card sx={{ p: 1, mb: 1 }}>
              <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      color: "#4a5568",
                      fontWeight: "500",
                    }}
                  >
                    Upload Input Payload To Be Tested:
                  </Typography>
                </Grid>
                <Grid item xs={12} md={8}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<CloudUploadIcon />}
                      sx={{
                        width: "18rem",
                        flex: 1,
                        py: 1.5,
                        borderColor: "#cbd5e0",
                        "&:hover": {
                          borderColor: "#a0aec0",
                        },
                      }}
                    >
                      Select XML File
                      <input
                        type="file"
                        accept=".xml"
                        hidden
                        onChange={handleFileChange} // Use the new handler
                      />
                    </Button>
                    {formData.neoInputPayload && (
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          color: "#38a169",
                          fontSize: "0.875rem",
                        }}
                      >
                        <CheckCircleOutlineIcon
                          fontSize="small"
                          sx={{ mr: 1 }}
                        />
                        File uploaded: {formData.neoInputPayload.name}
                      </Box>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: "flex", flexDirection: "row", gap: 3 }}>
          <NEOFormSection formData={formData} setFormData={setFormData} />
          <CPINeoFormSection formData={formData} setFormData={setFormData} />
        </Box>

        <Snackbar
          open={showMessage}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "top", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={deployMessage?.severity || "info"}
            sx={{ width: "100%" }}
          >
            {deployMessage?.text}
          </Alert>
        </Snackbar>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mt: 4,
            pt: 3,
            borderTop: "1px solid #e2e8f0",
          }}
        >
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={onBack}
            sx={{
              px: 3,
              py: 1.5,
              color: "#4a5568",
              borderColor: "#cbd5e0",
              "&:hover": {
                backgroundColor: "#edf2f7",
                borderColor: "#a0aec0",
              },
            }}
          >
            Back to Connection
          </Button>

          <Box sx={{ display: "flex", gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<PlayCircleOutlineIcon />}
              onClick={handleProceedClick}
              disabled={isProceeding}
              sx={{
                px: 3,
                py: 1.5,
                backgroundColor: "#3182ce",
                "&:hover": {
                  backgroundColor: "#2c5282",
                },
                "&:disabled": {
                  backgroundColor: "#e2e8f0",
                  color: "#a0aec0",
                },
              }}
            >
              {isProceeding ? "Processing..." : "Extract Data"}
            </Button>

            <Button
              variant="contained"
              startIcon={<AssessmentIcon />}
              onClick={handleGenerateReportClick}
              disabled={isGeneratingReport}
              sx={{
                px: 3,
                py: 1.5,
                backgroundColor: "#38a169",
                "&:hover": {
                  backgroundColor: "#2f855a",
                },
                "&:disabled": {
                  backgroundColor: "#e2e8f0",
                  color: "#a0aec0",
                },
              }}
            >
              {isGeneratingReport ? "Generating..." : "Generate Report"}
            </Button>
          </Box>
        </Box>
      </Paper>

      <TriggerFlowModal
        isOpen={showTriggerModal}
        onClose={() => setShowTriggerModal(false)}
        iFlowName={formData.iFlowName}
        startTime={formData.startTime}
        endTime={formData.endTime}
      />

      {formData.adapterName === "SFTP" && (
        <ConfigureSchedulerModal
          isOpen={showConfigureModal}
          onClose={() => setShowConfigureModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "IDOC" && (
        <ConfigureIDOC
          isOpen={showIDOCModal}
          onClose={() => setShowIDOCModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "HTTPS" && (
        <ConfigureHTTP
          isOpen={showHTTPModal}
          onClose={() => setShowHTTPModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "SOAP" && (
        <ConfigureSOAP
          isOpen={showSOAPModal}
          onClose={() => setShowSOAPModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}
    </Box>
  );
};

export default NeoSingleTestCaseForm;
