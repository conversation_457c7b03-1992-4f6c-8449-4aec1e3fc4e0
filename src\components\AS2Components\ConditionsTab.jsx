import React, { useState } from "react";
import { MenuItem, Select, FormControl, Checkbox } from "@mui/material";
import API_ENDPOINT from "../../config";

const ConditionsTab = ({ onClose, setSnackbar, artifactId }) => {
  const [conditionsData, setConditionsData] = useState({
    conditionEnabled: { param: "", value: false },
    conditionType: { param: "", value: "File Size" },
    conditionOperator: { param: "", value: ">" },
    conditionValue: { param: "", value: "" },
    conditionAction: { param: "", value: "Reject" },
    bodySize: { param: "", value: "40" },
    attachmentsSize: { param: "", value: "100" }
  });
  const [loading, setLoading] = useState(false);

  const handleConditionsChange = (e) => {
    const { name, value, type, checked } = e.target;
    setConditionsData((prev) => ({
      ...prev,
      [name]: { ...prev[name], value: type === 'checkbox' ? checked : value },
    }));
  };

  const handleParamChange = (e) => {
    const { name, value } = e.target;
    setConditionsData((prev) => ({
      ...prev,
      [name]: { ...prev[name], param: value },
    }));
  };

  const handleCancel = () => {
    if (onClose) onClose();
  };

  const handleSubmit = async () => {
    setLoading(true);
    
    try {
      // Create an array of all parameter updates
      const updates = [
        { name: conditionsData.conditionEnabled.param, value: conditionsData.conditionEnabled.value },
        { name: conditionsData.bodySize.param, value: conditionsData.bodySize.value },
        { name: conditionsData.attachmentsSize.param, value: conditionsData.attachmentsSize.value },
        ...(conditionsData.conditionEnabled.value ? [
          { name: conditionsData.conditionType.param, value: conditionsData.conditionType.value },
          { name: conditionsData.conditionOperator.param, value: conditionsData.conditionOperator.value },
          { name: conditionsData.conditionValue.param, value: conditionsData.conditionValue.value },
          { name: conditionsData.conditionAction.param, value: conditionsData.conditionAction.value }
        ] : [])
      ].filter(update => update.name && (update.value !== "" && update.value !== undefined)); // Only include parameters with names and values

      if (updates.length === 0) {
        setSnackbar({
          open: true,
          message: "Please enter at least one parameter name and value",
          severity: "error"
        });
        setLoading(false);
        return;
      }

      // Execute all updates sequentially
      let lastResponse = null;
      for (const update of updates) {
        const endpoint = `${API_ENDPOINT}/api/updateParam/${artifactId}/${update.name}`;
        
        const response = await fetch(endpoint, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            parameterValue: update.value,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update parameter ${update.name}`);
        }
        
        // Store the last successful response
        lastResponse = await response.json();
      }

      // Show the success message from the API response
      if (lastResponse && lastResponse.status) {
        setSnackbar({
          open: true,
          message: lastResponse.status,
          severity: "success"
        });
      } else {
        setSnackbar({
          open: true,
          message: "Conditions parameters updated successfully",
          severity: "success"
        });
      }
    } catch (error) {
      console.error("Error updating conditions parameters:", error);
      setSnackbar({
        open: true,
        message: error.message || "Failed to update some conditions parameters",
        severity: "error"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', paddingBottom: '80px' }}>
      {/* MAXIMUM MESSAGE SIZE Section */}
      <h4 style={{ 
        color: '#6c757d', 
        fontSize: '12px', 
        fontWeight: 'bold',
        marginBottom: '20px',
        textTransform: 'uppercase',
        letterSpacing: '0.5px',
        margin: '0 0 20px 0'
      }}>
        MAXIMUM MESSAGE SIZE
      </h4>
      
      <div style={{ marginBottom: '40px' }}>
        {/* Body Size */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: '150px 1fr 100px', 
          gap: '10px', 
          alignItems: 'center',
          marginBottom: '15px'
        }}>
          <label style={{ color: '#333', fontSize: '14px' }}>
            Body Size (in MB):
          </label>
          <input
            type="text"
            name="bodySize"
            value={conditionsData.bodySize.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <input
            type="text"
            name="bodySize"
            value={conditionsData.bodySize.value}
            onChange={handleConditionsChange}
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
        </div>

        {/* Attachments Size */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: '150px 1fr 100px', 
          gap: '10px', 
          alignItems: 'center',
          marginBottom: '15px'
        }}>
          <label style={{ color: '#333', fontSize: '14px' }}>
            Attachments Size (in MB):
          </label>
          <input
            type="text"
            name="attachmentsSize"
            value={conditionsData.attachmentsSize.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <input
            type="text"
            name="attachmentsSize"
            value={conditionsData.attachmentsSize.value}
            onChange={handleConditionsChange}
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
        </div>
      </div>

      {/* Bottom Action Bar */}
      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '10px',
        padding: '15px 20px',
        borderTop: '1px solid #dee2e6',
        backgroundColor: '#f8f9fa',
        marginTop: '20px'
      }}>
        <button
          onClick={handleSubmit}
          disabled={loading}
          style={{
            padding: '8px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer',
            fontWeight: 'bold',
            opacity: loading ? 0.7 : 1
          }}
        >
          {loading ? 'Updating...' : 'OK'}
        </button>
        <button
          onClick={handleCancel}
          style={{
            padding: '8px 20px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default ConditionsTab;