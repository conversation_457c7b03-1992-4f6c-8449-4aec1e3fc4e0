import React, { useState } from "react";
import { MenuItem, Select, FormControl, Checkbox } from "@mui/material";

const DeliveryAssuranceTab = ({ onClose, setSnackbar, artifactId }) => {
  const [deliveryData, setDeliveryData] = useState({
    qualityOfService: { param: "", value: "Exactly Once" },
    retryInterval: { param: "", value: "1" },
    exponentialBackoff: { param: "", value: true },
    maximumRetryInterval: { param: "", value: "60" },
    deadLetterQueue: { param: "", value: true },
    encryptMessageDuringPersistence: { param: "", value: true },
  });
  const [loading, setLoading] = useState(false);

  const handleDeliveryChange = (e) => {
    const { name, value, type, checked } = e.target;
    setDeliveryData((prev) => ({
      ...prev,
      [name]: {
        ...prev[name],
        value: type === "checkbox" ? checked : value,
      },
    }));
  };

  const handleParamChange = (e) => {
    const { name, value } = e.target;
    setDeliveryData((prev) => ({
      ...prev,
      [name]: { ...prev[name], param: value },
    }));
  };

  const handleCancel = () => {
    if (onClose) onClose();
  };
  // Add this function inside your component (before the return statement)
  const updateParameters = async (updates, artifactId, setSnackbar) => {
    try {
      let lastResponse = null;
      for (const update of updates) {
        const endpoint = `${API_ENDPOINT}/api/updateParam/${artifactId}/${update.name}`;

        const response = await fetch(endpoint, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            parameterValue: update.value.toString(), // Ensure value is string
          }),
        });

        if (!response.ok) {
          const errorData = await response.text();
          throw new Error(
            `Failed to update parameter ${update.name}: ${errorData}`
          );
        }

        lastResponse = await response.json();
      }

      setSnackbar({
        open: true,
        message: lastResponse?.status || "Parameters updated successfully",
        severity: "success",
      });
      return true;
    } catch (error) {
      console.error("Error updating parameters:", error);
      setSnackbar({
        open: true,
        message: error.message || "Failed to update some parameters",
        severity: "error",
      });
      return false;
    }
  };

  // Then modify your handleSubmit to include validation:
  const handleSubmit = async () => {
    // Validate required fields
    if (!deliveryData.qualityOfService.param) {
      setSnackbar({
        open: true,
        message: "Quality of Service parameter name is required",
        severity: "error",
      });
      return;
    }

    if (deliveryData.qualityOfService.value === "Exactly Once") {
      if (
        !deliveryData.retryInterval.param ||
        !deliveryData.maximumRetryInterval.param
      ) {
        setSnackbar({
          open: true,
          message:
            "Retry Interval and Maximum Retry Interval parameter names are required when using Exactly Once",
          severity: "error",
        });
        return;
      }
    }

    setLoading(true);

    const updates = [
      {
        name: deliveryData.qualityOfService.param,
        value: deliveryData.qualityOfService.value,
      },
      ...(deliveryData.qualityOfService.value === "Exactly Once"
        ? [
            {
              name: deliveryData.retryInterval.param,
              value: deliveryData.retryInterval.value,
            },
            {
              name: deliveryData.exponentialBackoff.param,
              value: deliveryData.exponentialBackoff.value.toString(),
            },
            {
              name: deliveryData.maximumRetryInterval.param,
              value: deliveryData.maximumRetryInterval.value,
            },
            {
              name: deliveryData.deadLetterQueue.param,
              value: deliveryData.deadLetterQueue.value.toString(),
            },
            {
              name: deliveryData.encryptMessageDuringPersistence.param,
              value:
                deliveryData.encryptMessageDuringPersistence.value.toString(),
            },
          ]
        : []),
    ].filter((update) => update.name);

    const success = await updateParameters(updates, artifactId, setSnackbar);

    setLoading(false);
    if (success) onClose?.();
  };

  return (
    <div
      style={{
        padding: "20px",
        fontFamily: "Arial, sans-serif",
        paddingBottom: "80px",
      }}
    >
      <h3
        style={{
          color: "#6c757d",
          fontSize: "12px",
          fontWeight: "bold",
          marginBottom: "16px",
          textTransform: "uppercase",
          letterSpacing: "0.5px",
        }}
      >
        DELIVERY ASSURANCE PARAMETERS
      </h3>

      <div style={{ display: "grid", gap: "16px" }}>
        {/* Quality Of Service */}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "150px 1fr 200px",
            gap: "10px",
            alignItems: "center",
          }}
        >
          <label
            style={{
              color: "#333",
              fontSize: "14px",
            }}
          >
            Quality Of Service: <span style={{ color: "red" }}>*</span>
          </label>
          <input
            type="text"
            name="qualityOfService"
            value={deliveryData.qualityOfService.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: "8px 12px",
              border: "1px solid #ccc",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          />
          <FormControl size="small">
            <Select
              name="qualityOfService"
              value={deliveryData.qualityOfService.value}
              onChange={handleDeliveryChange}
              style={{ fontSize: "14px" }}
            >
              <MenuItem value="Exactly Once">Exactly Once</MenuItem>
              <MenuItem value="Best Effort">Best Effort</MenuItem>
            </Select>
          </FormControl>
        </div>

        {/* Conditional fields - only show when "Exactly Once" is selected */}
        {deliveryData.qualityOfService.value === "Exactly Once" && (
          <>
            {/* Retry Interval */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "150px 1fr 200px",
                gap: "10px",
                alignItems: "center",
              }}
            >
              <label
                style={{
                  color: "#333",
                  fontSize: "14px",
                }}
              >
                Retry Interval (in min): <span style={{ color: "red" }}>*</span>
              </label>
              <input
                type="text"
                name="retryInterval"
                value={deliveryData.retryInterval.param}
                onChange={handleParamChange}
                placeholder="Define Parameter"
                style={{
                  padding: "8px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "14px",
                }}
              />
              <input
                type="text"
                name="retryInterval"
                value={deliveryData.retryInterval.value}
                onChange={handleDeliveryChange}
                style={{
                  padding: "8px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "14px",
                }}
              />
            </div>

            {/* Exponential Backoff */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "150px 1fr 200px",
                gap: "10px",
                alignItems: "center",
              }}
            >
              <label
                style={{
                  color: "#333",
                  fontSize: "14px",
                }}
              >
                Exponential Backoff: <span style={{ color: "red" }}>*</span>
              </label>
              <input
                type="text"
                name="exponentialBackoff"
                value={deliveryData.exponentialBackoff.param}
                onChange={handleParamChange}
                placeholder="Define Parameter"
                style={{
                  padding: "8px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "14px",
                }}
              />
              <div style={{ display: "flex", justifyContent: "center" }}>
                <Checkbox
                  name="exponentialBackoff"
                  checked={deliveryData.exponentialBackoff.value}
                  onChange={handleDeliveryChange}
                  color="primary"
                />
              </div>
            </div>

            {/* Maximum Retry Interval */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "150px 1fr 200px",
                gap: "10px",
                alignItems: "center",
              }}
            >
              <label
                style={{
                  color: "#333",
                  fontSize: "14px",
                }}
              >
                Maximum Retry Interval (in min):{" "}
                <span style={{ color: "red" }}>*</span>
              </label>
              <input
                type="text"
                name="maximumRetryInterval"
                value={deliveryData.maximumRetryInterval.param}
                onChange={handleParamChange}
                placeholder="Define Parameter"
                style={{
                  padding: "8px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "14px",
                }}
              />
              <input
                type="text"
                name="maximumRetryInterval"
                value={deliveryData.maximumRetryInterval.value}
                onChange={handleDeliveryChange}
                style={{
                  padding: "8px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "14px",
                }}
              />
            </div>

            {/* Dead-Letter Queue */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "150px 1fr 200px",
                gap: "10px",
                alignItems: "center",
              }}
            >
              <label
                style={{
                  color: "#333",
                  fontSize: "14px",
                }}
              >
                Dead-Letter Queue:
              </label>
              <input
                type="text"
                name="deadLetterQueue"
                value={deliveryData.deadLetterQueue.param}
                onChange={handleParamChange}
                placeholder="Define Parameter"
                style={{
                  padding: "8px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "14px",
                }}
              />
              <div style={{ display: "flex", justifyContent: "center" }}>
                <Checkbox
                  name="deadLetterQueue"
                  checked={deliveryData.deadLetterQueue.value}
                  onChange={handleDeliveryChange}
                  color="primary"
                />
              </div>
            </div>

            {/* Encrypt Message During Persistence */}
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "150px 1fr 200px",
                gap: "10px",
                alignItems: "center",
              }}
            >
              <label
                style={{
                  color: "#333",
                  fontSize: "14px",
                }}
              >
                Encrypt Message During Persistence:{" "}
                <span style={{ color: "red" }}>*</span>
              </label>
              <input
                type="text"
                name="encryptMessageDuringPersistence"
                value={deliveryData.encryptMessageDuringPersistence.param}
                onChange={handleParamChange}
                placeholder="Define Parameter"
                style={{
                  padding: "8px 12px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "14px",
                }}
              />
              <div style={{ display: "flex", justifyContent: "center" }}>
                <Checkbox
                  name="encryptMessageDuringPersistence"
                  checked={deliveryData.encryptMessageDuringPersistence.value}
                  onChange={handleDeliveryChange}
                  color="primary"
                />
              </div>
            </div>
          </>
        )}
      </div>
      <div
        style={{
          // position: 'fixed',
          bottom: "0",
          left: "0",
          right: "0",
          backgroundColor: "#f8f9fa",
          borderTop: "1px solid #dee2e6",
          padding: "15px 20px",
          display: "flex",
          justifyContent: "flex-end",
          gap: "10px",
          zIndex: 1000,
        }}
      >
        <button
          onClick={handleSubmit}
          disabled={loading}
          style={{
            padding: "8px 20px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            fontSize: "14px",
            fontWeight: "500",
            cursor: "pointer",
            transition: "background-color 0.2s",
            opacity: loading ? 0.7 : 1,
          }}
          onMouseOver={(e) =>
            !loading && (e.target.style.backgroundColor = "#0056b3")
          }
          onMouseOut={(e) =>
            !loading && (e.target.style.backgroundColor = "#007bff")
          }
        >
          {loading ? "Updating..." : "OK"}
        </button>
        <button
          onClick={handleCancel}
          style={{
            padding: "8px 20px",
            backgroundColor: "transparent",
            color: "#007bff",
            border: "1px solid #007bff",
            borderRadius: "4px",
            fontSize: "14px",
            fontWeight: "500",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "#007bff";
            e.target.style.color = "white";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "transparent";
            e.target.style.color = "#007bff";
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default DeliveryAssuranceTab;
