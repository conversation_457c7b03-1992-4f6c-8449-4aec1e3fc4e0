import "../App.css";
import './ConfigureIDOCModel.css';
import AS2ConfigurationParam from "./AS2ConfigurationParam";

const ConfigureAS2 = ({ isOpen, onClose, iFlowName }) => {
  if (!isOpen) return null;

  return (
    <div className="scheduler-modal-overlay">
      <div className="scheduler-modal-content" style={{maxWidth:"50rem"}}>
        <div className="scheduler-modal-header">
          <h3>Configure AS2 for {iFlowName}</h3>
        </div>
        <div className="scheduler-modal-body">
          <AS2ConfigurationParam artifactId={iFlowName} onClose={onClose} />
        </div>
      </div>
    </div>
  );
};

export default ConfigureAS2;