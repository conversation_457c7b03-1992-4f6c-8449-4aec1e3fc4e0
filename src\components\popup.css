/* Popup Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out;
}

.popup-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  min-width: 400px;
  max-width: 500px;
  width: 90%;
  animation: slideIn 0.3s ease-out;
  overflow: hidden;
}

.popup-header {
  display: flex;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.popup-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  margin-right: 16px;
  flex-shrink: 0;
}

.popup-title {
  flex: 1;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.popup-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.popup-close:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.popup-content {
  padding: 16px 24px 24px;
}

.popup-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 14px;
}

.popup-footer {
  padding: 16px 24px 24px;
  display: flex;
  justify-content: flex-end;
}

.popup-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 10px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  min-width: 80px;
}

.popup-button:hover {
  background-color: #2563eb;
}

/* Popup Type Variations */
.popup-success .popup-icon {
  background-color: #dcfce7;
  color: #16a34a;
}

.popup-success .popup-button {
  background-color: #16a34a;
}

.popup-success .popup-button:hover {
  background-color: #15803d;
}

.popup-error .popup-icon {
  background-color: #fef2f2;
  color: #dc2626;
}

.popup-error .popup-button {
  background-color: #dc2626;
}

.popup-error .popup-button:hover {
  background-color: #b91c1c;
}

.popup-warning .popup-icon {
  background-color: #fef3c7;
  color: #d97706;
}

.popup-warning .popup-button {
  background-color: #d97706;
}

.popup-warning .popup-button:hover {
  background-color: #b45309;
}

.popup-info .popup-icon {
  background-color: #dbeafe;
  color: #2563eb;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-50px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .popup-container {
    min-width: 300px;
    margin: 16px;
  }
  
  .popup-header {
    padding: 16px 20px 12px;
  }
  
  .popup-content {
    padding: 12px 20px 20px;
  }
  
  .popup-footer {
    padding: 12px 20px 20px;
  }
  
  .popup-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
    margin-right: 12px;
  }
  
  .popup-title {
    font-size: 16px;
  }
}