
import React from "react";

const POMultipleFormSection = ({ formData, setFormData, serviceInterfaces  }) => {
  const handleInputChange = (type, value) => {
    setFormData({
      ...formData,
      [type]: value,
    });
  };

  return (
    
    <div className="testcase-section">
      <h5 style={{ display: "flex", marginBottom: "10px" ,marginTop:"20px"}}>
        Please mention the required details from SAP PO 7.5 Monitoring
      </h5>
      <table>
        <thead>
          <tr style={{ backgroundColor: "#272D4F", color: "white" }}>
            <th >
              SAP PO 7.5
            </th>
            <th style={{ padding: "10px", border: "1px solid #ccc", textAlign: "left" }}>
              Enter details
            </th>
          </tr>
        </thead>
        <tbody>
          {["startTime", "endTime"].map((type) => (
            <tr key={type}>
              <td >
                {type === "startTime" ? "Start Time" : "End Time"}
              </td>
              <td >
                <input
                  type="text"
                  value={formData[type] || ""}
                  onChange={(e) => handleInputChange(type, e.target.value)}
                  placeholder="2025-05-29T10:00:58.137Z"
                  
                />
              </td>
            </tr>
          ))}
          <tr>
          <td>Service Interface</td>
          <td>
            <select
              value={formData.serviceInterface}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  serviceInterface: e.target.value,
                })
              }
            >
              <option value="">Select Interface</option>
              {serviceInterfaces.map((item, index) => (
                <option key={index} value={item.interface.name}>
                  {item.interface.name} ({item.interface.namespace})
                </option>
              ))}
            </select>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  );
};

export default POMultipleFormSection;