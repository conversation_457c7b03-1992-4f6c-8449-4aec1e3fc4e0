import React, { useState } from "react";
import API_ENDPOINT from "../config";
import { Snackbar, Alert } from "@mui/material";
import { useNavigate } from "react-router-dom";
import "./ConfigureSftpParams.css";

const ConfigureSftpParams = ({ artifactId, onClose ,header, targetapi,sourceapi}) => {
  const [source, setSource] = useState({ name: "", value: "" });
  const [target, setTarget] = useState({ name: "", value: "" });
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [severity, setSeverity] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleBack = () => {
    if (onClose) {
      onClose();
    } else {
      navigate("/SelectTask");
    }
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const validateInputs = () => {
    if (!source.name.trim()) {
      setSnackbarMessage("Source parameter name is required");
      return false;
    }
    if (!source.value.trim()) {
      setSnackbarMessage("Source parameter value is required");
      return false;
    }
    if (!target.name.trim()) {
      setSnackbarMessage("Target parameter name is required");
      return false;
    }
    if (!target.value.trim()) {
      setSnackbarMessage("Target parameter value is required");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    if (!validateInputs()) {
      setSeverity("error");
      setSnackbarOpen(true);
      setLoading(false);
      return;
    }

    try {
      // Submit source parameters
      const sourceEndpoint = `${API_ENDPOINT}${sourceapi}${artifactId}/${source.name}`;
      const sourceResponse = await fetch(sourceEndpoint, {
        method: "PUT",
        headers: {
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ parameterValue: source.value }),
      });

      if (!sourceResponse.ok) {
        const errorData = await sourceResponse.json();
        throw new Error(errorData.message || `Failed to update source parameter ${source.name}`);
      }

      // Submit target parameters
      const targetEndpoint = `${API_ENDPOINT}${targetapi}${artifactId}/${target.name}`;
      const targetResponse = await fetch(targetEndpoint, {
        method: "PUT",
        headers: {
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ parameterValue: target.value }),
      });

      if (!targetResponse.ok) {
        const errorData = await targetResponse.json();
        throw new Error(errorData.message || `Failed to update target parameter ${target.name}`);
      }

      setSnackbarMessage("Both source and target parameters updated successfully");
      setSeverity("success");
      setSnackbarOpen(true);
    } catch (error) {
      console.error("Error updating parameters:", error);
      setSnackbarMessage(error.message || "Failed to update parameters");
      setSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="sftp-configuration-container">
      {loading && (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>Updating parameters...</p>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <h3>{header}</h3>
        
        {/* Source Configuration */}
        <div className="param-group">
          <h4>Source Configuration</h4>
          <div className="form-group">
            <label htmlFor="source-name">Parameter Name:</label>
            <input
              type="text"
              id="source-name"
              value={source.name}
              onChange={(e) => setSource({...source, name: e.target.value})}
              className="form-control"
              placeholder="e.g., sftp_source_host"
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="source-value">Parameter Value:</label>
            <input
              type="text"
              id="source-value"
              value={source.value}
              onChange={(e) => setSource({...source, value: e.target.value})}
              className="form-control"
              placeholder="Enter source value"
              required
            />
          </div>
        </div>

        {/* Target Configuration */}
        <div className="param-group">
          <h4>Target Configuration</h4>
          <div className="form-group">
            <label htmlFor="target-name">Parameter Name:</label>
            <input
              type="text"
              id="target-name"
              value={target.name}
              onChange={(e) => setTarget({...target, name: e.target.value})}
              className="form-control"
              placeholder="e.g., sftp_target_path"
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="target-value">Parameter Value:</label>
            <input
              type="text"
              id="target-value"
              value={target.value}
              onChange={(e) => setTarget({...target, value: e.target.value})}
              className="form-control"
              placeholder="Enter target value"
              required
            />
          </div>
        </div>

        <div className="button-group">
          <button
            type="button"
            className="boldBtn btn btn-dark"
            onClick={handleBack}
          >
            Back
          </button>
          <button
            type="submit"
            className="boldBtn btn btn-primary"
            disabled={loading}
          >
            {loading ? "Updating..." : "Update Parameters"}
          </button>
        </div>
      </form>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
        sx={{ marginTop: "100px" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={severity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default ConfigureSftpParams;