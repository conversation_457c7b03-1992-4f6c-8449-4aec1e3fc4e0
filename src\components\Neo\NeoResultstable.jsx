import React, { useState } from "react";
import APIEndpoint from "../../config";
import CircularProgress from "@mui/material/CircularProgress";
import { DiffEditor } from "@monaco-editor/react";
import xmlFormatter from "xml-formatter";

const NeoResultsTable = ({
  formdata,
  comparisons,
  onRowClick,
  onViewPayload,
  hoveredRow,
  setHoveredRow,
}) => {
  const [diffViewerOpen, setDiffViewerOpen] = useState(false);
  const [currentComparison, setCurrentComparison] = useState(null);
  const [leftPayload, setLeftPayload] = useState("");
  const [rightPayload, setRightPayload] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const formatXml = (xmlString) => {
    if (!xmlString || typeof xmlString !== 'string') {
      return xmlString; // Return as-is if not a string or empty
    }

    try {
      // Basic check if it looks like XML
      if (xmlString.trim().startsWith('<')) {
        return xmlFormatter(xmlString, {
          indentation: '  ',
          collapseContent: false,
          lineSeparator: '\n',
        });
      }
      return xmlString; // Return as-is if not XML
    } catch (e) {
      console.error('Error formatting XML:', e);
      return xmlString; // Return original if formatting fails
    }
  };

  const handleViewDiff = async (comparison) => {
    setIsLoading(true);
    setCurrentComparison(comparison);

    try {
      const token = localStorage.getItem("token");

      // Fetch both payloads in parallel - using Neo specific file paths
      const [neoRes, cpiRes] = await Promise.all([
        fetch(`${APIEndpoint}${comparison.neoOutputFile}`, {
          headers: { "Authorization": `Basic ${localStorage.getItem("basicAuth")}`},
        }),
        fetch(`${APIEndpoint}${comparison.cpioutputFile}`, {
          headers: { "Authorization": `Basic ${localStorage.getItem("basicAuth")}` },
        }),
      ]);

      const [neoText, cpiText] = await Promise.all([
        neoRes.text(),
        cpiRes.text(),
      ]);

      // Format both XML payloads
      setLeftPayload(formatXml(neoText));
      setRightPayload(formatXml(cpiText));
      setDiffViewerOpen(true);
    } catch (error) {
      console.error("Error loading payloads:", error);
      setLeftPayload("Error loading Neo payload");
      setRightPayload("Error loading CPI payload");
      setDiffViewerOpen(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="table-container">
        <table className="comparison-table">
          <thead>
            <tr>
              <th>Test Case</th>
              <th>Key</th>
              <th>Flow Name</th>
              <th>Neo Output File</th>
              <th>CPI Output File</th>
              <th>Test Result</th>
              <th>Actions</th>
              <th>Differences</th>
              <th>CPI O/P</th>
              <th>Neo O/P</th>
              <th>Error</th>
              <th>Solution</th>
            </tr>
          </thead>
          <tbody style={{textAlign: "left"}}>
            {comparisons.map((comparison, index) => (
              <tr
                key={index}
                className={`${comparison.status === "Match" ? "Passed" : "Failed"} ${
                  hoveredRow === index ? "row-hover" : ""
                }`}
                onClick={() => onRowClick(comparison)}
                onMouseEnter={() => setHoveredRow(index)}
                onMouseLeave={() => setHoveredRow(null)}
                style={{ cursor: "pointer" }}
              >
                <td>{`Test ${index + 1}`}</td>
                <td>{comparison.key || "N/A"}</td>
             
                <td>{formdata.iFlowName || "N/A"}</td>
                <td>
                  <button
                    className="view-payload-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      onViewPayload(comparison.neoOutputFile, "Neo Output");
                    }}
                  >
                    View Neo Output
                  </button>
                </td>
                <td>
                  <button
                    className="view-payload-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      onViewPayload(comparison.cpioutputFile, "CPI Output");
                    }}
                  >
                    View CPI Output
                  </button>
                </td>

                <td>
                  <div
                    className={`status-badge ${
                      comparison.status === "Match"
                        ? "status-match"
                        : comparison.status === "Difference"
                        ? "status-difference"
                        : "status-cannot-process"
                    }`}
                  >
                    {comparison.status}
                  </div>
                </td>

                <td>
                  <button
                    className={`details-button ${
                      comparison.status === "Match" ? "disabled" : ""
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (comparison.status !== "Match") {
                        handleViewDiff(comparison);
                      }
                    }}
                    disabled={comparison.status === "Match"}
                  >
                    {comparison.status === "Match" ? "No Errors" : "View Diff"}
                  </button>
                </td>
                <td>
                  {comparison.differences ? comparison.differences.length : 0}
                </td>
                <td>{comparison.cpiOutput}</td>
                <td>{comparison.neoOutput}</td>
                <td>{comparison.reason}</td>
                <td>{comparison.solution}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Diff Viewer Modal */}
      {diffViewerOpen && (
        <div className="modal-overlay">
          <div className="modal-content diff-modal">
            <div className="modal-header">
              <h3>
                Payload Comparison: {currentComparison?.interfaceName || "N/A"}
                <small> (Key: {currentComparison?.key})</small>
              </h3>
              <button
                className="close-modal"
                onClick={() => setDiffViewerOpen(false)}
              >
                ×
              </button>
            </div>

            <div className="diff-container">
              {isLoading ? (
                <div className="loading-message">
                  <CircularProgress />
                  <p>Loading payloads for comparison...</p>
                </div>
              ) : (
                leftPayload &&
                rightPayload && (
                  <DiffEditor
                    key={currentComparison?.key}
                    height="70vh"
                    language="xml"
                    original={leftPayload}
                    modified={rightPayload}
                    options={{
                      readOnly: true,
                      renderSideBySide: true,
                      enableSplitViewResizing: true,
                      scrollBeyondLastLine: false,
                      minimap: { enabled: false },
                    }}
                  />
                )
              )}
            </div>

            <div className="diff-legend">
              <div className="legend-item">
                <span className="legend-color neo-color"></span>
                <span>Neo Output Payload</span>
              </div>
              <div className="legend-item">
                <span className="legend-color cpi-color"></span>
                <span>CPI Output Payload</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default NeoResultsTable;