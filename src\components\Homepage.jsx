import React from "react";

const Homepage = ({ onNavigate }) => {
  return (
    <>
    <div style={{
      padding: "20px",
      maxWidth: "900px",
      margin: "1rem auto",
      minHeight: "calc(100vh - 200px)", // Adjust based on your navbar/footer height
      display: "flex",
      flexDirection: "column",
      border: "1px solid #e5e7eb",
      borderRadius: "12px",
      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)",
      backgroundColor: "#fff",
      justifyContent: "space-between"
    }}>
    <div>
              <h2>Welcome to IntSwitch Test Automation Tool</h2>
              <p style={{fontSize: "17px", color: "gray"}}>
                <strong>IntSwitch</strong> Test Automation tool for testing data
                migrated from Non-SAP or SAP based <br />
                on Premise / On cloud middleware to SAP Integration Suite’s
                Cloud <br />
                Integration Capability
              </p>
            </div>
            <footer>
              <a href="#">
                Welcome to IntSwitch Testing Tool and copyright Information
              </a>
              <p style={{fontSize: "10px", color: "gray"}}>
                Incture is one of the leading providers of digital applications, products, and digital engineering solutions on SAP® Business Technology Platform.
Cherrywork® is a comprehensive extensions suite for SAP customers delivering packaged business value with agility and at scale to address evolving business requirements.
Incture, Cherrywork and other Cherrywork applications are registered trademarks of Incture Technologies Private Limited
              </p>
            </footer></div>
    </>
  );
};

export default Homepage;


