import React, { useState } from 'react';
import BasicScheduler from '../components/Schedulers/BasicScheduler';
import './ConfigureSchedulerModal.css';
import ConfigureSftpParams from './Configuresftpparams';

const ConfigureSchedulerModal = ({ isOpen, onClose, iFlowName }) => {
  const [activeTab, setActiveTab] = useState('parameters'); // 'parameters' or 'scheduler'

  if (!isOpen) return null;

  return (
    <div className="scheduler-modal-overlay">
      <div className="scheduler-modal-content">
        <div className="scheduler-modal-header">
          <h3>Configure SFTP For {iFlowName}</h3>
          <button className="scheduler-close-button" onClick={onClose}>
            &times;
          </button>
        </div>
        
        {/* Tabs for navigation */}
        <div className="scheduler-modal-tabs">
          <button 
            className={`scheduler-tab ${activeTab === 'parameters' ? 'active' : ''}`}
            onClick={() => setActiveTab('parameters')}
          >
            Configure Parameters
          </button>
          <button 
            className={`scheduler-tab ${activeTab === 'scheduler' ? 'active' : ''}`}
            onClick={() => setActiveTab('scheduler')}
          >
            Configure Scheduler
          </button>
        </div>
        
        <div className="scheduler-modal-body">
          {activeTab === 'parameters' ? (
            <div className="parameters-config">
              <ConfigureSftpParams artifactId={iFlowName} onClose={onClose} header="Configure SFTP Parameters" targetapi="/api/updateParamSftpTarget/" sourceapi="/api/updateParamSftpSource/"/>
            </div>
          ) : (
            <div className="parameters-config">
            <div className="sftp-configuration-container" style={{display:"flex", alignContent:"center"}}>
            <BasicScheduler artifactId={iFlowName} onClose={onClose} /></div></div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConfigureSchedulerModal; 