import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import API_ENDPOINT from "../../config";
import { Snackbar, Alert } from "@mui/material";
import { useNavigate } from "react-router-dom";
import Scheduler from "./Scheduler";
import Scheduler2 from "./Scheduler2";
import "./BasicScheduler.css";

const BasicScheduler = ({ artifactId, onClose }) => {
  const [selectedScheduler, setSelectedScheduler] = useState("scheduler1");
  const [selectedScheduleType, setSelectedScheduleType] = useState("on_day");
  const [cronExpressions, setCronExpressions] = useState({
    scheduler1: "",
    scheduler2: "",
    scheduler3: "",
    scheduler4: "",
  });
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [severity, setSeverity] = useState("");
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [parameterName, setParameterName] = useState("");

  const handleSchedulerChange = (e) => {
    setSelectedScheduler(e.target.value);
  };

  const handleBack = () => {
    if (onClose) {
      onClose();
    } else {
      navigate("/SelectTask");
    }
  };

  const handleScheduleTypeChange = (e) => {
    const newScheduleType = e.target.value;
    setSelectedScheduleType(newScheduleType);
    setSelectedScheduler(
      newScheduleType === "on_day" ? "scheduler1" : "scheduler3"
    );
  };

  const handleCronExpressionUpdate = (scheduler, cronExpression) => {
    setCronExpressions((prev) => ({
      ...prev,
      [scheduler]: cronExpression,
    }));
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const handleParameterNameChange = (e) => {
    setParameterName(e.target.value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    const cronExpression = cronExpressions[selectedScheduler];

    if (!cronExpression) {
      console.error("No cron expression provided.");
      return;
    }

    if (!parameterName) {
      setSnackbarMessage("Please enter a parameter name");
      setSeverity("error");
      setSnackbarOpen(true);
      setLoading(false);
      return;
    }

    const endpoint = `${API_ENDPOINT}/api/updateParam/${artifactId}/${parameterName}`;

    try {
      const response = await fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          parameterValue: cronExpression,
        }),
      });

      if (response.ok) {
        setSnackbarMessage("Updated Successfully");
        setSeverity("success");
        setSnackbarOpen(true);
        setLoading(false);
      } else {
        setSnackbarMessage("Failed uploading.");
        setSeverity("error");
        setSnackbarOpen(true);
        console.error("Failed to update scheduler");
        setLoading(false);
      }
    } catch (error) {
      console.error("Error updating scheduler:", error);
      setLoading(false);
    }
  };

  return (
    <div style={{ width: "fit-content" }}>
      {loading && (
        <div className="loading-container">
          {/* <img src={out} alt="Loading" className="loading-gif" /> */}
        </div>
      )}
      <form onSubmit={handleSubmit}>
        <h3>Configure SFTP Scheduler</h3>
        <div className="form-group">
          <label htmlFor="parameterName">Parameter Name:</label>
          <input
            type="text"
            id="parameterName"
            value={parameterName}
            onChange={handleParameterNameChange}
            className="form-control"
            placeholder="Enter parameter name for scheduler"
            required
          />
        </div>

        <div className="schedule-type">
          <div className="time">
            <div className="form-check">
              <input
                type="radio"
                id="on_day"
                value="on_day"
                checked={selectedScheduleType === "on_day"}
                onChange={handleScheduleTypeChange}
              />
              <label htmlFor="on_day">Schedule on Day</label>
            </div>
          </div>

          {selectedScheduleType === "on_day" && (
            <div className={`on-day ${selectedScheduleType === "on_day" ? "active" : ""}`}>
              <div className="form-check">
                <input
                  type="radio"
                  id="scheduler1_on_day"
                  value="scheduler1"
                  checked={selectedScheduler === "scheduler1"}
                  onChange={handleSchedulerChange}
                />
                <label htmlFor="scheduler1_on_day">On Time</label>
              </div>
              <div className="form-check">
                <input
                  type="radio"
                  id="scheduler2_on_day"
                  value="scheduler2"
                  checked={selectedScheduler === "scheduler2"}
                  onChange={handleSchedulerChange}
                />
                <label htmlFor="scheduler2_on_day">Between Time</label>
              </div>
              {selectedScheduler === "scheduler1" && (
                <Scheduler
                  onCronExpressionUpdate={(cronExp) =>
                    handleCronExpressionUpdate("scheduler1", cronExp)
                  }
                />
              )}
              {selectedScheduler === "scheduler2" && (
                <Scheduler2
                  onCronExpressionUpdate={(cronExp) =>
                    handleCronExpressionUpdate("scheduler2", cronExp)
                  }
                />
              )}
            </div>
          )}

          {selectedScheduleType === "recur" && (
            <div className="recur">
              <div className="form-check">
                <input
                  type="radio"
                  id="scheduler3_recur"
                  value="scheduler3"
                  checked={selectedScheduler === "scheduler3"}
                  onChange={handleSchedulerChange}
                />
                <label htmlFor="scheduler3_recur">On Time</label>
              </div>
              <div className="form-check">
                <input
                  type="radio"
                  id="scheduler4_recur"
                  value="scheduler4"
                  checked={selectedScheduler === "scheduler4"}
                  onChange={handleSchedulerChange}
                />
                <label htmlFor="scheduler4_recur">Every</label>
              </div>
              {selectedScheduler === "scheduler3" && (
                <Scheduler4
                  onCronExpressionUpdate={(cronExp) =>
                    handleCronExpressionUpdate("scheduler3", cronExp)
                  }
                />
              )}
              {selectedScheduler === "scheduler4" && (
                <Scheduler3
                  onCronExpressionUpdate={(cronExp) =>
                    handleCronExpressionUpdate("scheduler4", cronExp)
                  }
                />
              )}
            </div>
          )}
        </div>

        <div className="form-group">
          <div className="col btn-group">
            <button className="boldBtn btn btn-dark mx-5" onClick={handleBack}>
              Back
            </button>
            <button type="submit" className="boldBtn btn btn-primary">
              {loading ? "Updating..." : "Update"}
            </button>
          </div>
        </div>
      </form>

      <Snackbar
        open={snackbarOpen}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
        sx={{ marginTop: "100px" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={severity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default BasicScheduler;