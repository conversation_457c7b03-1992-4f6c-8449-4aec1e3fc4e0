import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Button,
  Card,
  CardContent,
} from "@mui/material";
import muIcon from "../assets/mu.png";
import boomiIcon from "../assets/Boomi.png";
import poIcon from "../assets/database.png";
import neoIcon from "../assets/database.png";
import sapIcon from "../assets/sap.png";

const ExtractPayloads = () => {
  const [sourceSystem, setSourceSystem] = useState("");
  const targetSystem = "SAP Cloud Integration";
  const navigate = useNavigate();

  const handleChangeSource = (event) => {
    setSourceSystem(event.target.value);
  };
  
  const handleProceed = () => {
    if (!sourceSystem) {
      alert("Please select a source system.");
      return;
    }
    
    // Navigate based on selected system
    switch(sourceSystem) {
      case "PO":
        navigate("/dashboard/po-login");
        break;
      case "Boomi":
        navigate("/dashboard/boomi");
        break;
      case "Mulesoft":
        navigate("/dashboard/mulesoft");
        break;
      case "Neo":
        navigate("/dashboard/neo");
        break;
      default:
        break;
    }
  };

  return (
    <Box sx={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      p: 3,
    }}>
      <Card sx={{
        width: "100%",
        maxWidth: 900,
        p: 2,
        backgroundColor: "#F8F7F7",
        boxShadow: "0px 0px 20px 3px gray",
      }}>
        <CardContent>
          <Box sx={{
            display: "flex",
            flexDirection: "column",
            gap: 3,
          }}>
            <Typography variant="h4" gutterBottom sx={{ 
              fontWeight: "bold", 
              textAlign: "center" 
            }}>
              IntSwitch: Test Automation Tool
            </Typography>
            <hr />

            <Box sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-evenly",
              gap: 4,
            }}>
              <Typography variant="h6" sx={{ fontWeight: 800 }}>
                Source System
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 800 }}>
                Target System
              </Typography>
            </Box>

            <Box sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: 4,
            }}>
              <FormControl sx={{ minWidth: 300 }}>
                <InputLabel>Source System</InputLabel>
                <Select
                  value={sourceSystem}
                  label="Source System"
                  onChange={handleChangeSource}
                >
                  {[
                    // { value: "Mulesoft", icon: muIcon, label: "Mulesoft" },
                    // { value: "Boomi", icon: boomiIcon, label: "Boomi" },
                    { value: "PO", icon: poIcon, label: "SAP PO 7.5" },
                    { value: "Neo", icon: neoIcon, label: "Neo" },
                  ].map((system) => (
                    <MenuItem key={system.value} value={system.value}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <img 
                          src={system.icon} 
                          alt={system.label} 
                          width="24" 
                          height="24" 
                        />
                        {system.label}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Box sx={{
                display: "flex",
                alignItems: "center",
                minWidth: 300,
                border: "1px solid rgba(0, 0, 0, 0.23)",
                borderRadius: 1,
                padding: "16.5px 14px",
                bgcolor: "#f5f5f5",
              }}>
                <img
                  src={sapIcon}
                  alt="SAP Cloud Integration"
                  width="24"
                  height="24"
                  style={{ marginRight: 8 }}
                />
                {targetSystem}
              </Box>
            </Box>

            <Button
              variant="contained"
              onClick={handleProceed}
              sx={{
                mt: 2,
                fontWeight: "bold",
                width: "50%",
                alignSelf: "center",
                backgroundColor: "#272D4F",
                "&:hover": {
                  backgroundColor: "#1a2035",
                },
              }}
            >
              Proceed
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ExtractPayloads;