// import React, { useState } from "react";
// import { useNavigate } from "react-router-dom";
// import ScenarioSelector from "../ScenarioSelector";
// import NeoSingleTestCaseForm from "./NeoSingleTestCaseForm";
// import NeoMultipleTestCaseForm from "./NeoMultipleTestCaseForm";
// import NeoComparisonResults from "./NeoComparisonResults";
// import APIEndpoint from "../../config";
// import "../../App.css";
// import "../popup.css";

// const CustomPopup = ({ isOpen, onClose, title, message, type = "info" }) => {
//   if (!isOpen) return null;

//   // const getIcon = () => {
//   //   switch (type) {
//   //     case "success":
//   //       return "✓";
//   //     case "error":
//   //       return "✕";
//   //     case "warning":
//   //       return "⚠";
//   //     default:
//   //       return "ℹ";
//   //   }
//   // };

//   const getTypeClass = () => {
//     switch (type) {
//       case "success":
//         return "popup-success";
//       case "error":
//         return "popup-error";
//       case "warning":
//         return "popup-warning";
//       default:
//         return "popup-info";
//     }
//   };

//   return (
//     <div className="popup-overlay">
//       <div className={`popup-container ${getTypeClass()}`}>
//         <div className="popup-header">
//           {/* <div className="popup-icon">{getIcon()}</div> */}
//           <h3 className="popup-title">{title}</h3>
//           <button className="popup-close" onClick={onClose}>
//             ×
//           </button>
//         </div>
//         <div className="popup-content">
//           <p>{message}</p>
//         </div>
//         <div className="popup-footer">
//           <button className="popup-button" onClick={onClose}>
//             OK
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };

// const NeoSingleTest = () => {
//   const [selection, setSelection] = useState(null);
//   const [showResults, setShowResults] = useState(false);
//   const [comparisonResult, setComparisonResult] = useState(null);

//   // Popup state
//   const [popup, setPopup] = useState({
//     isOpen: false,
//     title: "",
//     message: "",
//     type: "info",
//   });
//   const [formData, setFormData] = useState({
//     neoInputPayloadzip: null,
//     neoInputPayloadContentzip: "",
//     neoInputPayload: null, //ng
//     neoInputPayloadContent: "", // Store content for preview if ne Store the file object instead of strieded
//     serviceInterface: "",
//     tenant: "",
//     tenantNeo: "",
//     username: "",
//     packageName: "",
//     packageNameNeo: "",
//     iFlowNameNeo: "",
//     iFlowName: "",
//     NeoadapterName: "",
//     adapterName: "",
//   });

//   const navigate = useNavigate();

//   // Popup helper functions
//   const showPopup = (title, message, type = "info") => {
//     setPopup({
//       isOpen: true,
//       title,
//       message,
//       type,
//     });
//   };

//   const closePopup = () => {
//     setPopup({
//       isOpen: false,
//       title: "",
//       message: "",
//       type: "info",
//     });
//   };

//   const handleProceed = async () => {
//     if (selection === "single") {
//       await handleSingleTestProceed();
//     } else if (selection === "multiple") {
//       await handleMultipleTestProceed();
//     }
//   };

//   const handleGenerateReport = async () => {
//     if (selection === "single") {
//       await handleSingleTestGenerateReport();
//     } else if (selection === "multiple") {
//       await handleMultipleTestGenerateReport();
//     }
//   };

//   const handleSingleTestProceed = async () => {
//     const token = localStorage.getItem("token");

//     try {
//       const { serviceInterface, iFlowNameNeo, tenantNeo } = formData;

//       const formDataToSend = new FormData();
//       formDataToSend.append("inputFile", formData.neoInputPayload);
//       formDataToSend.append("NeoartifactName", iFlowNameNeo);
//       formDataToSend.append("CfartifactName", formData.iFlowName);
//       formDataToSend.append("adapterType", formData.adapterName);
//       formDataToSend.append("configName", formData.tenant);

//       const response = await fetch(`${APIEndpoint}/api/testNeoSingle`, {
//         method: "POST",
//         headers: {
//           "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
//         },
//         body: formDataToSend,
//       });

//       if (!response.ok) {
//         const errorText = await response.text();
//         throw new Error(`API error (${response.status}): ${errorText}`);
//       }

//       // First try to read as text to see what we got
//       const responseText = await response.text();
//       let result;

//       try {
//         // Try to parse as JSON if possible
//         result = responseText ? JSON.parse(responseText) : {};
//       } catch (e) {
//         // If not JSON, use the text as the result
//         result = { message: responseText };
//       }

//       console.log("API response:", result);

//       if (responseText.includes("created successfully")) {
//         showPopup(
//           "Success",
//           "Data extraction completed successfully! Please wait a minute to generate the report.",
//           "success"
//         );
//       } else {
//         showPopup(
//           "Success",
//           "Request processed, but unexpected response format.",
//           "info"
//         );
//       }

//       return result;
//     } catch (error) {
//       console.error("Submission error:", error);
//       showPopup(
//         "Processing Failed",
//         `Processing failed: ${error.message}`,
//         "error"
//       );
//       throw error;
//     }
//   };

//   const handleSingleTestGenerateReport = async () => {
//     const token = localStorage.getItem("token");

//     try {
//       const { serviceInterface, iFlowNameNeo, tenantNeo } = formData;

//       const params = new URLSearchParams({
//         cpiArtifactName: formData.iFlowName,
//         neoArtifactName: iFlowNameNeo,
//         adapterType: formData.adapterName,
//       });

//       const compareResponse = await fetch(
//         `${APIEndpoint}/api/neocfunitcompare?${params.toString()}`,
//         {
//           method: "GET",
//           headers: {
//             "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
//           },
//         }
//       );

//       if (compareResponse == "created successfully") {
//         const text = await compareResponse.text();
//         throw new Error(`Comparison error: ${text}`);
//       }

//       const compareData = await compareResponse.json();
//       console.log("Comparison result:", compareData);

//       setComparisonResult(compareData);
//       setShowResults(true);
//     } catch (error) {
//       console.error("Report generation error:", error);
//       showPopup(
//         "Report Generation Failed",
//         `Report generation failed: ${error.message}`,
//         "error"
//       );
//       throw error;
//     }
//   };

//   const handleMultipleTestProceed = async () => {
//     const token = localStorage.getItem("token");

//     try {
//       const { serviceInterface, iFlowNameNeo, tenantNeo } = formData;

//       const formDataToSend = new FormData();
//       formDataToSend.append("inputFile", formData.neoInputPayloadzip); // This will now be the File object
//       formDataToSend.append("NeoartifactName", iFlowNameNeo);
//       formDataToSend.append("CfartifactName", formData.iFlowName);
//       formDataToSend.append("adapterType", formData.adapterName);
//       formDataToSend.append("configName", formData.tenant);
//       const response = await fetch(`${APIEndpoint}/api/testNeoBulk`, {
//         method: "POST",
//         headers: {
//           "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
//         },
//         body: formDataToSend,
//       });

//       if (!response.ok) {
//         const errorText = await response.text();
//         throw new Error(`API error (${response.status}): ${errorText}`);
//       }

//       // First try to read as text to see what we got
//       const responseText = await response.text();
//       let result;

//       try {
//         // Try to parse as JSON if possible
//         result = responseText ? JSON.parse(responseText) : {};
//       } catch (e) {
//         // If not JSON, use the text as the result
//         result = { message: responseText };
//       }

//       console.log("API response:", result);

//       if (responseText.includes("created successfully")) {
//         showPopup(
//           "Success",
//           "Data extraction completed successfully! Please wait a minute to generate the report.",
//           "success"
//         );
//       } else {
//         showPopup(
//           "Success",
//           "Request processed, but unexpected response format.",
//           "info"
//         );
//       }

//       return result;
//     } catch (error) {
//       console.error("Submission error:", error);
//       showPopup(
//         "Processing Failed",
//         `Processing failed: ${error.message}`,
//         "error"
//       );
//       throw error;
//     }
//   };

//   const handleMultipleTestGenerateReport = async () => {
//      const token = localStorage.getItem("token");

//     try {
//       const { serviceInterface, iFlowNameNeo, tenantNeo } = formData;

//       const params = new URLSearchParams({
//         cpiArtifactName: formData.iFlowName,
//         neoArtifactName: iFlowNameNeo,
//         adapterType: formData.adapterName,
//       });

//       const compareResponse = await fetch(
//         `${APIEndpoint}/api/neocfunitcompare?${params.toString()}`,
//         {
//           method: "GET",
//           headers: {
//             "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
//           },
//         }
//       );

//       if (compareResponse == "created successfully") {
//         const text = await compareResponse.text();
//         throw new Error(`Comparison error: ${text}`);
//       }

//       const compareData = await compareResponse.json();
//       console.log("Comparison result:", compareData);

//       setComparisonResult(compareData);
//       setShowResults(true);
//     } catch (error) {
//       console.error("Report generation error:", error);
//       showPopup(
//         "Report Generation Failed",
//         `Report generation failed: ${error.message}`,
//         "error"
//       );
//       throw error;
//     }
//   };

//   const handleBackToDashboard = () => {
//     navigate(-1);
//   };

//   return (
//     <div className="neo-po-testing-container">
//       {/* Custom Popup */}
//       <CustomPopup
//         isOpen={popup.isOpen}
//         onClose={closePopup}
//         title={popup.title}
//         message={popup.message}
//         type={popup.type}
//       />

//       {!showResults ? (
//         <>
//           <ScenarioSelector
//             selection={selection}
//             setSelection={setSelection}
//             showTitle={true}
//           />

//           {selection === "single" && (
//             <NeoSingleTestCaseForm
//               formData={formData}
//               setFormData={setFormData}
//               onBack={handleBackToDashboard}
//               onProceed={handleProceed}
//               onGenerateReport={handleGenerateReport}
//             />
//           )}

//           {selection === "multiple" && (
//             <NeoMultipleTestCaseForm
//               formData={formData}
//               setFormData={setFormData}
//               onBack={handleBackToDashboard}
//               onProceed={handleProceed}
//               onGenerateReport={handleGenerateReport}
//             />
//           )}
//         </>
//       ) : (
//         <NeoComparisonResults
//           comparisonResult={comparisonResult}
//           formdata={formData}
//           onBack={() => setShowResults(false)}
//           isSingleTestCase={selection === "single"}
//         />
//       )}
//     </div>
//   );
// };

// export default NeoSingleTest;


import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import ScenarioSelector from "../ScenarioSelector";
import NeoSingleTestCaseForm from "./NeoSingleTestCaseForm";
import NeoMultipleTestCaseForm from "./NeoMultipleTestCaseForm";
import NeoComparisonResults from "./NeoComparisonResults";
import APIEndpoint from "../../config";
import "../../App.css";
import "../popup.css";

const CustomPopup = ({ isOpen, onClose, title, message, type = "info" }) => {
  if (!isOpen) return null;

  const getTypeClass = () => {
    switch (type) {
      case "success":
        return "popup-success";
      case "error":
        return "popup-error";
      case "warning":
        return "popup-warning";
      default:
        return "popup-info";
    }
  };

  return (
    <div className="popup-overlay">
      <div className={`popup-container ${getTypeClass()}`}>
        <div className="popup-header">
          <h3 className="popup-title">{title}</h3>
          <button className="popup-close" onClick={onClose}>
            ×
          </button>
        </div>
        <div className="popup-content">
          <p>{message}</p>
        </div>
        <div className="popup-footer">
          <button className="popup-button" onClick={onClose}>
            OK
          </button>
        </div>
      </div>
    </div>
  );
};

const NeoSingleTest = () => {
  const [selection, setSelection] = useState(null);
  const [showResults, setShowResults] = useState(false);
  const [comparisonResult, setComparisonResult] = useState(null);

  // Popup state
  const [popup, setPopup] = useState({
    isOpen: false,
    title: "",
    message: "",
    type: "info",
  });

  // Separate form data for single and multiple test cases
  const [singleFormData, setSingleFormData] = useState({
    neoInputPayload: null, // For single test - XML file
    neoInputPayloadContent: "", // For single test - XML content
    serviceInterface: "",
    tenant: "",
    tenantNeo: "",
    username: "",
    packageName: "",
    packageNameNeo: "",
    iFlowNameNeo: "",
    iFlowName: "",
    NeoadapterName: "",
    adapterName: "",
  });

  const [multipleFormData, setMultipleFormData] = useState({
    neoInputPayloadzip: null, // For multiple test - ZIP file
    neoInputPayloadContentzip: "", // For multiple test - ZIP info
    serviceInterface: "",
    tenant: "",
    tenantNeo: "",
    username: "",
    packageName: "",
    packageNameNeo: "",
    iFlowNameNeo: "",
    iFlowName: "",
    NeoadapterName: "",
    adapterName: "",
  });

  const navigate = useNavigate();

  // Get the appropriate form data based on selection
  const getCurrentFormData = () => {
    return selection === "single" ? singleFormData : multipleFormData;
  };

  const getCurrentSetFormData = () => {
    return selection === "single" ? setSingleFormData : setMultipleFormData;
  };

  // Popup helper functions
  const showPopup = (title, message, type = "info") => {
    setPopup({
      isOpen: true,
      title,
      message,
      type,
    });
  };

  const closePopup = () => {
    setPopup({
      isOpen: false,
      title: "",
      message: "",
      type: "info",
    });
  };

  const handleProceed = async () => {
    if (selection === "single") {
      await handleSingleTestProceed();
    } else if (selection === "multiple") {
      await handleMultipleTestProceed();
    }
  };

  const handleGenerateReport = async () => {
    if (selection === "single") {
      await handleSingleTestGenerateReport();
    } else if (selection === "multiple") {
      await handleMultipleTestGenerateReport();
    }
  };

  const handleSingleTestProceed = async () => {
    const token = localStorage.getItem("token");

    try {
      const { serviceInterface, iFlowNameNeo, tenantNeo } = singleFormData;

      const formDataToSend = new FormData();
      formDataToSend.append("inputFile", singleFormData.neoInputPayload);
      formDataToSend.append("NeoartifactName", iFlowNameNeo);
      formDataToSend.append("CfartifactName", singleFormData.iFlowName);
      formDataToSend.append("adapterType", singleFormData.adapterName);
      formDataToSend.append("configName", singleFormData.tenant);

      const response = await fetch(`${APIEndpoint}/api/testNeoSingle`, {
        method: "POST",
        headers: {
          "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
        },
        body: formDataToSend,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error (${response.status}): ${errorText}`);
      }

      const responseText = await response.text();
      let result;

      try {
        result = responseText ? JSON.parse(responseText) : {};
      } catch (e) {
        result = { message: responseText };
      }

      console.log("API response:", result);

      if (responseText.includes("created successfully")) {
        showPopup(
          "Success",
          "Data extraction completed successfully! Please wait a minute to generate the report.",
          "success"
        );
      } else {
        showPopup(
          "Success",
          "Request processed, but unexpected response format.",
          "info"
        );
      }

      return result;
    } catch (error) {
      console.error("Submission error:", error);
      showPopup(
        "Processing Failed",
        `Processing failed: ${error.message}`,
        "error"
      );
      throw error;
    }
  };

  const handleSingleTestGenerateReport = async () => {
    const token = localStorage.getItem("token");

    try {
      const { serviceInterface, iFlowNameNeo, tenantNeo } = singleFormData;

      const params = new URLSearchParams({
        cpiArtifactName: singleFormData.iFlowName,
        neoArtifactName: iFlowNameNeo,
        adapterType: singleFormData.adapterName,
      });

      const compareResponse = await fetch(
        `${APIEndpoint}/api/neocfunitcompare?${params.toString()}`,
        {
          method: "GET",
          headers: {
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
          },
        }
      );

      if (!compareResponse.ok) {
        const text = await compareResponse.text();
        throw new Error(`Comparison error: ${text}`);
      }

      const compareData = await compareResponse.json();
      console.log("Comparison result:", compareData);

      setComparisonResult(compareData);
      setShowResults(true);
    } catch (error) {
      console.error("Report generation error:", error);
      showPopup(
        "Report Generation Failed",
        `Report generation failed: ${error.message}`,
        "error"
      );
      throw error;
    }
  };

  const handleMultipleTestProceed = async () => {
    const token = localStorage.getItem("token");

    try {
      const { serviceInterface, iFlowNameNeo, tenantNeo } = multipleFormData;

      const formDataToSend = new FormData();
      formDataToSend.append("inputFile", multipleFormData.neoInputPayloadzip);
      formDataToSend.append("NeoartifactName", iFlowNameNeo);
      formDataToSend.append("CfartifactName", multipleFormData.iFlowName);
      formDataToSend.append("adapterType", multipleFormData.adapterName);
      formDataToSend.append("configName", multipleFormData.tenant);

      const response = await fetch(`${APIEndpoint}/api/testNeoBulk`, {
        method: "POST",
        headers: {
          "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
        },
        body: formDataToSend,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error (${response.status}): ${errorText}`);
      }

      const responseText = await response.text();
      let result;

      try {
        result = responseText ? JSON.parse(responseText) : {};
      } catch (e) {
        result = { message: responseText };
      }

      console.log("API response:", result);

      if (responseText.includes("created successfully")) {
        showPopup(
          "Success",
          "Data extraction completed successfully! Please wait a minute to generate the report.",
          "success"
        );
      } else {
        showPopup(
          "Success",
          "Request processed, but unexpected response format.",
          "info"
        );
      }

      return result;
    } catch (error) {
      console.error("Submission error:", error);
      showPopup(
        "Processing Failed",
        `Processing failed: ${error.message}`,
        "error"
      );
      throw error;
    }
  };

  const handleMultipleTestGenerateReport = async () => {
    const token = localStorage.getItem("token");

    try {
      const { serviceInterface, iFlowNameNeo, tenantNeo } = multipleFormData;

      const params = new URLSearchParams({
        cpiArtifactName: multipleFormData.iFlowName,
        neoArtifactName: iFlowNameNeo,
        adapterType: multipleFormData.adapterName,
      });

      const compareResponse = await fetch(
        `${APIEndpoint}/api/neocfunitcompare?${params.toString()}`,
        {
          method: "GET",
          headers: {
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`,
          },
        }
      );

      if (!compareResponse.ok) {
        const text = await compareResponse.text();
        throw new Error(`Comparison error: ${text}`);
      }

      const compareData = await compareResponse.json();
      console.log("Comparison result:", compareData);

      setComparisonResult(compareData);
      setShowResults(true);
    } catch (error) {
      console.error("Report generation error:", error);
      showPopup(
        "Report Generation Failed",
        `Report generation failed: ${error.message}`,
        "error"
      );
      throw error;
    }
  };

  const handleBackToDashboard = () => {
    navigate(-1);
  };

  return (
    <div className="neo-po-testing-container">
      {/* Custom Popup */}
      <CustomPopup
        isOpen={popup.isOpen}
        onClose={closePopup}
        title={popup.title}
        message={popup.message}
        type={popup.type}
      />

      {!showResults ? (
        <>
          <ScenarioSelector
            selection={selection}
            setSelection={setSelection}
            showTitle={true}
          />

          {selection === "single" && (
            <NeoSingleTestCaseForm
              formData={singleFormData}
              setFormData={setSingleFormData}
              onBack={handleBackToDashboard}
              onProceed={handleProceed}
              onGenerateReport={handleGenerateReport}
            />
          )}

          {selection === "multiple" && (
            <NeoMultipleTestCaseForm
              formData={multipleFormData}
              setFormData={setMultipleFormData}
              onBack={handleBackToDashboard}
              onProceed={handleProceed}
              onGenerateReport={handleGenerateReport}
            />
          )}
        </>
      ) : (
        <NeoComparisonResults
          comparisonResult={comparisonResult}
          formdata={getCurrentFormData()}
          onBack={() => setShowResults(false)}
          isSingleTestCase={selection === "single"}
        />
      )}
    </div>
  );
};

export default NeoSingleTest;
