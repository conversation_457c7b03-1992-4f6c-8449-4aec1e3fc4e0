import React, { useState, useEffect } from 'react';
import {
  FormControl, InputLabel, Select, MenuItem, Checkbox, FormGroup, FormControlLabel,
  TextField, Box, Typography, Grid
} from '@mui/material';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import dayjs from 'dayjs';

const Scheduler3 = ({ onCronExpressionUpdate }) => {
  const [startTime, setStartTime] = useState(dayjs('2022-01-01T05:00'));
  const [endTime, setEndTime] = useState(dayjs('2022-01-01T12:00'));
  const [intervalValue, setIntervalValue] = useState('5m');
  const [recurrenceType, setRecurrenceType] = useState('daily');
  const [selectedWeekdays, setSelectedWeekdays] = useState([]);

  const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  const handleWeekdayChange = (day) => {
    setSelectedWeekdays(prev => 
      prev.includes(day) ? prev.filter(d => d !== day) : [...prev, day]
    );
  };

  const handleRecurrenceTypeChange = (event) => {
    const newType = event.target.value;
    setRecurrenceType(newType);
    if (newType === 'weekly' && selectedWeekdays.length === 0) {
      setSelectedWeekdays(['Monday']);
    }
  };

  const generateCronExpression = () => {
    const [intervalNum] = intervalValue.match(/\d+/);
    const startHour = startTime.hour();
    const endHour = endTime.hour() - 1;
    const intervalPart = `0/${intervalNum}`;
    const hourRange = `${startHour}-${endHour}`;

    if (recurrenceType === 'daily') {
      return `0 ${intervalPart} ${hourRange} ? * * * --tz=IST`;
    } else if (recurrenceType === 'weekly') {
      const weekdayStr = selectedWeekdays.map(day => day.slice(0, 3).toUpperCase()).join(',');
      return `0 ${intervalPart} ${hourRange} ? * ${weekdayStr} * --tz=IST`;
    }

    return '';
  };

  useEffect(() => {
    const cronExpression = generateCronExpression();
    onCronExpressionUpdate(cronExpression);
  }, [startTime, endTime, intervalValue, recurrenceType, selectedWeekdays]);

  return (
    <Box sx={{ mt: 2 }}>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>Recurrence Type</InputLabel>
            <Select
              value={recurrenceType}
              onChange={handleRecurrenceTypeChange}
              label="Recurrence Type"
            >
              <MenuItem value="daily">Daily</MenuItem>
              <MenuItem value="weekly">Weekly</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {recurrenceType === 'weekly' && (
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>Choose Weekdays</Typography>
            <FormGroup row>
              {weekdays.map((day) => (
                <FormControlLabel
                  key={day}
                  control={
                    <Checkbox
                      checked={selectedWeekdays.includes(day)}
                      onChange={() => handleWeekdayChange(day)}
                    />
                  }
                  label={day}
                />
              ))}
            </FormGroup>
          </Grid>
        )}

        <Grid item xs={12} sm={6}>
          <TimePicker
            label="Start Time"
            value={startTime}
            onChange={(newValue) => setStartTime(newValue)}
            renderInput={(params) => <TextField {...params} fullWidth />}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TimePicker
            label="End Time"
            value={endTime}
            onChange={(newValue) => setEndTime(newValue)}
            renderInput={(params) => <TextField {...params} fullWidth />}
          />
        </Grid>

        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>Interval</InputLabel>
            <Select
              value={intervalValue}
              onChange={(e) => setIntervalValue(e.target.value)}
              label="Interval"
            >
              <MenuItem value="1m">1 min</MenuItem>
              <MenuItem value="5m">5 min</MenuItem>
              <MenuItem value="10m">10 min</MenuItem>
              <MenuItem value="15m">15 min</MenuItem>
              <MenuItem value="20m">20 min</MenuItem>
              <MenuItem value="30m">30 min</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Scheduler3;