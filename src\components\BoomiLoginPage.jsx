// import React from 'react'

// const BoomiLoginPage = () => {
//   //   const handleProceed = async () => {
//   //   const token = localStorage.getItem("token");
//   //   const queryParams = new URLSearchParams({
//   //     serviceInterface: formData.serviceInterface,
//   //     timestamp: formData.timestamp,
//   //     cpiEndPoint: formData.cpiEndPoint,
//   //     cpiClientId: formData.cpiClientId,
//   //     cpiClientSecret: formData.cpiClientSecret,
//   //   });

//   //   try {
//   //     // First API call - sending the test case
//   //     const response = await fetch(
//   //       `${APIEndpoint}/api/extractUnitPayloads?${queryParams.toString()}`,
//   //       {
//   //         method: "POST",
//   //         headers: {
//   //           Authorization: token ? `Bearer ${token}` : "",
//   //         },
//   //       }
//   //     );

//   //     if (!response.ok) {
//   //       const text = await response.text();
//   //       throw new Error(`Error ${response.status}: ${text}`);
//   //     }

//   //     const result = await response.json();
//   //     console.log("Test case submitted:", result);

//   //     // Second API call - fetching comparison results
//   //     const compareResponse = await fetch(
//   //       `${APIEndpoint}/compareWithDetailsUnit?messageId=${formData.msgId}`,
//   //       {
//   //         method: "POST",
//   //         headers: {
//   //           "Content-Type": "application/json",
//   //           Authorization: token ? `Bearer ${token}` : "",
//   //         },
//   //         body: JSON.stringify({}), // empty or include body as needed
//   //       }
//   //     );

//   //     if (!compareResponse.ok) {
//   //       const text = await compareResponse.text();
//   //       throw new Error(`Error ${compareResponse.status}: ${text}`);
//   //     }

//   //     const compareData = await compareResponse.json();
//   //     console.log("Comparison result:", compareData);

//   //     // Store comparison results in state and show the results page
//   //     setComparisonResult(compareData); // Make sure you create this state
//   //     setShowResults(true);
//   //   } catch (error) {
//   //     console.error("Submission error:", error);
//   //     alert(`Submission failed: ${error.message}`);
//   //   }
//   // };
//   return (
//     <div>BoomiLoginPage</div>
//   )
// }

// export default BoomiLoginPage




import React from "react";
import { Card, CardContent } from "@mui/material";
import ConstructionIcon from "@mui/icons-material/Construction";
import "./Boomi.css"; // Make sure it includes your base styling

const BoomiLoginPage = () => {
  return (
    <div className="placeholder-page">
      <Card className="placeholder-card" elevation={3}>
        <CardContent className="placeholder-content">
          <ConstructionIcon fontSize="large" color="primary" />
          <h2>Boomi Login Module</h2>
          <p>This module is currently under development.</p>
          <p>Please check back later or navigate using the menu.</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default BoomiLoginPage;
