import React, { useState } from "react";
import "./ComparePayloads.css";

const SinglePayload = ({ selection, onBack, onReset }) => {
  const [inputFile, setInputFile] = useState(null);
  const [sapPOFile, setSapPOFile] = useState(null);
  const [sapCPIFile, setSapCPIFile] = useState(null);

  const handleFileChange = (e, setter) => {
    const file = e.target.files[0];
    if (file && file.type === "application/zip") {
      setter(file);
    } else {
      alert("Please upload a .zip file.");
    }
  };

  const handleRun = async () => {
    if (!sapPOFile || !sapCPIFile) {
      alert("Please upload both SAP PO and SAP IS files before running.");
      return;
    }

    const formData = new FormData();
    if (inputFile) formData.append("inputFile", inputFile);
    formData.append("sapPOFile", sapPOFile);
    formData.append("sapCPIFile", sapCPIFile);

    try {
      const response = await fetch("/api/run-single-test", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        alert("Single test execution started:\n" + JSON.stringify(result));
      } else {
        alert("Error starting single test.");
      }
    } catch (error) {
      console.error("Run Error:", error);
      alert("Something went wrong!");
    }
  };

  return (
    <>
      <h3 className="upload-heading">Single Payload Test - {selection}</h3>
      
      <div className="navigation-buttons">
        <button className="back-button" onClick={onBack}>
          Back to Test Type
        </button>
        <button className="reset-button" onClick={onReset}>
          Start Over
        </button>
      </div>

      <table className="upload-table">
        <thead>
          <tr>
            <th>Upload Input Payload (in Zip)</th>
            <th>Upload SAP PO Payload (in Zip)</th>
            <th>Upload SAP IS Payload (in Zip)</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              <label className="upload-button">
                Upload ZIP
                <input
                  type="file"
                  accept=".zip"
                  hidden
                  onChange={(e) => handleFileChange(e, setInputFile)}
                />
              </label>
              <div className="file-name">{inputFile?.name}</div>
            </td>
            <td>
              <label className="upload-button">
                Upload ZIP
                <input
                  type="file"
                  accept=".zip"
                  hidden
                  onChange={(e) => handleFileChange(e, setSapPOFile)}
                />
              </label>
              <div className="file-name">{sapPOFile?.name}</div>
            </td>
            <td>
              <label className="upload-button">
                Upload ZIP
                <input
                  type="file"
                  accept=".zip"
                  hidden
                  onChange={(e) => handleFileChange(e, setSapCPIFile)}
                />
              </label>
              <div className="file-name">{sapCPIFile?.name}</div>
            </td>
          </tr>
        </tbody>
      </table>

      <div className="run-section">
        <button className="run-button" onClick={handleRun}>
          Run Single Test
        </button>
      </div>

      {/* <footer className="footer">
        <p>
          <a href="#">Welcome to IntSwitch Testing Tool and copyright Information</a>
        </p>
        <p>
          <a href="#" className="intswitch">IntSwitch</a> Testing tool for testing data migrated from Non-SAP and SAP based on Premise / On cloud middleware to SAP Integration Suite's Cloud Integration Capability
        </p>
      </footer> */}
    </>
  );
};

export default SinglePayload;