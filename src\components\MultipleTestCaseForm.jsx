// // import React, { useState, useEffect } from "react";
// // import { useNavigate } from "react-router-dom";
// // import POMultipleFormSection from "./POMultipleFormSection";
// // import CPIFormSection from "./CPIFormSection";
// // import TriggerFlowModal from "./TriggerFlowModal";
// // import "../App.css";
// // import APIEndpoint from "../config";
// // import ConfigureSchedulerModal from "./ConfigureSchedulerModal";

// // const MultipleTestCaseForm = ({ formData, setFormData, onBack, onProceed }) => {
// //   const [deployMessage, setDeployMessage] = useState(null);
// //   const [isDeploying, setIsDeploying] = useState(false);
// //   const [showMessage, setShowMessage] = useState(false);
// //   const [showTriggerModal, setShowTriggerModal] = useState(false);
// //   const [showConfigureModal, setShowConfigureModal] = useState(false);

// //   // Check if adapter is not HTTP to enable Configure button
// //   const isConfigureEnabled =
// //     formData.adapterName && formData.adapterName !== "HTTP";

// //   useEffect(() => {
// //     if (deployMessage) {
// //       setShowMessage(true);
// //       const timer = setTimeout(() => {
// //         setShowMessage(false);
// //         setDeployMessage(null);
// //       }, 3000);
// //       return () => clearTimeout(timer);
// //     }
// //   }, [deployMessage]);

// //   const onConfigure = () => {
// //     if (!formData.iFlowName) {
// //       alert("Please select an I-Flow Name before configuring");
// //       return;
// //     }
// //     setShowConfigureModal(true);
// //   };

// //   const onTriggerFlow = () => {
// //     if (!formData.iFlowName) {
// //       alert("Please select an I-Flow Name before triggering the flow");
// //       return;
// //     }
// //     if (!formData.startTime || !formData.endTime) {
// //       alert(
// //         "Please set both Start Time and End Time before triggering the flow"
// //       );
// //       return;
// //     }
// //     setShowTriggerModal(true);
// //   };

// //   const handleDeploy = async () => {
// //     if (!formData.iFlowName || !formData.tenant) {
// //       alert("Please select both I-Flow Name and Tenant before deploying");
// //       return;
// //     }

// //     setIsDeploying(true);
// //     setDeployMessage(null);

// //     try {
// //       const apiTenantName =
// //         formData.tenant.charAt(0).toUpperCase() +
// //         formData.tenant.slice(1).toLowerCase();
// //       const response = await fetch(
// //         `${APIEndpoint}/api/deploy?artifactId=${encodeURIComponent(
// //           formData.iFlowName
// //         )}&configName=${encodeURIComponent(apiTenantName)}`,
// //         {
// //           method: "POST",
// //         }
// //       );

// //       if (!response.ok) {
// //         throw new Error("Failed to initiate deployment");
// //       }

// //       const data = await response.json();
// //       setDeployMessage(
// //         "Deployment started successfully! Your artifact is being processed."
// //       );
// //     } catch (error) {
// //       console.error("Deployment error:", error);
// //       setDeployMessage("Deployment failed. Please try again.");
// //     } finally {
// //       setIsDeploying(false);
// //     }
// //   };

// //   return (
// //     <div>
// //       <div className="single-testcase-content">
// //         <POMultipleFormSection formData={formData} setFormData={setFormData} />
// //         <CPIFormSection formData={formData} setFormData={setFormData} />
// //       </div>

// //       {showMessage && (
// //         <div
// //           style={{
// //             margin: "10px 0",
// //             padding: "15px",
// //             backgroundColor: deployMessage.includes("failed")
// //               ? "#ffebee"
// //               : "#e8f5e9",
// //             border: deployMessage.includes("failed")
// //               ? "1px solid #ef9a9a"
// //               : "1px solid #a5d6a7",
// //             borderRadius: "4px",
// //             color: deployMessage.includes("failed") ? "#c62828" : "#2e7d32",
// //             fontWeight: "500",
// //             textAlign: "center",
// //           }}
// //         >
// //           {deployMessage}
// //         </div>
// //       )}

// //       <div style={{ display: "flex", justifyContent: "space-between" }}>
// //         <div className="back-button-container">
// //           <button onClick={onBack} className="back-button">
// //             ← Back to System Selection
// //           </button>
// //         </div>
// //         <div style={{ display: "flex", justifyContent: "space-around" }}>
// //           <div>
// //             <button
// //               className="proceed-button"
// //               style={{
// //                 marginRight: "10px",
// //                 backgroundColor: !isConfigureEnabled ? "#cccccc" : "", // Gray when disabled
// //                 color: !isConfigureEnabled ? "#666666" : "", // Darker text when disabled
// //                 cursor: !isConfigureEnabled ? "not-allowed" : "pointer", // Not-allowed cursor when disabled
// //               }}
// //               onClick={isConfigureEnabled ? onConfigure : undefined} // Only set onClick when enabled
// //               disabled={!isConfigureEnabled}
// //             >
// //               Configure
// //             </button>
// //           </div>
// //           <div>
// //             <button
// //               style={{ marginRight: "10px" }}
// //               className="proceed-button"
// //               onClick={handleDeploy}
// //               disabled={isDeploying}
// //             >
// //               {isDeploying ? "Deploying..." : "Deploy"}
// //             </button>
// //           </div>
// //           <div>
// //             {/* <button
// //               className="proceed-button"
// //               style={{ marginRight: "10px" }}
// //               onClick={onTriggerFlow}
// //             >
// //               Trigger Flow
// //             </button> */}
// //           </div>
// //           <div>
// //             <button className="proceed-button" onClick={onProceed}>
// //               Proceed →
// //             </button>
// //           </div>
// //         </div>
// //       </div>

// //       <TriggerFlowModal
// //         isOpen={showTriggerModal}
// //         onClose={() => setShowTriggerModal(false)}
// //         iFlowName={formData.iFlowName}
// //         startTime={formData.startTime}
// //         endTime={formData.endTime}
// //       />

// //       <ConfigureSchedulerModal
// //         isOpen={showConfigureModal}
// //         onClose={() => setShowConfigureModal(false)}
// //         iFlowName={formData.iFlowName}
// //       />
// //     </div>
// //   );
// // };

// // export default MultipleTestCaseForm;

// import React, { useState, useEffect } from "react";
// import POMultipleFormSection from "./POMultipleFormSection";
// import CPIFormSection from "./CPIFormSection";
// import "../App.css";
// import APIEndpoint from "../config";
// import ConfigureSchedulerModal from "./ConfigureSchedulerModal";
// import POFormSection from "./POFormSection";
// import ConfigureIDOC from "./ConfigureIDOC";
// import ConfigureHTTP from "./ConfigureHTTP";
// import ConfigureSOAP from "./ConfigureSOAP";

// const MultipleTestCaseForm = ({
//   formData,
//   setFormData,
//   onBack,
//   onProceed,
//   serviceInterfaces,
// }) => {
//   const [deployMessage, setDeployMessage] = useState(null);
//   const [isDeploying, setIsDeploying] = useState(false);
//   const [showMessage, setShowMessage] = useState(false);
//   const [showConfigureModal, setShowConfigureModal] = useState(false);
//   const [showIDOCModal, setShowIDOCModal] = useState(false);
//   const [showHTTPModal, setShowHTTPModal] = useState(false);
//   const [showSOAPModal, setShowSOAPModal] = useState(false);
//   const isConfigureEnabled =
//     formData.adapterName &&
//     (formData.adapterName === "SFTP" || formData.adapterName === "IDOC" || formData.adapterName === "HTTPS"|| formData.adapterName === "SOAP");

//   useEffect(() => {
//     if (deployMessage) {
//       setShowMessage(true);
//       const timer = setTimeout(() => {
//         setShowMessage(false);
//         setDeployMessage(null);
//       }, 3000);
//       return () => clearTimeout(timer);
//     }
//   }, [deployMessage]);

//   const onConfigure = () => {
//     if (!formData.iFlowName) {
//       alert("Please select an I-Flow Name before configuring");
//       return;
//     }
//     // setShowConfigureModal(true);
//     if (formData.adapterName === "SFTP") {
//       setShowConfigureModal(true);
//     } else if (formData.adapterName === "IDOC") {
//       setShowIDOCModal(true);
//     } else if (formData.adapterName === "HTTPS") {
//       setShowHTTPModal(true);
//     }else if (formData.adapterName === "SOAP") {
//       setShowSOAPModal(true);
//     }
//   };

//   const handleDeploy = async () => {
//     if (!formData.iFlowName || !formData.tenant) {
//       alert("Please select both I-Flow Name and Tenant before deploying");
//       return;
//     }

//     setIsDeploying(true);
//     setDeployMessage(null)

//     try {
//       const apiTenantName =
//         formData.tenant.charAt(0).toUpperCase() +
//         formData.tenant.slice(1).toLowerCase();
//       const response = await fetch(
//         `${APIEndpoint}/api/deploy?artifactId=${encodeURIComponent(
//           formData.iFlowName
//         )}&configName=${encodeURIComponent(apiTenantName)}`,
//         {
//           method: "POST",
//         }
//       );

//       if (!response.ok) {
//         throw new Error("Failed to initiate deployment");
//       }

//       const data = await response.json();
//       setDeployMessage(
//         "Deployment started successfully! Your artifact is being processed."
//       );
//     } catch (error) {
//       console.error("Deployment error:", error);
//       setDeployMessage("Deployment failed. Please try again.");
//     } finally {
//       setIsDeploying(false);
//     }
//   };

//   return (
//     <div>
//       <div className="single-testcase-content">
//         <POFormSection
//           formData={formData}
//           setFormData={setFormData}
//           serviceInterfaces={serviceInterfaces}
//         />
//         {/* <POMultipleFormSection formData={formData} setFormData={setFormData} /> */}
//         <CPIFormSection formData={formData} setFormData={setFormData} />
//       </div>

//       {showMessage && (
//         <div
//           style={{
//             margin: "10px 0",
//             padding: "15px",
//             backgroundColor: deployMessage.includes("failed")
//               ? "#ffebee"
//               : "#e8f5e9",
//             border: deployMessage.includes("failed")
//               ? "1px solid #ef9a9a"
//               : "1px solid #a5d6a7",
//             borderRadius: "4px",
//             color: deployMessage.includes("failed") ? "#c62828" : "#2e7d32",
//             fontWeight: "500",
//             textAlign: "center",
//           }}
//         >
//           {deployMessage}
//         </div>
//       )}

//       <div style={{ display: "flex", justifyContent: "space-between" }}>
//         <div className="back-button-container">
//           <button onClick={onBack} className="back-button">
//             ← Back to System Selection
//           </button>
//         </div>
//         <div style={{ display: "flex", justifyContent: "space-around" }}>
//           <div>
//             <button
//               className="proceed-button"
//               style={{
//                 marginRight: "10px",
//                 backgroundColor: !isConfigureEnabled ? "#cccccc" : "",
//                 color: !isConfigureEnabled ? "#666666" : "",
//                 cursor: !isConfigureEnabled ? "not-allowed" : "pointer",
//               }}
//               onClick={isConfigureEnabled ? onConfigure : undefined}
//               disabled={!isConfigureEnabled}
//             >
//               Configure
//             </button>
//           </div>
//           <div>
//             <button
//               style={{ marginRight: "10px" }}
//               className="proceed-button"
//               onClick={handleDeploy}
//               disabled={isDeploying}
//             >
//               {isDeploying ? "Deploying..." : "Deploy"}
//             </button>
//           </div>
//           <div>
//             <button className="proceed-button" onClick={onProceed}>
//               Proceed →
//             </button>
//           </div>
//         </div>
//       </div>

//       {formData.adapterName === "SFTP" && (
//         <ConfigureSchedulerModal
//           isOpen={showConfigureModal}
//           onClose={() => setShowConfigureModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}

//       {formData.adapterName === "IDOC" && (
//         <ConfigureIDOC
//           isOpen={showIDOCModal}
//           onClose={() => setShowIDOCModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}

//       {formData.adapterName === "HTTPS" && (
//         <ConfigureHTTP
//           isOpen={showHTTPModal}
//           onClose={() => setShowHTTPModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}
//       {formData.adapterName === "SOAP" && (
//         <ConfigureSOAP
//           isOpen={showSOAPModal}
//           onClose={() => setShowSOAPModal(false)}
//           iFlowName={formData.iFlowName}
//         />
//       )}
//     </div>
//   );
// };

// export default MultipleTestCaseForm;

import React, { useState, useEffect } from "react";
import POMultipleFormSection from "./POMultipleFormSection";
import CPIFormSection from "./CPIFormSection";
import "../App.css";
import APIEndpoint from "../config";
import ConfigureSchedulerModal from "./ConfigureSchedulerModal";
import POFormSection from "./POFormSection";
import ConfigureIDOC from "./ConfigureIDOC";
import ConfigureHTTP from "./ConfigureHTTP";
import ConfigureSOAP from "./ConfigureSOAP";
import ConfigureAS2 from "./ConfigureAS2";
import ConfigureXI from "./ConfigureXI";
const MultipleTestCaseForm = ({
  formData,
  setFormData,
  onBack,
  onProceed,
  serviceInterfaces,
  onGenerateReport,
}) => {
  const [isProceeding, setIsProceeding] = useState(false);
  const [deployMessage, setDeployMessage] = useState(null);
  const [isDeploying, setIsDeploying] = useState(false);
  const [showMessage, setShowMessage] = useState(false);
  const [showConfigureModal, setShowConfigureModal] = useState(false);
  const [showIDOCModal, setShowIDOCModal] = useState(false);
  const [showXIModal, setShowXIModal] = useState(false);
  const [showHTTPModal, setShowHTTPModal] = useState(false);
  const [showSOAPModal, setShowSOAPModal] = useState(false);
  const [showAS2Modal, setShowAS2Modal] = useState(false);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  const handleGenerateReportClick = async () => {
    setIsGeneratingReport(true);
    try {
      await onGenerateReport();
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const isConfigureEnabled =
    formData.adapterName &&
    (formData.adapterName === "SFTP" ||
      formData.adapterName === "IDOC" ||
      formData.adapterName === "HTTPS" ||
      formData.adapterName === "AS2" ||
      formData.adapterName === "XI" ||
      formData.adapterName === "SOAP");

  // Button styles matching Dashboard component
  const getButtonStyle = (type = "default", disabled = false) => {
    const baseStyle = {
      backgroundColor: disabled ? "#f3f4f6" : "white",
      color: disabled ? "#9ca3af" : "#374151",
      borderRadius: "8px",
      fontWeight: "500",
      fontSize: "14px",
      border: "1px solid #e5e7eb",
      padding: "12px 16px",
      cursor: disabled ? "not-allowed" : "pointer",
      transition: "all 0.3s ease",
      boxShadow: disabled
        ? "0 1px 2px rgba(0, 0, 0, 0.05)"
        : "0 1px 3px rgba(0, 0, 0, 0.1)",
      textAlign: "center",
      minWidth: "120px",
    };

    // Specific styles for different button types
    switch (type) {
      case "back":
        return {
          ...baseStyle,
          backgroundColor: "#f8fafc",
          borderColor: "#cbd5e1",
          color: "#475569",
        };
      case "proceed":
        return {
          ...baseStyle,
          backgroundColor: disabled ? "#f3f4f6" : "#e5e7eb",
          fontWeight: "600",
        };
      default:
        return baseStyle;
    }
  };

  const containerStyle = {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: "20px",
    padding: "20px 0",
  };

  const buttonGroupStyle = {
    display: "flex",
    gap: "10px",
    alignItems: "center",
  };

  useEffect(() => {
    if (deployMessage) {
      setShowMessage(true);
      const timer = setTimeout(() => {
        setShowMessage(false);
        setDeployMessage(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [deployMessage]);

  const onConfigure = () => {
    if (!formData.iFlowName) {
      alert("Please select an I-Flow Name before configuring");
      return;
    }
    if (formData.adapterName === "SFTP") {
      setShowConfigureModal(true);
    } else if (formData.adapterName === "IDOC") {
      setShowIDOCModal(true);
    } else if (formData.adapterName === "HTTPS") {
      setShowHTTPModal(true);
    } else if (formData.adapterName === "SOAP") {
      setShowSOAPModal(true);
    }else if (formData.adapterName === "AS2") {
      setShowAS2Modal(true);
    }
    else if (formData.adapterName === "XI") {
      setShowXIModal(true);
    }
  };

  const handleDeploy = async () => {
    if (!formData.iFlowName || !formData.tenant) {
      alert("Please select both I-Flow Name and Tenant before deploying");
      return;
    }

    setIsDeploying(true);
    setDeployMessage(null);

    try {
      const apiTenantName =
        formData.tenant.charAt(0).toUpperCase() +
        formData.tenant.slice(1).toLowerCase();
      const response = await fetch(
        `${APIEndpoint}/api/deploy?artifactId=${encodeURIComponent(
          formData.iFlowName
        )}&configName=${encodeURIComponent(apiTenantName)}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Basic ${localStorage.getItem("basicAuth")}`
          }
        }
      );

      if (!response.ok) {
        throw new Error("Failed to initiate deployment");
      }

      const data = await response.json();
      setDeployMessage(
        "Deployment started successfully! Your artifact is being processed."
      );
    } catch (error) {
      console.error("Deployment error:", error);
      setDeployMessage("Deployment failed. Please try again.");
    } finally {
      setIsDeploying(false);
    }
  };

  const handleProceedClick = async () => {
    setIsProceeding(true);
    try {
      await onProceed();
    } finally {
      setIsProceeding(false);
    }
  };
  return (
    <div>
      <div className="single-testcase-content">
        <POFormSection
          formData={formData}
          setFormData={setFormData}
          serviceInterfaces={serviceInterfaces}
        />
        <CPIFormSection formData={formData} setFormData={setFormData} />
      </div>

      {showMessage && (
        <div
          style={{
            margin: "10px 0",
            padding: "15px",
            backgroundColor: deployMessage.includes("failed")
              ? "#ffebee"
              : "#e8f5e9",
            border: deployMessage.includes("failed")
              ? "1px solid #ef9a9a"
              : "1px solid #a5d6a7",
            borderRadius: "8px",
            color: deployMessage.includes("failed") ? "#c62828" : "#2e7d32",
            fontWeight: "500",
            textAlign: "center",
          }}
        >
          {deployMessage}
        </div>
      )}

      <div style={containerStyle}>
        <div>
          <button
            onClick={onBack}
            style={getButtonStyle("back")}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = "#e2e8f0";
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = "#f8fafc";
            }}
          >
            ← Back to System Selection
          </button>
        </div>

        <div style={buttonGroupStyle}>
          <button
            style={getButtonStyle("default", !isConfigureEnabled)}
            onClick={isConfigureEnabled ? onConfigure : undefined}
            disabled={!isConfigureEnabled}
            onMouseEnter={(e) => {
              if (isConfigureEnabled) {
                e.target.style.backgroundColor = "#f9fafb";
              }
            }}
            onMouseLeave={(e) => {
              if (isConfigureEnabled) {
                e.target.style.backgroundColor = "white";
              }
            }}
          >
            Configure
          </button>

          <button
            style={getButtonStyle("default", isDeploying)}
            onClick={handleDeploy}
            disabled={isDeploying}
            onMouseEnter={(e) => {
              if (!isDeploying) {
                e.target.style.backgroundColor = "#f9fafb";
              }
            }}
            onMouseLeave={(e) => {
              if (!isDeploying) {
                e.target.style.backgroundColor = "white";
              }
            }}
          >
            {isDeploying ? "Deploying..." : "Deploy"}
          </button>

          <button
            style={getButtonStyle("proceed", isProceeding)}
            onClick={handleProceedClick}
            disabled={isProceeding}
            onMouseEnter={(e) => {
              if (!isProceeding) {
                e.target.style.backgroundColor = "#d1d5db";
              }
            }}
            onMouseLeave={(e) => {
              if (!isProceeding) {
                e.target.style.backgroundColor = "#e5e7eb";
              }
            }}
          >
            {isProceeding ? (
              <span
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <svg
                  className="spinner"
                  viewBox="0 0 50 50"
                  style={{ width: "20px", height: "20px", marginRight: "8px" }}
                >
                  <circle
                    className="path"
                    cx="25"
                    cy="25"
                    r="20"
                    fill="none"
                    strokeWidth="5"
                  ></circle>
                </svg>
                Processing...
              </span>
            ) : (
              "Extract Data →"
            )}
          </button>
          <button
            style={getButtonStyle("generate", isGeneratingReport)}
            onClick={handleGenerateReportClick}
            disabled={isGeneratingReport}
            onMouseEnter={(e) => {
              if (!isGeneratingReport) {
                e.target.style.backgroundColor = "#bbf7d0";
              }
            }}
            onMouseLeave={(e) => {
              if (!isGeneratingReport) {
                e.target.style.backgroundColor = "#dcfce7";
              }
            }}
          >
            {isGeneratingReport ? (
              <span
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <svg
                  className="spinner"
                  viewBox="0 0 50 50"
                  style={{ width: "20px", height: "20px", marginRight: "8px" }}
                >
                  <circle
                    className="path"
                    cx="25"
                    cy="25"
                    r="20"
                    fill="none"
                    strokeWidth="5"
                  ></circle>
                </svg>
                Generating...
              </span>
            ) : (
              "📊 Generate Report"
            )}
          </button>
        </div>
      </div>

      {formData.adapterName === "SFTP" && (
        <ConfigureSchedulerModal
          isOpen={showConfigureModal}
          onClose={() => setShowConfigureModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "IDOC" && (
        <ConfigureIDOC
          isOpen={showIDOCModal}
          onClose={() => setShowIDOCModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "HTTPS" && (
        <ConfigureHTTP
          isOpen={showHTTPModal}
          onClose={() => setShowHTTPModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}
      {formData.adapterName === "XI" && (
        <ConfigureXI
          isOpen={showXIModal}
          onClose={() => setShowXIModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}

      {formData.adapterName === "SOAP" && (
        <ConfigureSOAP
          isOpen={showSOAPModal}
          onClose={() => setShowSOAPModal(false)}
          iFlowName={formData.iFlowName}
        />
      )}
      {formData.adapterName === "AS2" && (
        <ConfigureAS2
          isOpen={showAS2Modal}
          onClose={() => setShowAS2Modal(false)}
          iFlowName={formData.iFlowName}
        />
      )}
      <style jsx>{`
        .spinner {
          animation: rotate 2s linear infinite;
        }
        .spinner .path {
          stroke: #374151;
          stroke-linecap: round;
          animation: dash 1.5s ease-in-out infinite;
        }
        @keyframes rotate {
          100% {
            transform: rotate(360deg);
          }
        }
        @keyframes dash {
          0% {
            stroke-dasharray: 1, 150;
            stroke-dashoffset: 0;
          }
          50% {
            stroke-dasharray: 90, 150;
            stroke-dashoffset: -35;
          }
          100% {
            stroke-dasharray: 90, 150;
            stroke-dashoffset: -124;
          }
        }
      `}</style>
    </div>
  );
};

export default MultipleTestCaseForm;
