import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import APIEndpoint from "../config";
import axios from "axios";

const CPIFormSection = ({ formData, setFormData }) => {
  const navigate = useNavigate();
  const [packages, setPackages] = useState([]);
  const [iFlows, setIFlows] = useState([]);
  const [loadingPackages, setLoadingPackages] = useState(false);
  const [loadingIFlows, setLoadingIFlows] = useState(false);
  
  // Search functionality states
  const [packageSearchTerm, setPackageSearchTerm] = useState("");
  const [showPackageDropdown, setShowPackageDropdown] = useState(false);
  const [filteredPackages, setFilteredPackages] = useState([]);
  const packageDropdownRef = useRef(null);

  // Function to convert tenant value for API (DEV -> Dev, QA -> Qa)
  const getApiTenantName = (tenant) => {
    if (!tenant) return tenant;
    return tenant.charAt(0).toUpperCase() + tenant.slice(1).toLowerCase();
  };

  // Filter packages based on search term
  useEffect(() => {
    if (packageSearchTerm) {
      const filtered = packages.filter(pkg =>
        pkg.name.toLowerCase().includes(packageSearchTerm.toLowerCase())
      );
      setFilteredPackages(filtered);
    } else {
      setFilteredPackages(packages);
    }
  }, [packageSearchTerm, packages]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (packageDropdownRef.current && !packageDropdownRef.current.contains(event.target)) {
        setShowPackageDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const fetchPackages = async () => {
      if (!formData.tenant) return;

      setLoadingPackages(true);
      try {
        const apiTenantName = getApiTenantName(formData.tenant);
        const response = await axios.get(
          `${APIEndpoint}/packages`,
          { 
            params: { configName: apiTenantName },
            headers: {
              'Content-Type': 'application/json',
            }
          }
        );
        
        console.log("Raw API response:", response.data);
        
        // Extract package data from the response structure
        const packageKeys = Object.keys(response.data || {});
        
        // Create packages array with packageId for both display and value
        const packagesArray = packageKeys.map(key => ({
          id: key,
          packageId: response.data[key].packageId,
          name: response.data[key].packageId
        }));
        
        console.log("Processed packages:", packagesArray);
        setPackages(packagesArray);
        
        // Reset package and IFlow selections when tenant changes
        setFormData(prev => ({
          ...prev,
          packageName: "",
          iFlowName: ""
        }));
        setIFlows([]);
        setPackageSearchTerm("");
      } catch (error) {
        console.error("Error fetching packages:", error);
      } finally {
        setLoadingPackages(false);
      }
    };

    fetchPackages();
  }, [formData.tenant, setFormData]);

  // Update the IFlow fetch to use the correct packageId
  useEffect(() => {
    const fetchIFlows = async () => {
      if (!formData.packageName || !formData.tenant) return;

      setLoadingIFlows(true);
      try {
        const apiTenantName = getApiTenantName(formData.tenant);
        const response = await axios.get(
          `${APIEndpoint}/api/integration/artifacts`,
          { 
            params: { 
              packageId: formData.packageName,
              configName: apiTenantName 
            },
            headers: {
              'Content-Type': 'application/json',
            }
          }
        );
        
        console.log("IFlow API response:", response.data);
        
        // Extract IFlow names from the response structure
        const iFlowData = response.data.body || {};
        const iFlowNames = Object.keys(iFlowData).map(key => ({
          id: key,
          name: iFlowData[key].artifactName
        }));
        setIFlows(iFlowNames);
        
        // Reset IFlow selection when package changes
        setFormData(prev => ({
          ...prev,
          iFlowName: ""
        }));
      } catch (error) {
        console.error("Error fetching IFlows:", error);
      } finally {
        setLoadingIFlows(false);
      }
    };

    fetchIFlows();
  }, [formData.packageName, formData.tenant, setFormData]);

  const handlePackageSelect = (packageName) => {
    setFormData({
      ...formData,
      packageName: packageName,
    });
    setPackageSearchTerm(packageName);
    setShowPackageDropdown(false);
  };

  const handlePackageInputChange = (e) => {
    const value = e.target.value;
    setPackageSearchTerm(value);
    setShowPackageDropdown(true);
    
    // If the input is cleared, also clear the selected package
    if (!value) {
      setFormData({
        ...formData,
        packageName: "",
        iFlowName: ""
      });
    }
  };

  console.log("formData", formData);

  return (
    <div className="testcase-section">
      <h5 style={{ display: "flex", marginBottom: "10px" ,marginTop:"20px"}}>Please mention the required details from SAP Integration suite</h5>
      <table>
        <thead>
          <tr style={{ backgroundColor: '#272D4F', color: 'white' }}>
            <th style={{ backgroundColor: '#272D4F', padding: '10px', border: '1px solid #ccc', textAlign: 'left' }}>SAP Integration suite</th>
            <th style={{ backgroundColor: '#272D4F', padding: '10px', border: '1px solid #ccc', textAlign: 'left' }}>Enter details</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Tenant</td>
            <td>
              <select
                value={formData.tenant}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    tenant: e.target.value,
                  })
                }
                required
              >
                <option value="">Select Tenant</option>
                <option value="Dev">DEV</option>
                <option value="QA">QA</option>
              </select>
            </td>
          </tr>
          <tr>
            <td>Package Name</td>
            <td>
              <div style={{ position: 'relative' }} ref={packageDropdownRef}>
                <input
                  type="text"
                  value={packageSearchTerm}
                  onChange={handlePackageInputChange}
                  onFocus={() => setShowPackageDropdown(true)}
                  placeholder={loadingPackages ? "Loading packages..." : "Search and select package"}
                  disabled={loadingPackages || !formData.tenant}
                  required
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ccc',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                />
                {showPackageDropdown && !loadingPackages && filteredPackages.length > 0 && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: 0,
                    right: 0,
                    backgroundColor: 'white',
                    border: '1px solid #ccc',
                    borderTop: 'none',
                    maxHeight: '200px',
                    overflowY: 'auto',
                    zIndex: 1000,
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                  }}>
                    {filteredPackages.map((pkg) => (
                      <div
                        key={pkg.id}
                        onClick={() => handlePackageSelect(pkg.name)}
                        style={{
                          padding: '8px 12px',
                          cursor: 'pointer',
                          borderBottom: '1px solid #eee',
                          backgroundColor: 'white',
                          fontSize: '14px'
                        }}
                        onMouseEnter={(e) => e.target.style.backgroundColor = '#f5f5f5'}
                        onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
                      >
                        {pkg.name}
                      </div>
                    ))}
                  </div>
                )}
                {showPackageDropdown && !loadingPackages && packageSearchTerm && filteredPackages.length === 0 && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: 0,
                    right: 0,
                    backgroundColor: 'white',
                    border: '1px solid #ccc',
                    borderTop: 'none',
                    padding: '8px 12px',
                    color: '#666',
                    fontSize: '14px',
                    zIndex: 1000
                  }}>
                    No packages found
                  </div>
                )}
              </div>
            </td>
          </tr>
          <tr>
            <td>I-Flow Name</td>
            <td>
              <select
                value={formData.iFlowName}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    iFlowName: e.target.value,
                  })
                }
                required
                disabled={loadingIFlows || !formData.packageName}
              >
                <option value="">{loadingIFlows ? "Loading IFlows..." : "Select IFlow"}</option>
                {iFlows.map((flow) => (
                  <option key={flow.id} value={flow.name}>
                    {flow.name}
                  </option>
                ))}
              </select>
            </td>
          </tr>
          <tr>
            <td>Sender Adapter</td>
            <td>
              <select
                value={formData.adapterName}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    adapterName: e.target.value,
                  })
                }
                required
              >
                <option value="">Select Adapter</option>
                <option value="AS2"> AS2</option>
                <option value="HTTPS"> HTTPS</option>
                <option value="IDOC">IDOC</option>
                <option value="SFTP">SFTP</option>
                <option value="SOAP">SOAP</option>
                <option value="JMS">JMS</option>
                <option value="Mail">MAIL</option>
                <option value="PROCESS_DIRECT">PROCESS DIRECT</option>
                <option value="XI">XI</option>
              </select>
            </td>
          </tr>
          <tr>
            <td colSpan="2">
              <label>
                If you are not a registered user please register here{" "}
                <a 
                  href="#register" 
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/dashboard/register');
                  }}
                  style={{color: 'blue', cursor: 'pointer'}}
                >
                  Register
                </a>
              </label>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default CPIFormSection;