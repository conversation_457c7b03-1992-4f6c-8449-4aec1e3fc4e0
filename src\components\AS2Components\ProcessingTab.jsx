import React, { useState } from "react";
import { MenuItem, Select, FormControl, Checkbox } from "@mui/material";
import API_ENDPOINT from "../../config";

const ProcessingTab = ({ onClose, setSnackbar, artifactId }) => {
  const [processingData, setProcessingData] = useState({
    partnerIdSource: { param: "", value: "Authorized User" },
    messageIdLeftPart: { param: "", value: "" },
    messageIdRightPart: { param: "", value: "" },
    partnerAs2Id: { param: "", value: "" },
    ownAs2Id: { param: "", value: "" },
    messageSubject: { param: "", value: "" },
    numberOfConcurrentProcesses: { param: "", value: "" },
    mandatoryFileName: { param: "", value: false },
    duplicateMessageId: { param: "", value: false },
    duplicateFileName: { param: "", value: false },
  });
  const [loading, setLoading] = useState(false);

  const handleProcessingChange = (e) => {
    const { name, value, type, checked } = e.target;
    setProcessingData((prev) => ({
      ...prev,
      [name]: { ...prev[name], value: type === 'checkbox' ? checked : value },
    }));
  };

  const handleParamChange = (e) => {
    const { name, value } = e.target;
    setProcessingData((prev) => ({
      ...prev,
      [name]: { ...prev[name], param: value },
    }));
  };

  const handleCancel = () => {
    if (onClose) onClose();
  };

  const handleSubmit = async () => {
    setLoading(true);
    
    try {
      // Create an array of all parameter updates
      const updates = [
        { name: processingData.partnerIdSource.param, value: processingData.partnerIdSource.value },
        { name: processingData.messageIdLeftPart.param, value: processingData.messageIdLeftPart.value },
        { name: processingData.messageIdRightPart.param, value: processingData.messageIdRightPart.value },
        { name: processingData.partnerAs2Id.param, value: processingData.partnerAs2Id.value },
        { name: processingData.ownAs2Id.param, value: processingData.ownAs2Id.value },
        { name: processingData.messageSubject.param, value: processingData.messageSubject.value },
        { name: processingData.numberOfConcurrentProcesses.param, value: processingData.numberOfConcurrentProcesses.value },
        { name: processingData.mandatoryFileName.param, value: processingData.mandatoryFileName.value },
        { name: processingData.duplicateMessageId.param, value: processingData.duplicateMessageId.value },
        { name: processingData.duplicateFileName.param, value: processingData.duplicateFileName.value }
      ].filter(update => update.name && (update.value !== "" && update.value !== undefined)); // Only include parameters with names and values

      if (updates.length === 0) {
        setSnackbar({
          open: true,
          message: "Please enter at least one parameter name and value",
          severity: "error"
        });
        setLoading(false);
        return;
      }

      // Execute all updates sequentially
      let lastResponse = null;
      for (const update of updates) {
        const endpoint = `${API_ENDPOINT}/api/updateParam/${artifactId}/${update.name}`;
        
        const response = await fetch(endpoint, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            parameterValue: update.value,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update parameter ${update.name}`);
        }
        
        // Store the last successful response
        lastResponse = await response.json();
      }

      // Show the success message from the API response
      if (lastResponse && lastResponse.status) {
        setSnackbar({
          open: true,
          message: lastResponse.status,
          severity: "success"
        });
      } else {
        setSnackbar({
          open: true,
          message: "Parameters updated successfully",
          severity: "success"
        });
      }
    } catch (error) {
      console.error("Error updating parameters:", error);
      setSnackbar({
        open: true,
        message: error.message || "Failed to update some parameters",
        severity: "error"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h4 style={{ 
        display: 'flex',
        alignItems: 'center',
        color: '#6c757d', 
        fontSize: '14px', 
        fontWeight: 'bold',
        marginBottom: '20px',
        textTransform: 'uppercase'
      }}>
        PARTNER ID RESOLUTION
      </h4>
      
      <div style={{ marginBottom: '30px' }}>
        <div style={{ display: 'grid', gridTemplateColumns: '200px 1fr 200px', gap: '10px', alignItems: 'center', marginBottom: '20px' }}>
          <label style={{ color: '#333', fontSize: '14px' }}>Source:</label>
          <input
            type="text"
            name="partnerIdSource"
            value={processingData.partnerIdSource.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <FormControl size="small">
            <Select
              name="partnerIdSource"
              value={processingData.partnerIdSource.value}
              onChange={handleProcessingChange}
              style={{ fontSize: '14px' }}
            >
              <MenuItem value="AS2 Partner ID Header">AS2 Partner ID Header</MenuItem>
              <MenuItem value="Authorized User">Authorized User</MenuItem>
              <MenuItem value="Dynamic">Dynamic</MenuItem>
            </Select>
          </FormControl>
        </div>
      </div>

      <h4 style={{ 
        display: 'flex',
        color: '#6c757d', 
        fontSize: '14px', 
        fontWeight: 'bold',
        marginBottom: '20px',
        textTransform: 'uppercase'
      }}>
        EXPECTED MESSAGES
      </h4>
      
      <div style={{ display: 'grid', gap: '20px', marginBottom: '30px' }}>
        {[
          { label: "Message ID Left Part:", name: "messageIdLeftPart", required: true },
          { label: "Message ID Right Part:", name: "messageIdRightPart", required: true },
          { label: "Partner AS2 ID:", name: "partnerAs2Id", required: true },
          { label: "Own AS2 ID:", name: "ownAs2Id", required: true },
          { label: "Message Subject:", name: "messageSubject", required: true },
          { label: "Number of Concurrent Processes:", name: "numberOfConcurrentProcesses", required: true }
        ].map((field) => (
          <div key={field.name} style={{ display: 'grid', gridTemplateColumns: '200px 1fr 200px', gap: '10px', alignItems: 'center' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>
              {field.label} {field.required && <span style={{ color: 'red' }}>*</span>}
            </label>
            <input
              type="text"
              name={field.name}
              value={processingData[field.name].param}
              onChange={handleParamChange}
              placeholder="Define Parameter"
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <input
              type="text"
              name={field.name}
              value={processingData[field.name].value}
              onChange={handleProcessingChange}
              placeholder="Define Value"
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
          </div>
        ))}
      </div>

      <h4 style={{ 
        display: 'flex',
        color: '#6c757d', 
        fontSize: '14px', 
        fontWeight: 'bold',
        marginBottom: '20px',
        textTransform: 'uppercase'
      }}>
        MESSAGE SETTINGS
      </h4>
      
      <div style={{ display: 'grid', gap: '20px' }}>
        {[
          { label: "Mandatory File Name:", name: "mandatoryFileName", type: "checkbox" },
          { label: "Duplicate Message ID:", name: "duplicateMessageId", type: "checkbox" },
          { label: "Duplicate File Name:", name: "duplicateFileName", type: "checkbox" }
        ].map((field) => (
          <div key={field.name} style={{ display: 'grid', gridTemplateColumns: '200px 1fr 50px', gap: '10px', alignItems: 'center' }}>
            <label style={{ color: '#333', fontSize: '14px' }}>{field.label}</label>
            <input
              type="text"
              name={field.name}
              value={processingData[field.name].param}
              onChange={handleParamChange}
              placeholder="Define Parameter"
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            <Checkbox
              name={field.name}
              checked={processingData[field.name].value}
              onChange={handleProcessingChange}
              size="small"
            />
          </div>
        ))}
      </div>

      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '10px',
        padding: '15px 20px',
        borderTop: '1px solid #dee2e6',
        backgroundColor: '#f8f9fa',
        marginTop: '20px'
      }}>
        <button
          onClick={handleSubmit}
          disabled={loading}
          style={{
            padding: '8px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer',
            fontWeight: 'bold',
            opacity: loading ? 0.7 : 1
          }}
        >
          {loading ? 'Updating...' : 'OK'}
        </button>
        <button
          onClick={handleCancel}
          style={{
            padding: '8px 20px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default ProcessingTab;