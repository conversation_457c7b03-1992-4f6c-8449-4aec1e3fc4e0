import React from "react";

const ComparisonDetailsModal = ({ selectedComparison, onClose, formdata }) => {
  if (!selectedComparison) return null;
  
  // Safely get differences array with default empty array
  const differences = selectedComparison.differences || [];
  
  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <h3>Detailed Comparison:</h3>
        <h4>Message Id: {selectedComparison.messageId ? selectedComparison.messageId : "n/a"}</h4>
        <h4>Flow Name: {formdata?.iFlowName ?? "n/a"}</h4>
        <button className="close-modal" onClick={onClose}>
          ×
        </button>

        <div className="comparison-details">
          {selectedComparison.status === "Cannot be processed" ? (
            <div>
              <h4>Processing Error:</h4>
              <p>{selectedComparison.reason || "Unknown error"}</p>
            </div>
          ) : differences.length > 0 ? (
            <div>
              <h4>Differences Found:</h4>
              <table className="differences-table">
                <thead>
                  <tr>
                    <th>Element</th>
                    <th>XPath</th>
                    <th>Expected Value</th>
                    <th>Actual Value</th>
                  </tr>
                </thead>
                <tbody>
                  {differences.map((diff, idx) => (
                    <tr key={idx}>
                      <td>{diff.elementName}</td>
                      <td>{diff.xpath}</td>
                      <td>{diff.expectedValue}</td>
                      <td>{diff.actualValue}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p>No differences found (full match).</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ComparisonDetailsModal;