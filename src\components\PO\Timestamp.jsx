import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { 
  FaClock, 
  FaCalendarAlt, 
  FaArrowRight, 
  FaGlobe,
  FaMapMarkerAlt,
  FaUser,
  FaRefresh
} from "react-icons/fa";

const TimestampPage = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [timezone, setTimezone] = useState(Intl.DateTimeFormat().resolvedOptions().timeZone);
  const [userInfo, setUserInfo] = useState({
    sessionId: '',
    ipAddress: '',
    userAgent: ''
  });
  const navigate = useNavigate();

  useEffect(() => {
    // Update time every second
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // Generate session info
    setUserInfo({
      sessionId: Math.random().toString(36).substr(2, 9).toUpperCase(),
      ipAddress: 'Detecting...', // In real app, you'd get this from API
      userAgent: navigator.userAgent.split(' ')[0]
    });

    // Simulate IP detection
    setTimeout(() => {
      setUserInfo(prev => ({
        ...prev,
        ipAddress: '192.168.1.' + Math.floor(Math.random() * 254 + 1)
      }));
    }, 1500);

    return () => clearInterval(timer);
  }, []);

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: true,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const handleProceedToLogin = () => {
    // Store timestamp info in sessionStorage or pass as state
    const timestampData = {
      accessTime: currentTime.toISOString(),
      timezone: timezone,
      sessionId: userInfo.sessionId
    };
    
    // You can store this data or pass it to the next page
    sessionStorage.setItem('timestampData', JSON.stringify(timestampData));
    
    // Navigate to PO Login page
    navigate('/po-login'); // Adjust path as needed
  };

  const refreshSession = () => {
    setUserInfo(prev => ({
      ...prev,
      sessionId: Math.random().toString(36).substr(2, 9).toUpperCase()
    }));
  };

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <div style={styles.header}>
          <div style={styles.logo}>
            <FaClock size={40} color="#084a94" />
          </div>
          <h1 style={styles.title}>Access Timestamp</h1>
          <p style={styles.subtitle}>Please verify your session details before proceeding</p>
        </div>

        <div style={styles.timestampSection}>
          <div style={styles.timeDisplay}>
            <div style={styles.currentTime}>
              {formatTime(currentTime)}
            </div>
            <div style={styles.currentDate}>
              {formatDate(currentTime)}
            </div>
          </div>
        </div>

        <div style={styles.infoGrid}>
          <div style={styles.infoCard}>
            <div style={styles.infoHeader}>
              <FaGlobe style={styles.infoIcon} />
              <span style={styles.infoLabel}>Timezone</span>
            </div>
            <div style={styles.infoValue}>{timezone}</div>
          </div>

          <div style={styles.infoCard}>
            <div style={styles.infoHeader}>
              <FaUser style={styles.infoIcon} />
              <span style={styles.infoLabel}>Session ID</span>
              <FaRefresh 
                style={styles.refreshIcon} 
                onClick={refreshSession}
                title="Generate new session ID"
              />
            </div>
            <div style={styles.infoValue}>{userInfo.sessionId}</div>
          </div>

          <div style={styles.infoCard}>
            <div style={styles.infoHeader}>
              <FaMapMarkerAlt style={styles.infoIcon} />
              <span style={styles.infoLabel}>IP Address</span>
            </div>
            <div style={styles.infoValue}>{userInfo.ipAddress}</div>
          </div>
        </div>

        <div style={styles.footerInfo}>
          <p style={styles.disclaimerText}>
            This timestamp will be recorded for audit purposes. 
            Please ensure your system time is accurate before proceeding.
          </p>
        </div>

        <div style={styles.buttonContainer}>
          <button
            onClick={handleProceedToLogin}
            style={styles.proceedButton}
          >
            <span>Proceed to PO Login</span>
            <FaArrowRight style={styles.buttonIcon} />
          </button>
        </div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    minHeight: "100vh",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f1f5f9",
    fontFamily: "'Segoe UI', 'Roboto', 'Oxygen', sans-serif",
    padding: "20px"
  },
  card: {
    width: "100%",
    maxWidth: "500px",
    backgroundColor: "white",
    borderRadius: "16px",
    boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
    padding: "40px",
    textAlign: "center"
  },
  header: {
    marginBottom: "32px"
  },
  logo: {
    marginBottom: "20px",
    display: "flex",
    justifyContent: "center"
  },
  title: {
    fontSize: "28px",
    fontWeight: "700",
    color: "#1e293b",
    marginBottom: "8px",
    margin: 0
  },
  subtitle: {
    fontSize: "16px",
    color: "#64748b",
    margin: 0,
    lineHeight: "1.5"
  },
  timestampSection: {
    backgroundColor: "#f8fafc",
    borderRadius: "12px",
    padding: "24px",
    marginBottom: "32px",
    border: "2px solid #e2e8f0"
  },
  timeDisplay: {
    textAlign: "center"
  },
  currentTime: {
    fontSize: "36px",
    fontWeight: "700",
    color: "#084a94",
    marginBottom: "8px",
    fontFamily: "'Monaco', 'Menlo', monospace"
  },
  currentDate: {
    fontSize: "18px",
    color: "#475569",
    fontWeight: "500"
  },
  infoGrid: {
    display: "grid",
    gap: "16px",
    marginBottom: "32px"
  },
  infoCard: {
    backgroundColor: "#f8fafc",
    borderRadius: "8px",
    padding: "16px",
    border: "1px solid #e2e8f0",
    textAlign: "left"
  },
  infoHeader: {
    display: "flex",
    alignItems: "center",
    marginBottom: "8px",
    position: "relative"
  },
  infoIcon: {
    width: "16px",
    height: "16px",
    color: "#64748b",
    marginRight: "8px"
  },
  infoLabel: {
    fontSize: "14px",
    fontWeight: "500",
    color: "#475569",
    flex: 1
  },
  refreshIcon: {
    width: "14px",
    height: "14px",
    color: "#64748b",
    cursor: "pointer",
    transition: "color 0.2s",
    marginLeft: "8px"
  },
  infoValue: {
    fontSize: "15px",
    fontWeight: "600",
    color: "#1e293b",
    fontFamily: "'Monaco', 'Menlo', monospace"
  },
  footerInfo: {
    marginBottom: "32px",
    padding: "16px",
    backgroundColor: "#fef3c7",
    borderRadius: "8px",
    border: "1px solid #fbbf24"
  },
  disclaimerText: {
    fontSize: "14px",
    color: "#92400e",
    margin: 0,
    lineHeight: "1.5"
  },
  buttonContainer: {
    display: "flex",
    justifyContent: "center"
  },
  proceedButton: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    padding: "16px 32px",
    fontSize: "16px",
    fontWeight: "600",
    backgroundColor: "#084a94",
    color: "white",
    border: "none",
    borderRadius: "10px",
    cursor: "pointer",
    transition: "all 0.3s ease",
    boxShadow: "0 4px 12px rgba(8, 74, 148, 0.3)",
    minWidth: "200px"
  },
  buttonIcon: {
    marginLeft: "12px",
    fontSize: "14px"
  }
};

export default TimestampPage;