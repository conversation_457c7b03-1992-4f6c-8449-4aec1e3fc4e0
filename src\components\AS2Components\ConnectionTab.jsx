import React, { useState } from "react";
import { MenuItem, Select, FormControl } from "@mui/material";
import API_ENDPOINT from "../../config";

const ConnectionTab = ({ onClose, setSnackbar, artifactId }) => {
  const [connectionData, setConnectionData] = useState({
    address: { param: "", value: "" },
    authorization: { param: "", value: "" },
    authorizationType: { param: "", value: "User Role" },
    userRole: { param: "", value: "ESBMessaging.send" }
  });
  const [loading, setLoading] = useState(false);

  const handleCancel = () => {
    if (onClose) onClose();
  };

  const handleConnectionChange = (e) => {
    const { name, value } = e.target;
    setConnectionData(prev => ({
      ...prev,
      [name]: { ...prev[name], value }
    }));
  };

  const handleParamChange = (e) => {
    const { name, value } = e.target;
    setConnectionData(prev => ({
      ...prev,
      [name]: { ...prev[name], param: value }
    }));
  };

 const handleSubmit = async () => {
  setLoading(true);
  
  try {
    // Create an array of all parameter updates
    const updates = [
      { name: connectionData.address.param, value: connectionData.address.value },
      { name: connectionData.authorization.param, value: connectionData.authorization.value },
      { name: connectionData.authorizationType.param, value: connectionData.authorizationType.value },
      { name: connectionData.userRole.param, value: connectionData.userRole.value }
    ].filter(update => update.name); // Only include parameters with names

    if (updates.length === 0) {
      setSnackbar({
        open: true,
        message: "Please enter at least one parameter name",
        severity: "error"
      });
      setLoading(false);
      return;
    }

    // Execute all updates sequentially
    let lastResponse = null;
    for (const update of updates) {
      const endpoint = `${API_ENDPOINT}/api/updateParam/${artifactId}/${update.name}`;
      
      const response = await fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          parameterValue: update.value,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update parameter ${update.name}`);
      }
      
      // Store the last successful response
      lastResponse = await response.json();
    }

    // Show the success message from the API response
    if (lastResponse && lastResponse.status) {
      setSnackbar({
        open: true,
        message: lastResponse.status,
        severity: "success"
      });
    } else {
      setSnackbar({
        open: true,
        message: "Parameters updated successfully",
        severity: "success"
      });
    }
  } catch (error) {
    console.error("Error updating parameters:", error);
    setSnackbar({
      open: true,
      message: error.message || "Failed to update some parameters",
      severity: "error"
    });
  } finally {
    setLoading(false);
  }
};
  return (
    <div>
      <h4 style={{ 
        display: 'flex',
        color: '#6c757d', 
        fontSize: '14px', 
        fontWeight: 'bold',
        marginBottom: '20px',
        textTransform: 'uppercase'
      }}>
        CONNECTION DETAILS
      </h4>
      
      <div style={{ display: 'grid', gap: '20px' }}>
        <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center' }}>
          <label style={{ color: '#333', fontSize: '14px' }}>Address:</label>
          <input
            type="text"
            name="address"
            value={connectionData.address.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <input
            type="text"
            name="address"
            value={connectionData.address.value}
            onChange={handleConnectionChange}
            placeholder="Define Value"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center' }}>
          <label style={{ color: '#333', fontSize: '14px' }}>
            Authorization: <span style={{ color: 'red' }}>*</span>
          </label>
          <input
            type="text"
            name="authorization"
            value={connectionData.authorization.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <FormControl size="small">
            <Select
              name="authorization"
              value={connectionData.authorization.value}
              onChange={handleConnectionChange}
              style={{ fontSize: '14px' }}
            >
              <MenuItem value="User Role">User Role</MenuItem>
              <MenuItem value="Client Certificate">Client Certificate</MenuItem>
            </Select>
          </FormControl>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr 200px', gap: '10px', alignItems: 'center' }}>
          <label style={{ color: '#333', fontSize: '14px' }}>
            User Role: <span style={{ color: 'red' }}>*</span>
          </label>
          <input
            type="text"
            name="userRole"
            value={connectionData.userRole.param}
            onChange={handleParamChange}
            placeholder="Define Parameter"
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px'
          }}>
            <input
              type="text"
              value={connectionData.userRole.value}
              readOnly
              style={{
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontSize: '14px',
                backgroundColor: '#f8f9fa',
                flex: 1
              }}
            />
            <button style={{
              padding: '8px 16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer'
            }}>
              Select
            </button>
          </div>
        </div>
      </div>
      
      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '10px',
        padding: '15px 20px',
        borderTop: '1px solid #dee2e6',
        backgroundColor: '#f8f9fa',
        marginTop: '20px'
      }}>
        <button
          onClick={handleSubmit}
          disabled={loading}
          style={{
            padding: '8px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer',
            fontWeight: 'bold',
            opacity: loading ? 0.7 : 1
          }}
        >
          {loading ? 'Updating...' : 'OK'}
        </button>
        <button
          onClick={handleCancel}
          style={{
            padding: '8px 20px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default ConnectionTab;