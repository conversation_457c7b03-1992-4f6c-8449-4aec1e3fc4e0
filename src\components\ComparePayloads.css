.test-case-upload {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.upload-heading {
  font-weight: bold;
  margin-top: 30px;
  margin-bottom: 15px;
}

.upload-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 40px;
}

.upload-table th {
  background-color: #005b82;
  color: white;
  padding: 10px;
  text-align: center;
  font-weight: bold;
}

.upload-table td {
  background-color: #d4dce3;
  text-align: center;
  padding: 15px;
}

.upload-button {
  padding: 8px 16px;
  background-color: #e0e0e0;
  border: none;
  font-weight: bold;
  cursor: pointer;
}

.upload-button:hover {
  background-color: #c5c5c5;
}

.run-section {
  text-align: center;
  margin-top: 20px;
}

.run-button {
  background-color: #007e33;
  color: white;
  padding: 10px 25px;
  border: none;
  font-size: 16px;
  font-weight: bold;
  border-radius: 5px;
  cursor: pointer;
}

.run-button:hover {
  background-color: #005b24;
}

.footer {
  margin-top: 40px;
  text-align: center;
  font-size: 13px;
}

.footer a {
  text-decoration: none;
  color: #000;
}

.footer .intswitch {
  color: red;
  font-weight: bold;
}

.upload-button input[type="file"] {
  display: none;
}

.upload-button {
  background-color: #e0e0e0;
  padding: 8px 16px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  display: inline-block;
}

.upload-button:hover {
  background-color: #c5c5c5;
}

.file-name {
  font-size: 12px;
  color: #333;
  margin-top: 5px;
}








/* ComparePayloads.css - Add these styles to your existing CSS file */

.download-button {
  background-color: #12173d;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 160px;
}

.download-button:hover {
  background-color: lightskyblue;
}

.download-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.download-button:disabled:hover {
  background-color: #cccccc;
}

/* Add a spinner to the button when generating */
.download-button.generating {
  position: relative;
}

.download-button.generating::before {
  content: "";
  position: absolute;
  left: 10px;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Additional styles for PDF report indicators */
.report-status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-pass {
  background-color: #4CAF50;
}

.status-fail {
  background-color: #F44336;
}

/* Report generation message */
.report-generation-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #333;
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  z-index: 1000;
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

/* ComparePayloads.css - Professional Business Styling */

.compare-payloads-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.compare-payloads-content {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-description {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Upload Section */
.upload-section {
  padding: 3rem 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
}

.section-description {
  font-size: 1rem;
  color: #718096;
  margin: 0;
  line-height: 1.5;
}

/* Upload Grid */
.upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

/* File Upload Cards */
.file-upload-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.file-upload-card:hover {
  border-color: #667eea;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.file-upload-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.file-upload-icon {
  width: 24px;
  height: 24px;
  color: #667eea;
  margin-right: 0.75rem;
}

.file-upload-icon svg {
  width: 100%;
  height: 100%;
}

.file-upload-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

/* File Upload Area */
.file-upload-area {
  display: block;
  cursor: pointer;
  border: 2px dashed #cbd5e0;
  border-radius: 8px;
  padding: 2rem 1rem;
  text-align: center;
  transition: all 0.3s ease;
  background: #f8fafc;
}

.file-upload-area:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.file-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.upload-icon {
  width: 48px;
  height: 48px;
  color: #a0aec0;
  margin-bottom: 0.5rem;
}

.upload-text {
  font-size: 1rem;
  font-weight: 500;
  color: #4a5568;
}

.upload-subtext {
  font-size: 0.875rem;
  color: #718096;
}

/* File Selected State */
.file-selected {
  margin-top: 1rem;
  padding: 1rem;
  background: #f0fff4;
  border: 1px solid #9ae6b4;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-icon {
  width: 20px;
  height: 20px;
  color: #38a169;
}

.file-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #2d3748;
  word-break: break-all;
}

.file-size {
  font-size: 0.75rem;
  color: #718096;
  font-weight: 500;
}

/* Action Section */
.action-section {
  /* display: flex;
  justify-content: center;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0; */
}

.run-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  box-shadow: 0 4px 14px rgba(102, 126, 234, 0.3);
}

.run-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.run-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.button-icon {
  width: 20px;
  height: 20px;
}

.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .compare-payloads-container {
    padding: 1rem;
  }
  
  .page-header {
    padding: 2rem 1rem;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .upload-section {
    padding: 1rem 1rem;
  }
  
  .upload-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .file-upload-card {
    padding: 1rem;
  }
  
  .file-upload-area {
    padding: 1.5rem 1rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
  }
  
  .run-button {
    width: 100%;
    padding: 1rem;
  }
  
  .file-selected {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
}

/* Legacy table styles for backward compatibility */
.test-case-upload {
  /* Legacy container styles */
}

.upload-heading {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 2rem 0 1rem 0;
  text-align: center;
}

.upload-table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.upload-table th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
}

.upload-table td {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: top;
}

.upload-table tr:hover {
  background: #f8fafc;
}

.upload-button {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  margin-bottom: 0.5rem;
}

.upload-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.run-section {
  text-align: center;
  margin: 2rem 0;
}

/* Animation for smooth transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.file-upload-card {
  animation: fadeIn 0.5s ease-out;
}

.file-upload-card:nth-child(1) {
  animation-delay: 0.1s;
}

.file-upload-card:nth-child(2) {
  animation-delay: 0.2s;
}

.file-upload-card:nth-child(3) {
  animation-delay: 0.3s;
}