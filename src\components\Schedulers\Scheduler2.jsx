import React, { useState, useEffect } from 'react';

const Scheduler2 = ({ onCronExpressionUpdate }) => {
  const [startDate, setStartDate] = useState('');
  const [startTimeInternal, setStartTimeInternal] = useState('00:00');
  const [endTime, setEndTime] = useState('24:00');
  const [intervalValue, setIntervalValue] = useState('1m');
  const [cronExpression, setCronExpression] = useState('');

  const handleTimeChange = (event) => {
    const newTime = event.target.value;
    if (newTime !== startTimeInternal) {
      setStartTimeInternal(newTime);
    }
  };
  

  const handleEndTimeChange = (event) => {
    setEndTime(event.target.value);
  };

  const handleIntervalChange = (event) => {
    setIntervalValue(event.target.value);
  };

  const generateCronExpression = () => {
    if (startDate) {
      const [intervalNum, intervalUnit] = intervalValue.match(/\d+|\D+/g);
      const startHour = parseInt(startTimeInternal.slice(0, 2));
      const endHour = parseInt(endTime.slice(0, 2)) - 1; // Exclude the end hour from the range
      const [year, month, day] = startDate.split('-');

      // Format the cron expression with dynamic date and remove leading zeros
      const newCronExpression = `0 0/${intervalNum} ${startHour.toString().replace(/^0+/, '')}-${endHour.toString().replace(/^0+/, '')} ${parseInt(day).toString().replace(/^0+/, '')} ${parseInt(month).toString().replace(/^0+/, '')} ? ${year} --tz=IST`;

      return newCronExpression;
    }

    return '';
  };

  useEffect(() => {
    const generatedCronExpression = generateCronExpression();
    if (cronExpression !== generatedCronExpression) { // Prevent redundant updates
      setCronExpression(generatedCronExpression);
      onCronExpressionUpdate(generatedCronExpression);
    }
  }, [startDate, startTimeInternal, endTime, intervalValue]); // Ensure these are stable
  

  return (
    <div style={{ margin: '20px 0', fontFamily: 'Arial, sans-serif'}}>
      <div style={{ marginBottom: '20px' }}>
        <h3></h3>
        <div style={{ display: 'flex', alignItems: 'flex-start', flexDirection: 'column', gap: '2px' }}>
        <label htmlFor="startDateInput">On Date:</label>
        <input
          type="date"
          value={startDate}
          onChange={(event) => setStartDate(event.target.value)}
          id="startDateInput"
          className="form-control"
        />
        <label htmlFor="startTimeInput">Between:</label>
        <select
          value={startTimeInternal}
          onChange={handleTimeChange}
          id="startTimeInput"
          className="form-control"
        >
          {[...Array(24).keys()].map((hour) => (
            <option key={hour} value={`${hour.toString().padStart(2, '0')}:00`}>
              {hour.toString().padStart(2, '0')}:00
            </option>
          ))}
        </select>
        <label htmlFor="endTimeInput">and:</label>
        <select
          value={endTime}
          onChange={handleEndTimeChange}
          id="endTimeInput"
          className="form-control"
        >
          {[...Array(24).keys()].map((hour) => (
            <option key={hour} value={`${(hour + 1).toString().padStart(2, '0')}:00`}>
              {(hour + 1).toString().padStart(2, '0')}:00
            </option>
          ))}
        </select>
        <label htmlFor="intervalInput">On Every:</label>
        <select
          value={intervalValue}
          onChange={handleIntervalChange}
          id="intervalInput"
          className="form-control"
        >
          <option value="1m">1 min</option>
          <option value="5m">5 min</option>
          <option value="10m">10 min</option>
          <option value="15m">15 min</option>
          <option value="20m">20 min</option>
          <option value="30m">30 min</option>
          {/* <option value="1h">1 hour</option>
          <option value="2h">2 hours</option>
          <option value="3h">3 hours</option>
          <option value="4h">4 hours</option>
          <option value="6h">6 hours</option>
          <option value="8h">8 hours</option>
          <option value="12h">12 hours</option> */}
        </select>
      </div>

      {/* <div>
        <strong>Cron Expression:</strong> {cronExpression}
      </div> */}
    </div>
    </div>
  );
};

export default Scheduler2;