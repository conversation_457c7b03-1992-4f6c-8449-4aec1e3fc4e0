import React, { useState } from "react";
import CircularProgress from "@mui/material/CircularProgress";
import ResultsTable from "./ResultsTable";
import ComparisonDetailsModal from "./ComparisonDetailsModal";
import PayloadModal from "./PayloadModal";
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  pdf,
} from "@react-pdf/renderer";
import { saveAs } from "file-saver";
import "../App.css";
import APIEndpoint from "../config";
import "../components/ComparisonDataForCompare.css";
import ComparisonPayloadTable from "./ComparisonPayloadTable";
import ChatIcon from '@mui/icons-material/Chat';
import ChatbotMain from "./Chatbot/ChatbotMain";
const styles = StyleSheet.create({
  page: {
    padding: 30,
    backgroundColor: "#FFFFFF",
  },
  section: {
    marginBottom: 15,
    padding: 10,
    backgroundColor: "#FAFAFA",
    borderRadius: 5,
    borderLeftWidth: 3,
    borderLeftColor: "#003366",
  },
  boldText: {
    fontWeight: "bold",
  },
  header: {
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#CCCCCC",
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#003366",
  },
  subtitle: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 5,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
    marginTop: 15,
    color: "#003366",
  },
  statusPass: {
    color: "#008800",
  },
  statusFail: {
    color: "#CC0000",
  },
  tableHeader: {
    backgroundColor: "#EEEEEE",
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#CCCCCC",
    padding: 5,
  },
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
    padding: 5,
  },
  text: {
    fontSize: 10,
    paddingRight: 5,
  },
  footer: {
    position: "absolute",
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: "center",
    color: "#666666",
    fontSize: 10,
    borderTopWidth: 1,
    borderTopColor: "#CCCCCC",
    paddingTop: 10,
  },
  summary: {
    marginTop: 15,
    marginBottom: 15,
    padding: 10,
    backgroundColor: "#F9F9F9",
    borderRadius: 5,
  },
  summaryText: {
    fontSize: 12,
    marginBottom: 5,
  },
});

const ComparisonReport = ({ reportData }) => {
  if (!reportData || !Array.isArray(reportData)) {
    return (
      <Document>
        <Page size="A4" style={styles.page}>
          <Text>No data available</Text>
        </Page>
      </Document>
    );
  }

  const matches = reportData.filter((item) => item.match === true).length;

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header}>
          <Text style={styles.title}>Comparison Report</Text>
          <Text style={styles.subtitle}>
            Generated on: {new Date().toLocaleString()}
          </Text>
        </View>

        <View style={styles.summary}>
          <Text style={[styles.summaryText, styles.boldText]}>
            Summary Statistics
          </Text>
          <Text style={styles.summaryText}>
            Total Comparisons: {reportData.length}
          </Text>
          <Text style={styles.summaryText}>
            Matches: {matches} | Differences: {reportData.length - matches}
          </Text>
          <Text style={styles.summaryText}>
            Match Rate:{" "}
            {reportData.length > 0
              ? Math.round((matches / reportData.length) * 100)
              : 0}
            %
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Comparison Results Overview</Text>
          <View style={styles.tableHeader}>
            <Text style={[styles.boldText, { width: "25%" }]}>File Name</Text>
            <Text style={[styles.boldText, { width: "15%" }]}>Match</Text>
            <Text style={[styles.boldText, { width: "15%" }]}>Differences</Text>
            <Text style={[styles.boldText, { width: "15%" }]}>Time (ms)</Text>
            <Text style={[styles.boldText, { width: "15%" }]}>Messages</Text>
          </View>
          {reportData.map((comparison, index) => (
            <View key={`summary-${index}`} style={styles.tableRow}>
              <Text style={[styles.text, { width: "25%", fontSize: 8 }]}>
                {comparison.fileName || "N/A"}
              </Text>

              <Text
                style={[
                  styles.text,
                  { width: "15%" },
                  comparison.match ? styles.statusPass : styles.statusFail,
                ]}
              >
                {comparison.match ? "MATCH" : "DIFFERENCES"}
              </Text>
              <Text style={[styles.text, { width: "15%" }]}>
                {comparison.differences ? comparison.differences.length : "0"}
              </Text>
              <Text style={[styles.text, { width: "15%" }]}>
                {comparison.comparisonTimeMs || "N/A"}
              </Text>
              <Text style={[styles.text, { width: "15%", fontSize: 8 }]}>
                {comparison.messages || "N/A"}
              </Text>
            </View>
          ))}
        </View>

        {/* Detailed Differences Section */}
        {reportData.some(
          (item) => item.differences && item.differences.length > 0
        ) && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Detailed Differences</Text>
            {reportData.map(
              (comparison, index) =>
                comparison.differences &&
                comparison.differences.length > 0 && (
                  <View key={`details-${index}`} style={{ marginBottom: 15 }}>
                    <Text
                      style={[
                        styles.boldText,
                        { fontSize: 12, marginBottom: 5 },
                      ]}
                    >
                      {comparison.fileName}
                    </Text>
                    {comparison.differences.map((diff, diffIndex) => (
                      <View
                        key={`diff-${index}-${diffIndex}`}
                        style={{ marginBottom: 5, paddingLeft: 10 }}
                      >
                        <Text style={[styles.text, { fontSize: 9 }]}>
                          Type: {diff.differenceType}
                        </Text>
                        <Text style={[styles.text, { fontSize: 9 }]}>
                          XPath: {diff.xpath || "Root"}
                        </Text>
                        <Text style={[styles.text, { fontSize: 9 }]}>
                          Expected: {diff.expectedValue}
                        </Text>
                        <Text style={[styles.text, { fontSize: 9 }]}>
                          Actual: {diff.actualValue}
                        </Text>
                        <Text
                          style={[styles.text, { fontSize: 8, color: "#666" }]}
                        >
                          {diff.description}
                        </Text>
                      </View>
                    ))}
                  </View>
                )
            )}
          </View>
        )}

        <View style={styles.footer}>
          <Text>Testing Tool - Comparison Report</Text>
          <Text>© {new Date().getFullYear()}</Text>
        </View>
      </Page>
    </Document>
  );
};

// Simple table component for displaying results
// const SimpleResultsTable = ({
//   comparisons,
//   onRowClick,
//   onViewPayload,
//   hoveredRow,
//   setHoveredRow,
// }) => {
//   return (
//     <div className="results-table-container">
//       <table className="results-table">
//         <thead>
//           <tr>
//             <th>File Name</th>

//             <th>Match</th>
//             <th>Differences</th>
//             <th>Time (ms)</th>
//             <th>Input File</th>
//             <th>PO Output</th>
//             <th>CPI Output</th>
//             <th>Actions</th>
//           </tr>
//         </thead>
//         <tbody>
//           {comparisons.map((comparison, index) => (
//             <tr
//               key={index}
//               className={`results-row ${hoveredRow === index ? "hovered" : ""}`}
//               onMouseEnter={() => setHoveredRow(index)}
//               onMouseLeave={() => setHoveredRow(null)}
//             >
//               <td>{comparison.fileName}</td>

//               <td>
//                 <span
//                   className={`match-badge ${
//                     comparison.match ? "match" : "no-match"
//                   }`}
//                 >
//                   {comparison.match ? "MATCH" : "DIFFERENCES"}
//                 </span>
//               </td>
//               <td>
//                 <span className="differences-count">
//                   {comparison.differences ? comparison.differences.length : 0}
//                 </span>
//               </td>
//               <td>{comparison.comparisonTimeMs}ms</td>
//               <td>
//                 {comparison.poInputPayload && (
//                   <button
//                     className="payload-btn input-btn"
//                     onClick={() =>
//                       onViewPayload(comparison.poInputPayload, "Input")
//                     }
//                   >
//                     View Input Payload
//                   </button>
//                 )}
//               </td>
//               <td>
//                 {comparison.poOutputPayload && (
//                   <button
//                     className="payload-btn po-btn"
//                     onClick={() =>
//                       onViewPayload(comparison.poOutputPayload, "PO Output")
//                     }
//                   >
//                     View PO Output
//                   </button>
//                 )}
//               </td>
//               <td>
//                 {comparison.cpiPayload && (
//                   <button
//                     className="payload-btn cpi-btn"
//                     onClick={() =>
//                       onViewPayload(comparison.cpiPayload, "CPI Output")
//                     }
//                   >
//                     View CPI Output
//                   </button>
//                 )}
//               </td>
//               <td>
//                 <div className="action-buttons">
//                   {comparison.differences &&
//                     comparison.differences.length > 0 && (
//                       <button
//                         className="view-details-btn"
//                         onClick={() => onRowClick(comparison)}
//                       >
//                         View Details
//                       </button>
//                     )}
//                 </div>
//               </td>
//             </tr>
//           ))}
//         </tbody>
//       </table>
//     </div>
//   );
// };

const ComparisonDataForCompare = ({
  comparisonResult,
  onBack,
  isSingleTestCase,
}) => {
  const [selectedComparison, setSelectedComparison] = useState(null);
  const [payloadContent, setPayloadContent] = useState(null);
  const [payloadType, setPayloadType] = useState("");
  const [isPayloadModalOpen, setIsPayloadModalOpen] = useState(false);
  const [isLoadingPayload, setIsLoadingPayload] = useState(false);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [chatbotOpen, setChatbotOpen] = useState(false);
  const handleViewPayload = async (payloadUrl, type) => {
    setIsLoadingPayload(true);
    setIsPayloadModalOpen(true);
    setPayloadType(type);

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${APIEndpoint}${payloadUrl}`, {
        headers: {
          "Authorization": `Basic ${localStorage.getItem("basicAuth")}`
        },
      });

      if (!response.ok)
        throw new Error(`Failed to fetch payload: ${response.status}`);
      const content = await response.text();
      setPayloadContent(content);
    } catch (error) {
      console.error("Error fetching payload:", error);
      setPayloadContent(`Error loading payload: ${error.message}`);
    } finally {
      setIsLoadingPayload(false);
    }
  };

  const handleDownloadReport = async () => {
    try {
      setIsGeneratingReport(true);
      if (!comparisonResult || !Array.isArray(comparisonResult)) {
        throw new Error("Invalid comparison data");
      }

      const pdfBlob = await pdf(
        <ComparisonReport reportData={comparisonResult} />
      ).toBlob();

      saveAs(
        pdfBlob,
        `comparison-report-${new Date().toISOString().slice(0, 10)}.pdf`
      );
    } catch (error) {
      console.error("Error generating PDF report:", error);
      alert("Failed to generate the report. Please try again.");
    } finally {
      setIsGeneratingReport(false);
    }
  };

  // Ensure comparisonResult is an array
  const comparisons = Array.isArray(comparisonResult) ? comparisonResult : [];

  return (
    <div className="comparison-results">
      {comparisons.length > 0 ? (
        <div className="results-container">
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "20px",
            }}
          >
            <h2>Comparison Results</h2>
            <button
              onClick={handleDownloadReport}
              className="download-button"
              disabled={isGeneratingReport}
            >
              {isGeneratingReport ? "Generating Report..." : "Download Report"}
            </button>
          </div>

          {/* Summary Statistics */}
          <div className="summary-stats">
            <div className="stat-item">
              <span className="stat-label">Total Comparisons:</span>
              <span className="stat-value">{comparisons.length}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Matches:</span>
              <span className="stat-value match">
                {comparisons.filter((c) => c.match).length}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Differences:</span>
              <span className="stat-value no-match">
                {comparisons.filter((c) => !c.match).length}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Success Rate:</span>
              <span className="stat-value">
                {comparisons.length > 0
                  ? Math.round(
                      (comparisons.filter((c) => c.match).length /
                        comparisons.length) *
                        100
                    )
                  : 0}
                %
              </span>
            </div>
          </div>

          <ComparisonPayloadTable
            comparisons={comparisons}
            onRowClick={setSelectedComparison}
            onViewPayload={handleViewPayload}
            hoveredRow={hoveredRow}
            setHoveredRow={setHoveredRow}
          />

          <ComparisonDetailsModal
            selectedComparison={selectedComparison}
            onClose={() => setSelectedComparison(null)}
          />

          <PayloadModal
            isOpen={isPayloadModalOpen}
            payloadType={payloadType}
            payloadContent={payloadContent}
            isLoading={isLoadingPayload}
            onClose={() => {
              setIsPayloadModalOpen(false);
              setPayloadContent(null);
            }}
          />


          <button 
          className="chatbot-toggle-button"
          onClick={() => setChatbotOpen(!chatbotOpen)}
          style={{
            position: 'fixed',
            bottom: '6rem',
            right: '20px',
            zIndex: 1000,
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '50%',
            width: '60px',
            height: '60px',
            cursor: 'pointer',
            boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <ChatIcon style={{ fontSize: '30px' }} />
        </button>

        {/* Chatbot component */}
        {chatbotOpen && (
          <div style={{
            position: 'fixed',
            bottom: '11rem',
            right: '20px',
            zIndex: 999,
            width: '350px',
            height: '500px',
            backgroundColor: 'white',
            borderRadius: '10px',
            boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
            overflow: 'hidden'
          }}>
            <ChatbotMain />
          </div>
        )}

        </div>
      ) : (
        <div className="loading-message">
          <CircularProgress />
          <p>Loading comparison results...</p>
        </div>
      )}

      <button onClick={onBack} className="back-button">
        ← Back to Form
      </button>
    </div>
  );
};

export default ComparisonDataForCompare;
