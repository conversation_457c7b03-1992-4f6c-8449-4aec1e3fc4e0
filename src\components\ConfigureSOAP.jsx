import React from 'react'
// import HTTPConfigurationParam from './HTTPConfigurationParam';
import SOAPConfigurationParam from './SOAPConfigurationParam';

const ConfigureSOAP = ({ isOpen, onClose, iFlowName }) => {
  if (!isOpen) return null;

  return (
    <div className="scheduler-modal-overlay">
      <div className="scheduler-modal-content">
        <div className="scheduler-modal-header">
          <h3>Configure SOAP for {iFlowName}</h3>
        </div>
        <div className="scheduler-modal-body">
          <SOAPConfigurationParam artifactId={iFlowName} onClose={onClose} />
        </div>
      </div>
    </div>
  );
}

export default ConfigureSOAP