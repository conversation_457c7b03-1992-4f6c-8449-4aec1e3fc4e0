{"name": "intswitch-Test-Automation-tool", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.3.1", "@mui/x-data-grid": "^8.10.0", "@react-pdf/renderer": "^4.3.0", "@tailwindcss/vite": "^4.1.12", "@ui5/webcomponents-react": "^2.10.1", "ace-builds": "^1.41.0", "axios": "^1.11.0", "date-fns": "^4.1.0", "diff": "^8.0.1", "diff2html": "^3.4.51", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.540.0", "moment": "^2.30.1", "react": "^19.1.0", "react-ace": "^14.0.1", "react-datepicker": "^8.3.0", "react-datetime": "^3.3.1", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "react-select": "^5.10.1", "react-time-picker": "^7.0.0", "tailwindcss": "^4.1.12", "xml-formatter": "^3.6.6"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}