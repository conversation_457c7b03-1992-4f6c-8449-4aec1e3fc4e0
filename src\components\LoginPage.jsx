import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>er,
  TextField,
  Button,
  Box,
  Typography,
  Alert,
  IconButton,
  InputAdornment,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import logo from "../assets/logo.png";
import Footer from "./Footer";
import LogoOfficial from "../assets/LogoOfficial.png";
import APIEndpoint from "../config";
const LoginPage = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async () => {
    setIsLoading(true);
    setError("");

    try {
      const response = await axios.post(
        `${APIEndpoint}/api/auth/login`,
        { username, password },
        { headers: { "Content-Type": "application/json" } }
      );

      console.log("Raw response:", response); // Debug log

      // Handle string response
      if (
        typeof response.data === "string" &&
        response.data.includes("Login successful")
      ) {
        // Success case
        const basicAuth = btoa(`${username}:${password}`);
        console.log("Created Basic Auth:", basicAuth);
        
        // ✅ Set the auth header
        axios.defaults.headers.common["Authorization"] = `Basic ${basicAuth}`;
        
        // ✅ Verify it was set
        console.log("Auth header set to:", axios.defaults.headers.common["Authorization"]);
        
        // ✅ Store for persistence
        localStorage.setItem('basicAuth', basicAuth);

        navigate("/dashboard");
      } else {
        setError("Invalid username or password");
      }
    } catch (error) {
      console.error("Login error:", error);
      setError(
        typeof error.response?.data === "string"
          ? error.response.data
          : "Login failed. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <>
      <div>
        <div>
          <AppBar position="fixed" sx={{ backgroundColor: "white" }}>
            <Toolbar style={{ justifyContent: "space-between" }}>
              <img src={logo} alt="Company Logo" style={{ height: 30 }} />
              <div
                style={{ display: "flex", alignItems: "center", gap: "1rem" }}
              >
                <img
                  src={LogoOfficial}
                  alt="Small Logo"
                  style={{ height: 30 }}
                />
              </div>
            </Toolbar>
          </AppBar>
        </div>
        
        <div style={{marginTop:"6rem"}}></div>
          <Box
            sx={{

              flexGrow: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mt: 8,
            }}
          >
            <Container maxWidth="xs">
              <Box
                sx={{
                  p: 4,
                  borderRadius: 2,
                  boxShadow: 3,
                  bgcolor: "background.paper",
                  textAlign: "center",
                  width: "100%",
                }}
              >
                <img
                  src={logo}
                  alt="Company Logo"
                  style={{ height: 40, marginBottom: 10 }}
                />

                <Typography
                  variant="h4"
                  gutterBottom
                  sx={{ fontWeight: "bold" }}
                >
                  Test Automation Tool
                </Typography>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  align="center"
                  style={{ margin: "1rem 0" }}
                >
                  Enter your Credentials below
                </Typography>

                {error && <Alert severity="error">{error}</Alert>}

                <TextField
                  label="Email*"
                  fullWidth
                  margin="normal"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                />

                <TextField
                  label="Password*"
                  type={showPassword ? "text" : "password"}
                  fullWidth
                  margin="normal"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  sx={{ mt: 2 }}
                  onClick={handleLogin}
                  disabled={isLoading}
                >
                  {isLoading ? "Logging in..." : "Login"}
                </Button>
              </Box>
            </Container>
          </Box>
       
        <div>
          <Footer />
        </div>
      </div>
    </>
  );
};

export default LoginPage;
