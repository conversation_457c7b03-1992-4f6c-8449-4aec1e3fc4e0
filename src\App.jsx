import "./App.css";
import LoginPage from "./components/LoginPage";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Dashboard from "./components/Dashboard";
import POTesting from "./components/POTesting";
import ExtractPayloads from "./components/ExtractPayloads";
import BoomiLoginPage from "./components/BoomiLoginPage";
import MulesoftTest from "./components/MulesoftTest";
import NeoTest from "./components/Neo/NeoTest";
import RegisterUser from "./components/RegisterUser";
import ComparePayloads from "./components/ComparePayloads";
import Homepage from "./components/Homepage";
import POLoginPage from "./components/POLoginPage";
import TimestampPage from "./components/TimestampPage";
import InterfacePage from "./components/InterfacePage";
import NeoSingletest from "./components/Neo/NeoSingletest";
import GroovyComponent from "./components/GroovyComponent";
import MMAPfile from "./components/MMAPfile";
import GroovyConvertor from "./components/GroovyConvertor";
// import SingleTestCaseForm from "./components/SingleTestCaseForm";

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LoginPage />} />
        <Route path="/dashboard" element={<Dashboard />}>
          <Route index element={<Homepage />} />
          <Route path="register" element={<RegisterUser />} />
          <Route path="extract" element={<ExtractPayloads />} />
          <Route path="po-login" element={<POLoginPage/>} />
          <Route path="timestamp" element={<TimestampPage/>} />
          <Route path="interface" element={<InterfacePage/>} />
          <Route path="compare" element={<ComparePayloads />} />
          <Route path="groovy" element={<GroovyComponent />} />
          <Route path="convertor" element={<GroovyConvertor />} />
           <Route path="mmap" element={<MMAPfile />} />
          <Route path="potesting" element={<POTesting />} />
          {/* <Route path="potesting/details" element={<SingleTestCaseForm />} /> */}
          <Route path="boomi" element={<BoomiLoginPage />} />
          <Route path="mulesoft" element={<MulesoftTest />} />
          <Route path="neo" element={<NeoTest />} />
          <Route path="/dashboard/neo/neoselection" element={<NeoSingletest />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;